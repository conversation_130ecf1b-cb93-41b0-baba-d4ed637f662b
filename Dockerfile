# Metamorphic Flux - Multi-stage Docker build
# This Dockerfile builds both the web app and agents service

# Base image with Node.js and pnpm
FROM node:20-alpine AS base
RUN npm install -g pnpm
WORKDIR /app

# Install dependencies
FROM base AS deps
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/web/package.json ./apps/web/
COPY apps/agents/package.json ./apps/agents/
RUN pnpm install --frozen-lockfile

# Build web application
FROM base AS web-builder
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/web/node_modules ./apps/web/node_modules
COPY . .

# Build the web app
WORKDIR /app/apps/web
RUN pnpm build

# Build agents service
FROM base AS agents-builder
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/apps/agents/node_modules ./apps/agents/node_modules
COPY . .

# Build the agents service
WORKDIR /app/apps/agents
RUN pnpm build

# Production web app image
FROM node:20-alpine AS web-runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=web-builder /app/apps/web/public ./public
COPY --from=web-builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./
COPY --from=web-builder --chown=nextjs:nodejs /app/apps/web/.next/static ./.next/static

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]

# Production agents service image
FROM node:20-alpine AS agents-runner
WORKDIR /app

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 agents

# Install production dependencies
COPY --from=deps /app/apps/agents/node_modules ./node_modules
COPY --from=agents-builder /app/apps/agents/dist ./dist
COPY --from=agents-builder /app/apps/agents/package.json ./

USER agents

EXPOSE 3001
ENV PORT 3001
ENV NODE_ENV production

CMD ["node", "dist/index.js"]

# Development image (for local development)
FROM base AS development
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Install development dependencies
RUN pnpm install

EXPOSE 3000 3001
CMD ["pnpm", "dev"]
