"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+realtime-js@2.11.15";
exports.ids = ["vendor-chunks/@supabase+realtime-js@2.11.15"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* binding */ REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* binding */ REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* binding */ REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* binding */ REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   \"default\": () => (/* binding */ RealtimeChannel)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_push__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/push */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n\n\n\n\n\n\nvar REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nvar REALTIME_LISTEN_TYPES;\n(function (REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nvar REALTIME_SUBSCRIBE_STATES;\n(function (REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nconst REALTIME_CHANNEL_STATES = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */\nclass RealtimeChannel {\n    constructor(\n    /** Topic name can be any string. */\n    topic, params = { config: {} }, socket) {\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, '');\n        this.params.config = Object.assign({\n            broadcast: { ack: false, self: false },\n            presence: { key: '' },\n            private: false,\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](() => this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive('ok', () => {\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent) => pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(() => {\n            this.rejoinTimer.reset();\n            this.socket.log('channel', `close ${this.topic} ${this._joinRef()}`);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason) => {\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log('channel', `error ${this.topic}`, reason);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive('timeout', () => {\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log('channel', `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.reply, {}, (payload, ref) => {\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__[\"default\"](this);\n        this.broadcastEndpointURL =\n            (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_4__.httpEndpointURL)(this.socket.endPoint) + '/api/broadcast';\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */\n    subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.state == _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed) {\n            const { config: { broadcast, presence, private: isPrivate }, } = this.params;\n            this._onError((e) => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(() => callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r) => r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate,\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({ config }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush\n                .receive('ok', async ({ postgres_changes }) => {\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n                else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for (let i = 0; i < bindingsLen; i++) {\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter }, } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter &&\n                            serverPostgresFilter.event === event &&\n                            serverPostgresFilter.schema === schema &&\n                            serverPostgresFilter.table === table &&\n                            serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), { id: serverPostgresFilter.id }));\n                        }\n                        else {\n                            this.unsubscribe();\n                            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error('mismatch between server and client bindings for postgres changes'));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            })\n                .receive('error', (error) => {\n                this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(', ') || 'error')));\n                return;\n            })\n                .receive('timeout', () => {\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'track',\n            payload,\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: 'presence',\n            event: 'untrack',\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */\n    async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === 'broadcast') {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue\n                ? `Bearer ${this.socket.accessTokenValue}`\n                : '';\n            const options = {\n                method: 'POST',\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : '',\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private,\n                        },\n                    ],\n                }),\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? 'ok' : 'error';\n            }\n            catch (error) {\n                if (error.name === 'AbortError') {\n                    return 'timed out';\n                }\n                else {\n                    return 'error';\n                }\n            }\n        }\n        else {\n            return new Promise((resolve) => {\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === 'broadcast' && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve('ok');\n                }\n                push.receive('ok', () => resolve('ok'));\n                push.receive('error', () => resolve('error'));\n                push.receive('timeout', () => resolve('timed out'));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */\n    unsubscribe(timeout = this.timeout) {\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n        const onClose = () => {\n            this.socket.log('channel', `leave ${this.topic}`);\n            this._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, 'leave', this._joinRef());\n        };\n        this.joinPush.destroy();\n        let leavePush = null;\n        return new Promise((resolve) => {\n            leavePush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush\n                .receive('ok', () => {\n                onClose();\n                resolve('ok');\n            })\n                .receive('timeout', () => {\n                onClose();\n                resolve('timed out');\n            })\n                .receive('error', () => {\n                resolve('error');\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger('ok', {});\n            }\n        }).finally(() => {\n            leavePush === null || leavePush === void 0 ? void 0 : leavePush.destroy();\n        });\n    }\n    /**\n     * Teardown the channel.\n     *\n     * Destroys and stops related timers.\n     */\n    teardown() {\n        this.pushBuffer.forEach((push) => push.destroy());\n        this.rejoinTimer && clearTimeout(this.rejoinTimer.timer);\n        this.joinPush.destroy();\n    }\n    /** @internal */\n    async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(() => controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), { signal: controller.signal }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */\n    _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        }\n        else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */\n    _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */\n    _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */\n    _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */\n    _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS;\n        const events = [close, error, leave, join];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw 'channel onMessage callbacks must return the payload, modified or unmodified';\n        }\n        if (['insert', 'update', 'delete'].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind) => {\n                var _a, _b, _c;\n                return (((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === '*' ||\n                    ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower);\n            }).map((bind) => bind.callback(handledPayload, ref));\n        }\n        else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind) => {\n                var _a, _b, _c, _d, _e, _f;\n                if (['broadcast', 'presence', 'postgres_changes'].includes(typeLower)) {\n                    if ('id' in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return (bindId &&\n                            ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) &&\n                            (bindEvent === '*' ||\n                                (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) ===\n                                    ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase())));\n                    }\n                    else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return (bindEvent === '*' ||\n                            bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase()));\n                    }\n                }\n                else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind) => {\n                if (typeof handledPayload === 'object' && 'ids' in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors,\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */\n    _isClosed() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n    }\n    /** @internal */\n    _isJoined() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n    }\n    /** @internal */\n    _isJoining() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n    }\n    /** @internal */\n    _isLeaving() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n    }\n    /** @internal */\n    _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */\n    _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback,\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        }\n        else {\n            this.bindings[typeLower] = [binding];\n        }\n        return this;\n    }\n    /** @internal */\n    _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind) => {\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower &&\n                RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */\n    static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for (const k in obj1) {\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */\n    _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */\n    _onClose(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */\n    _onError(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error, {}, (reason) => callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */\n    _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */\n    _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */\n    _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {},\n        };\n        if (payload.type === 'INSERT' || payload.type === 'UPDATE') {\n            records.new = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === 'UPDATE' || payload.type === 'DELETE') {\n            records.old = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n}\n//# sourceMappingURL=RealtimeChannel.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeClient)\n/* harmony export */ });\n/* harmony import */ var isows__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! isows */ \"(ssr)/../../node_modules/.pnpm/isows@1.0.7_ws@8.18.3/node_modules/isows/_esm/index.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/serializer */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n\n\n\n\n\n\nconst noop = () => { };\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nclass RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket. This can be a custom implementation\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers Deprecated: headers cannot be set on websocket connections and this option will be removed in the future.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.logLevel Sets the log level for Realtime\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */\n    constructor(endPoint, options) {\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = new Array();\n        this.endPoint = '';\n        this.httpEndpoint = '';\n        /** @deprecated headers cannot be set on websocket connections */\n        this.headers = {};\n        this.params = {};\n        this.timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 25000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.heartbeatCallback = noop;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new _lib_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: [],\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */\n        this._resolveFetch = (customFetch) => {\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            }\n            else if (typeof fetch === 'undefined') {\n                _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n            }\n            else {\n                _fetch = fetch;\n            }\n            return (...args) => _fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${_lib_constants__WEBPACK_IMPORTED_MODULE_0__.TRANSPORTS.websocket}`;\n        this.httpEndpoint = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_3__.httpEndpointURL)(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        }\n        else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params)\n            this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.timeout)\n            this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger)\n            this.logger = options.logger;\n        if ((options === null || options === void 0 ? void 0 : options.logLevel) || (options === null || options === void 0 ? void 0 : options.log_level)) {\n            this.logLevel = options.logLevel || options.log_level;\n            this.params = Object.assign(Object.assign({}, this.params), { log_level: this.logLevel });\n        }\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs)\n            this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs)\n            ? options.reconnectAfterMs\n            : (tries) => {\n                return [1000, 2000, 5000, 10000][tries - 1] || 10000;\n            };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode)\n            ? options.encode\n            : (payload, callback) => {\n                return callback(JSON.stringify(payload));\n            };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode)\n            ? options.decode\n            : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](async () => {\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (typeof window !== 'undefined' && !window.Worker) {\n                throw new Error('Web Worker is not supported');\n            }\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */\n    connect() {\n        if (this.conn) {\n            return;\n        }\n        if (!this.transport) {\n            this.transport = isows__WEBPACK_IMPORTED_MODULE_5__.WebSocket;\n        }\n        if (!this.transport) {\n            throw new Error('No transport provided');\n        }\n        this.conn = new this.transport(this.endpointURL());\n        this.setupConnection();\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */\n    endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, { vsn: _lib_constants__WEBPACK_IMPORTED_MODULE_0__.VSN }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */\n    disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function () { }; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : '');\n            }\n            else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n            this.channels.forEach((channel) => channel.teardown());\n        }\n    }\n    /**\n     * Returns all created channels\n     */\n    getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */\n    async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */\n    async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel) => channel.unsubscribe()));\n        this.channels = [];\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */\n    log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */\n    connectionState() {\n        switch (this.conn && this.conn.readyState) {\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.connecting:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Connecting;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.open:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.closing:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closing;\n            default:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */\n    isConnected() {\n        return this.connectionState() === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n    }\n    channel(topic, params = { config: {} }) {\n        const realtimeTopic = `realtime:${topic}`;\n        const exists = this.getChannels().find((c) => c.topic === realtimeTopic);\n        if (!exists) {\n            const chan = new _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__[\"default\"](`realtime:${topic}`, params, this);\n            this.channels.push(chan);\n            return chan;\n        }\n        else {\n            return exists;\n        }\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */\n    push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = () => {\n            this.encode(data, (result) => {\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log('push', `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        }\n        else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */\n    async setAuth(token = null) {\n        let tokenToSend = token ||\n            (this.accessToken && (await this.accessToken())) ||\n            this.accessTokenValue;\n        if (this.accessTokenValue != tokenToSend) {\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel) => {\n                const payload = {\n                    access_token: tokenToSend,\n                    version: _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_VERSION,\n                };\n                tokenToSend && channel.updateJoinPayload(payload);\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend,\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */\n    async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            this.heartbeatCallback('disconnected');\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log('transport', 'heartbeat timeout. Attempting to re-establish connection');\n            this.heartbeatCallback('timeout');\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.WS_CLOSE_NORMAL, 'hearbeat timeout');\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: 'phoenix',\n            event: 'heartbeat',\n            payload: {},\n            ref: this.pendingHeartbeatRef,\n        });\n        this.heartbeatCallback('sent');\n        await this.setAuth();\n    }\n    onHeartbeat(callback) {\n        this.heartbeatCallback = callback;\n    }\n    /**\n     * Flushes send buffer\n     */\n    flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback) => callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */\n    _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        }\n        else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */\n    _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c) => c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log('transport', `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */\n    _remove(channel) {\n        this.channels = this.channels.filter((c) => c.topic !== channel.topic);\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */\n    setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = 'arraybuffer';\n            this.conn.onopen = () => this._onConnOpen();\n            this.conn.onerror = (error) => this._onConnError(error);\n            this.conn.onmessage = (event) => this._onConnMessage(event);\n            this.conn.onclose = (event) => this._onConnClose(event);\n        }\n    }\n    /** @internal */\n    _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg) => {\n            let { topic, event, payload, ref } = msg;\n            if (topic === 'phoenix' && event === 'phx_reply') {\n                this.heartbeatCallback(msg.payload.status == 'ok' ? 'ok' : 'error');\n            }\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log('receive', `${payload.status || ''} ${topic} ${event} ${(ref && '(' + ref + ')') || ''}`, payload);\n            Array.from(this.channels)\n                .filter((channel) => channel._isMember(topic))\n                .forEach((channel) => channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback) => callback(msg));\n        });\n    }\n    /** @internal */\n    _onConnOpen() {\n        this.log('transport', `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this._startHeartbeat();\n        }\n        else {\n            if (!this.workerRef) {\n                this._startWorkerHeartbeat();\n            }\n        }\n        this.stateChangeCallbacks.open.forEach((callback) => callback());\n    }\n    /** @internal */\n    _startHeartbeat() {\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.heartbeatTimer = setInterval(() => this.sendHeartbeat(), this.heartbeatIntervalMs);\n    }\n    /** @internal */\n    _startWorkerHeartbeat() {\n        if (this.workerUrl) {\n            this.log('worker', `starting worker for from ${this.workerUrl}`);\n        }\n        else {\n            this.log('worker', `starting default worker`);\n        }\n        const objectUrl = this._workerObjectUrl(this.workerUrl);\n        this.workerRef = new Worker(objectUrl);\n        this.workerRef.onerror = (error) => {\n            this.log('worker', 'worker error', error.message);\n            this.workerRef.terminate();\n        };\n        this.workerRef.onmessage = (event) => {\n            if (event.data.event === 'keepAlive') {\n                this.sendHeartbeat();\n            }\n        };\n        this.workerRef.postMessage({\n            event: 'start',\n            interval: this.heartbeatIntervalMs,\n        });\n    }\n    /** @internal */\n    _onConnClose(event) {\n        this.log('transport', 'close', event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback) => callback(event));\n    }\n    /** @internal */\n    _onConnError(error) {\n        this.log('transport', `${error}`);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback) => callback(error));\n    }\n    /** @internal */\n    _triggerChanError() {\n        this.channels.forEach((channel) => channel._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error));\n    }\n    /** @internal */\n    _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? '&' : '?';\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        }\n        else {\n            const blob = new Blob([WORKER_SCRIPT], { type: 'application/javascript' });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\n//# sourceMappingURL=RealtimeClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* binding */ REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   \"default\": () => (/* binding */ RealtimePresence)\n/* harmony export */ });\n/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/\nvar REALTIME_PRESENCE_LISTEN_EVENTS;\n(function (REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nclass RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */\n    constructor(channel, opts) {\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: () => { },\n            onLeave: () => { },\n            onSync: () => { },\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: 'presence_state',\n            diff: 'presence_diff',\n        };\n        this.channel._on(events.state, {}, (newState) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff) => {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff) => {\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            }\n            else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences) => {\n            this.channel._trigger('presence', {\n                event: 'join',\n                key,\n                currentPresences,\n                newPresences,\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences) => {\n            this.channel._trigger('presence', {\n                event: 'leave',\n                key,\n                currentPresences,\n                leftPresences,\n            });\n        });\n        this.onSync(() => {\n            this.channel._trigger('presence', { event: 'sync' });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */\n    static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences) => {\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences) => {\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m) => m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m) => m.presence_ref);\n                const joinedPresences = newPresences.filter((m) => curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m) => newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            }\n            else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, { joins, leaves }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */\n    static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves),\n        };\n        if (!onJoin) {\n            onJoin = () => { };\n        }\n        if (!onLeave) {\n            onLeave = () => { };\n        }\n        this.map(joins, (key, newPresences) => {\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m) => m.presence_ref);\n                const curPresences = currentPresences.filter((m) => joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences) => {\n            let currentPresences = state[key];\n            if (!currentPresences)\n                return;\n            const presenceRefsToRemove = leftPresences.map((m) => m.presence_ref);\n            currentPresences = currentPresences.filter((m) => presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0)\n                delete state[key];\n        });\n        return state;\n    }\n    /** @internal */\n    static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key) => func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */\n    static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key) => {\n            const presences = state[key];\n            if ('metas' in presences) {\n                newState[key] = presences.metas.map((presence) => {\n                    presence['presence_ref'] = presence['phx_ref'];\n                    delete presence['phx_ref'];\n                    delete presence['phx_ref_prev'];\n                    return presence;\n                });\n            }\n            else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */\n    static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */\n    onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */\n    onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */\n    onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */\n    inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n}\n//# sourceMappingURL=RealtimePresence.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RealtimeClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjE1L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBOEM7QUFDMEg7QUFDaEY7QUFDMEg7QUFDbE4iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjE1XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccmVhbHRpbWUtanNcXGRpc3RcXG1vZHVsZVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWx0aW1lQ2xpZW50IGZyb20gJy4vUmVhbHRpbWVDbGllbnQnO1xuaW1wb3J0IFJlYWx0aW1lQ2hhbm5lbCwgeyBSRUFMVElNRV9MSVNURU5fVFlQRVMsIFJFQUxUSU1FX1BPU1RHUkVTX0NIQU5HRVNfTElTVEVOX0VWRU5ULCBSRUFMVElNRV9TVUJTQ1JJQkVfU1RBVEVTLCBSRUFMVElNRV9DSEFOTkVMX1NUQVRFUywgfSBmcm9tICcuL1JlYWx0aW1lQ2hhbm5lbCc7XG5pbXBvcnQgUmVhbHRpbWVQcmVzZW5jZSwgeyBSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTLCB9IGZyb20gJy4vUmVhbHRpbWVQcmVzZW5jZSc7XG5leHBvcnQgeyBSZWFsdGltZVByZXNlbmNlLCBSZWFsdGltZUNoYW5uZWwsIFJlYWx0aW1lQ2xpZW50LCBSRUFMVElNRV9MSVNURU5fVFlQRVMsIFJFQUxUSU1FX1BPU1RHUkVTX0NIQU5HRVNfTElTVEVOX0VWRU5ULCBSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTLCBSRUFMVElNRV9TVUJTQ1JJQkVfU1RBVEVTLCBSRUFMVElNRV9DSEFOTkVMX1NUQVRFUywgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHANNEL_EVENTS: () => (/* binding */ CHANNEL_EVENTS),\n/* harmony export */   CHANNEL_STATES: () => (/* binding */ CHANNEL_STATES),\n/* harmony export */   CONNECTION_STATE: () => (/* binding */ CONNECTION_STATE),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* binding */ DEFAULT_TIMEOUT),\n/* harmony export */   DEFAULT_VERSION: () => (/* binding */ DEFAULT_VERSION),\n/* harmony export */   SOCKET_STATES: () => (/* binding */ SOCKET_STATES),\n/* harmony export */   TRANSPORTS: () => (/* binding */ TRANSPORTS),\n/* harmony export */   VERSION: () => (/* binding */ VERSION),\n/* harmony export */   VSN: () => (/* binding */ VSN),\n/* harmony export */   WS_CLOSE_NORMAL: () => (/* binding */ WS_CLOSE_NORMAL)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js\");\n\nconst DEFAULT_VERSION = `realtime-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`;\nconst VSN = '1.0.0';\nconst VERSION = _version__WEBPACK_IMPORTED_MODULE_0__.version;\nconst DEFAULT_TIMEOUT = 10000;\nconst WS_CLOSE_NORMAL = 1000;\nvar SOCKET_STATES;\n(function (SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nvar CHANNEL_STATES;\n(function (CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nvar CHANNEL_EVENTS;\n(function (CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nvar TRANSPORTS;\n(function (TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nvar CONNECTION_STATE;\n(function (CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {}));\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Push)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n\nclass Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */\n    constructor(channel, event, payload = {}, timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT) {\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = '';\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = '';\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived('timeout')) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef(),\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({ status, callback });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload) => {\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(() => {\n            this.trigger('timeout', {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent)\n            this.channel._trigger(this.refEvent, { status, response });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response, }) {\n        this.recHooks\n            .filter((h) => h.status === status)\n            .forEach((h) => h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n}\n//# sourceMappingURL=push.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/push.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Serializer)\n/* harmony export */ });\n// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nclass Serializer {\n    constructor() {\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === 'string') {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return { ref: null, topic: topic, event: event, payload: data };\n    }\n}\n//# sourceMappingURL=serializer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js":
/*!**************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Timer)\n/* harmony export */ });\n/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */\nclass Timer {\n    constructor(callback, timerCalc) {\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(() => {\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n}\n//# sourceMappingURL=timer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js":
/*!*********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgresTypes: () => (/* binding */ PostgresTypes),\n/* harmony export */   convertCell: () => (/* binding */ convertCell),\n/* harmony export */   convertChangeData: () => (/* binding */ convertChangeData),\n/* harmony export */   convertColumn: () => (/* binding */ convertColumn),\n/* harmony export */   httpEndpointURL: () => (/* binding */ httpEndpointURL),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toJson: () => (/* binding */ toJson),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toTimestampString: () => (/* binding */ toTimestampString)\n/* harmony export */ });\n/**\n * Helpers to convert the change Payload into native JS types.\n */\n// Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nvar PostgresTypes;\n(function (PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */\nconst convertChangeData = (columns, record, options = {}) => {\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key) => {\n        acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */\nconst convertColumn = (columnName, columns, record, skipTypes) => {\n    const column = columns.find((x) => x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return convertCell(colType, value);\n    }\n    return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */\nconst convertCell = (type, value) => {\n    // if data type is an array\n    if (type.charAt(0) === '_') {\n        const dataType = type.slice(1, type.length);\n        return toArray(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch (type) {\n        case PostgresTypes.bool:\n            return toBoolean(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return toNumber(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return toJson(value);\n        case PostgresTypes.timestamp:\n            return toTimestampString(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime: // To allow users to cast it based on Timezone\n        case PostgresTypes.date: // To allow users to cast it based on Timezone\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime: // To allow users to cast it based on Timezone\n        case PostgresTypes.text:\n        case PostgresTypes.time: // To allow users to cast it based on Timezone\n        case PostgresTypes.timestamptz: // To allow users to cast it based on Timezone\n        case PostgresTypes.timetz: // To allow users to cast it based on Timezone\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nconst noop = (value) => {\n    return value;\n};\nconst toBoolean = (value) => {\n    switch (value) {\n        case 't':\n            return true;\n        case 'f':\n            return false;\n        default:\n            return value;\n    }\n};\nconst toNumber = (value) => {\n    if (typeof value === 'string') {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nconst toJson = (value) => {\n    if (typeof value === 'string') {\n        try {\n            return JSON.parse(value);\n        }\n        catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */\nconst toArray = (value, type) => {\n    if (typeof value !== 'string') {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === '{' && closeBrace === '}') {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse('[' + valTrim + ']');\n        }\n        catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(',') : [];\n        }\n        return arr.map((val) => convertCell(type, val));\n    }\n    return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */\nconst toTimestampString = (value) => {\n    if (typeof value === 'string') {\n        return value.replace(' ', 'T');\n    }\n    return value;\n};\nconst httpEndpointURL = (socketUrl) => {\n    let url = socketUrl;\n    url = url.replace(/^ws/i, 'http');\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, '');\n    return url.replace(/\\/+$/, '');\n};\n//# sourceMappingURL=transformers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js":
/*!****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.11.15';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjE1L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcmVhbHRpbWUtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjE1XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccmVhbHRpbWUtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuMTEuMTUnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/lib/version.js\n");

/***/ })

};
;