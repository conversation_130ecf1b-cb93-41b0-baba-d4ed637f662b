'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  DollarSign, 
  Users, 
  Eye,
  MousePointer,
  Zap,
  BarChart3,
  RefreshCw
} from 'lucide-react';

interface MetricData {
  label: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  format: 'number' | 'percentage' | 'currency' | 'time';
}

interface PerformanceMetricsProps {
  campaignId?: string;
  timeframe?: '24h' | '7d' | '30d' | '90d';
  className?: string;
}

export function PerformanceMetrics({ 
  campaignId, 
  timeframe = '7d', 
  className 
}: PerformanceMetricsProps): React.JSX.Element {
  const [metrics, setMetrics] = useState<MetricData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Mock data - in production this would come from the analytics service
  const mockMetrics: MetricData[] = useMemo(() => [
    {
      label: 'Impressions',
      value: 125420,
      change: 12.5,
      trend: 'up',
      format: 'number',
    },
    {
      label: 'Click-through Rate',
      value: 3.2,
      change: 0.8,
      trend: 'up',
      format: 'percentage',
    },
    {
      label: 'Cost per Click',
      value: 1.45,
      change: -0.15,
      trend: 'down',
      format: 'currency',
    },
    {
      label: 'Conversion Rate',
      value: 2.8,
      change: 0.3,
      trend: 'up',
      format: 'percentage',
    },
    {
      label: 'Cost per Acquisition',
      value: 52.30,
      change: -5.20,
      trend: 'down',
      format: 'currency',
    },
    {
      label: 'Return on Ad Spend',
      value: 4.2,
      change: 0.6,
      trend: 'up',
      format: 'number',
    },
    {
      label: 'Total Spend',
      value: 2840.50,
      change: 340.20,
      trend: 'up',
      format: 'currency',
    },
    {
      label: 'Revenue',
      value: 11930.10,
      change: 1520.30,
      trend: 'up',
      format: 'currency',
    },
  ], []);

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        value: metric.value + (Math.random() - 0.5) * metric.value * 0.02,
        change: metric.change + (Math.random() - 0.5) * 0.1,
      })));
      setLastUpdated(new Date());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    setMetrics(mockMetrics);
  }, [mockMetrics]);

  const formatValue = (value: number, format: MetricData['format']): string => {
    switch (format) {
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return `$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
      case 'time':
        return `${value.toFixed(1)}s`;
      default:
        return value.toLocaleString(undefined, { maximumFractionDigits: 1 });
    }
  };

  const formatChange = (change: number, format: MetricData['format']): string => {
    const prefix = change >= 0 ? '+' : '';
    switch (format) {
      case 'percentage':
        return `${prefix}${change.toFixed(1)}%`;
      case 'currency':
        return `${prefix}$${Math.abs(change).toFixed(2)}`;
      default:
        return `${prefix}${change.toFixed(1)}`;
    }
  };

  const getMetricIcon = (label: string) => {
    switch (label.toLowerCase()) {
      case 'impressions': return Eye;
      case 'click-through rate': return MousePointer;
      case 'cost per click': return DollarSign;
      case 'conversion rate': return Target;
      case 'cost per acquisition': return DollarSign;
      case 'return on ad spend': return TrendingUp;
      case 'total spend': return DollarSign;
      case 'revenue': return DollarSign;
      default: return BarChart3;
    }
  };

  const refreshMetrics = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsLoading(false);
  };

  return (
    <div className={`space-y-6 ${className ?? ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-flux-500" />
            Performance Metrics
          </h3>
          <p className="text-sm text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <select 
            className="text-sm border rounded px-2 py-1 bg-background"
            value={timeframe}
            onChange={(e) => {/* Handle timeframe change */}}
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>

          <Button
            variant="outline"
            size="sm"
            onClick={refreshMetrics}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => {
          const Icon = getMetricIcon(metric.label);
          const isPositive = metric.trend === 'up';
          const TrendIcon = isPositive ? TrendingUp : TrendingDown;

          return (
            <div
              key={metric.label}
              className="bg-card/50 backdrop-blur-sm border rounded-lg p-4 transition-all duration-300 hover:scale-105 hover:bg-card/70"
              style={{
                animationDelay: `${index * 100}ms`,
              }}
            >
              <div className="flex items-center justify-between mb-2">
                <Icon className="h-5 w-5 text-muted-foreground" />
                <div className={`flex items-center space-x-1 text-xs ${
                  isPositive ? 'text-green-500' : 'text-red-500'
                }`}>
                  <TrendIcon className="h-3 w-3" />
                  <span>{formatChange(metric.change, metric.format)}</span>
                </div>
              </div>

              <div className="space-y-1">
                <div className="text-2xl font-bold text-foreground">
                  {formatValue(metric.value, metric.format)}
                </div>
                <div className="text-sm text-muted-foreground">
                  {metric.label}
                </div>
              </div>

              {/* Mini trend indicator */}
              <div className="mt-3 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className={`h-full transition-all duration-1000 ${
                    isPositive ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  style={{ 
                    width: `${Math.min(Math.abs(metric.change) * 10, 100)}%`,
                    animation: 'pulse 2s infinite'
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-gradient-to-r from-green-500/20 to-green-600/20 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-500/20 rounded-full flex items-center justify-center">
              <TrendingUp className="h-5 w-5 text-green-500" />
            </div>
            <div>
              <div className="text-lg font-bold text-green-500">+24.5%</div>
              <div className="text-sm text-muted-foreground">Overall Performance</div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-500/20 to-blue-600/20 border border-blue-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-500/20 rounded-full flex items-center justify-center">
              <Zap className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <div className="text-lg font-bold text-blue-500">AI Optimized</div>
              <div className="text-sm text-muted-foreground">Real-time Adjustments</div>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-500/20 to-purple-600/20 border border-purple-500/30 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <div className="text-lg font-bold text-purple-500">3.2M</div>
              <div className="text-sm text-muted-foreground">Total Reach</div>
            </div>
          </div>
        </div>
      </div>

      {/* AI Insights */}
      <div className="bg-card/30 backdrop-blur-sm border rounded-lg p-4">
        <h4 className="font-medium mb-3 flex items-center">
          <Zap className="h-4 w-4 mr-2 text-flux-500" />
          AI Insights & Recommendations
        </h4>
        
        <div className="space-y-2 text-sm">
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
            <p>
              <strong>CTR increased 12.5%</strong> after AI copy optimization. 
              Consider applying similar tone to other campaigns.
            </p>
          </div>
          
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
            <p>
              <strong>Budget reallocation recommended:</strong> Shift 15% from Facebook to Google Ads 
              based on conversion performance.
            </p>
          </div>
          
          <div className="flex items-start space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2 flex-shrink-0" />
            <p>
              <strong>Creative refresh suggested:</strong> Design variants showing fatigue. 
              New creative assets recommended within 48 hours.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
