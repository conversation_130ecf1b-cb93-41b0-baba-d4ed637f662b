import { Matrix } from 'ml-matrix';
import { evaluate } from 'mathjs';
import { cloneDeep, sum, maxBy, minBy } from 'lodash';
import {
  OptimizationRequest,
  OptimizationResult,
  BudgetAllocation,
  ClassicalOptimizationError,
  OptimizationMetrics,
  PerformanceMetrics,
} from '../types';

/**
 * Classical Budget Optimization Solver
 * 
 * Implements multiple classical optimization algorithms as fallback
 * when quantum optimization is unavailable or suboptimal.
 * 
 * Algorithms:
 * - Sequential Quadratic Programming (SQP)
 * - Genetic Algorithm (GA)
 * - Particle Swarm Optimization (PSO)
 * - Simulated Annealing (SA)
 * - Interior Point Method
 */
export class ClassicalSolver {
  private maxIterations: number;
  private convergenceThreshold: number;
  private populationSize: number;

  constructor(options: {
    maxIterations?: number;
    convergenceThreshold?: number;
    populationSize?: number;
  } = {}) {
    this.maxIterations = options.maxIterations || 1000;
    this.convergenceThreshold = options.convergenceThreshold || 1e-6;
    this.populationSize = options.populationSize || 100;
  }

  /**
   * Main optimization entry point
   */
  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {
    const startTime = Date.now();
    
    try {
      // Choose best algorithm based on problem characteristics
      const algorithm = this.selectAlgorithm(request);
      console.log(`[Classical] Using ${algorithm} algorithm`);

      let result: OptimizationResult;
      
      switch (algorithm) {
        case 'sqp':
          result = await this.sequentialQuadraticProgramming(request);
          break;
        case 'genetic':
          result = await this.geneticAlgorithm(request);
          break;
        case 'pso':
          result = await this.particleSwarmOptimization(request);
          break;
        case 'simulated_annealing':
          result = await this.simulatedAnnealing(request);
          break;
        case 'interior_point':
          result = await this.interiorPointMethod(request);
          break;
        default:
          throw new ClassicalOptimizationError(
            `Unknown algorithm: ${algorithm}`,
            algorithm
          );
      }

      result.computeTime = Date.now() - startTime;
      result.method = 'classical';
      
      return result;
    } catch (error) {
      throw new ClassicalOptimizationError(
        `Classical optimization failed: ${error.message}`,
        'unknown',
        { originalError: error }
      );
    }
  }

  /**
   * Sequential Quadratic Programming (SQP)
   * Best for smooth, differentiable problems with equality/inequality constraints
   */
  private async sequentialQuadraticProgramming(request: OptimizationRequest): Promise<OptimizationResult> {
    const numChannels = request.channels.length;
    let x = this.getInitialSolution(request); // Initial budget allocation
    let iteration = 0;
    let converged = false;

    const metrics: OptimizationMetrics = {
      objectiveValue: 0,
      constraintViolations: 0,
      feasibility: false,
      optimality: 0,
      convergenceRate: 0,
    };

    while (iteration < this.maxIterations && !converged) {
      // Calculate gradient and Hessian
      const gradient = this.calculateGradient(x, request);
      const hessian = this.calculateHessian(x, request);
      
      // Solve QP subproblem
      const direction = this.solveQPSubproblem(gradient, hessian, x, request);
      
      // Line search
      const stepSize = this.lineSearch(x, direction, request);
      
      // Update solution
      const newX = x.map((xi, i) => xi + stepSize * direction[i]);
      
      // Check convergence
      const change = this.calculateNorm(newX.map((xi, i) => xi - x[i]));
      converged = change < this.convergenceThreshold;
      
      x = newX;
      iteration++;
      
      // Update metrics
      metrics.objectiveValue = this.evaluateObjective(x, request);
      metrics.constraintViolations = this.evaluateConstraintViolations(x, request);
    }

    metrics.feasibility = metrics.constraintViolations < 1e-6;
    metrics.optimality = converged ? 1.0 : 0.5;
    metrics.convergenceRate = iteration / this.maxIterations;

    return this.buildResult(request, x, metrics, iteration, 'sqp');
  }

  /**
   * Genetic Algorithm
   * Best for discrete, combinatorial problems with complex constraints
   */
  private async geneticAlgorithm(request: OptimizationRequest): Promise<OptimizationResult> {
    const numChannels = request.channels.length;
    let population = this.initializePopulation(request, this.populationSize);
    let generation = 0;
    let bestSolution = population[0];
    let bestFitness = this.evaluateFitness(bestSolution, request);

    while (generation < this.maxIterations) {
      // Evaluate fitness for all individuals
      const fitness = population.map(individual => this.evaluateFitness(individual, request));
      
      // Find best individual
      const maxFitnessIndex = fitness.indexOf(Math.max(...fitness));
      if (fitness[maxFitnessIndex] > bestFitness) {
        bestSolution = cloneDeep(population[maxFitnessIndex]);
        bestFitness = fitness[maxFitnessIndex];
      }

      // Selection (tournament selection)
      const parents = this.tournamentSelection(population, fitness, this.populationSize);
      
      // Crossover and mutation
      const offspring = this.crossoverAndMutation(parents, request);
      
      // Replace population
      population = offspring;
      generation++;
    }

    const metrics: OptimizationMetrics = {
      objectiveValue: this.evaluateObjective(bestSolution, request),
      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),
      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,
      optimality: 0.8, // GA doesn't guarantee global optimum
      convergenceRate: generation / this.maxIterations,
    };

    return this.buildResult(request, bestSolution, metrics, generation, 'genetic');
  }

  /**
   * Particle Swarm Optimization
   * Best for continuous optimization with multiple local optima
   */
  private async particleSwarmOptimization(request: OptimizationRequest): Promise<OptimizationResult> {
    const numChannels = request.channels.length;
    const numParticles = this.populationSize;
    
    // Initialize particles
    const particles = Array(numParticles).fill(null).map(() => ({
      position: this.getInitialSolution(request),
      velocity: Array(numChannels).fill(0).map(() => (Math.random() - 0.5) * 0.1),
      bestPosition: [] as number[],
      bestFitness: -Infinity,
    }));

    let globalBestPosition = particles[0].position;
    let globalBestFitness = -Infinity;
    let iteration = 0;

    // PSO parameters
    const w = 0.7; // Inertia weight
    const c1 = 1.5; // Cognitive parameter
    const c2 = 1.5; // Social parameter

    while (iteration < this.maxIterations) {
      for (const particle of particles) {
        const fitness = this.evaluateFitness(particle.position, request);
        
        // Update personal best
        if (fitness > particle.bestFitness) {
          particle.bestPosition = cloneDeep(particle.position);
          particle.bestFitness = fitness;
        }
        
        // Update global best
        if (fitness > globalBestFitness) {
          globalBestPosition = cloneDeep(particle.position);
          globalBestFitness = fitness;
        }
        
        // Update velocity and position
        for (let i = 0; i < numChannels; i++) {
          const r1 = Math.random();
          const r2 = Math.random();
          
          particle.velocity[i] = w * particle.velocity[i] +
            c1 * r1 * (particle.bestPosition[i] - particle.position[i]) +
            c2 * r2 * (globalBestPosition[i] - particle.position[i]);
          
          particle.position[i] += particle.velocity[i];
          
          // Ensure bounds
          particle.position[i] = Math.max(0, Math.min(1, particle.position[i]));
        }
      }
      
      iteration++;
    }

    const metrics: OptimizationMetrics = {
      objectiveValue: this.evaluateObjective(globalBestPosition, request),
      constraintViolations: this.evaluateConstraintViolations(globalBestPosition, request),
      feasibility: this.evaluateConstraintViolations(globalBestPosition, request) < 1e-6,
      optimality: 0.85,
      convergenceRate: iteration / this.maxIterations,
    };

    return this.buildResult(request, globalBestPosition, metrics, iteration, 'pso');
  }

  /**
   * Simulated Annealing
   * Best for discrete optimization with many local optima
   */
  private async simulatedAnnealing(request: OptimizationRequest): Promise<OptimizationResult> {
    let currentSolution = this.getInitialSolution(request);
    let currentEnergy = -this.evaluateFitness(currentSolution, request);
    let bestSolution = cloneDeep(currentSolution);
    let bestEnergy = currentEnergy;
    
    const initialTemp = 1000;
    const finalTemp = 0.01;
    const coolingRate = 0.95;
    let temperature = initialTemp;
    let iteration = 0;

    while (temperature > finalTemp && iteration < this.maxIterations) {
      // Generate neighbor solution
      const neighbor = this.generateNeighbor(currentSolution, request);
      const neighborEnergy = -this.evaluateFitness(neighbor, request);
      
      // Accept or reject neighbor
      const deltaE = neighborEnergy - currentEnergy;
      if (deltaE < 0 || Math.random() < Math.exp(-deltaE / temperature)) {
        currentSolution = neighbor;
        currentEnergy = neighborEnergy;
        
        if (currentEnergy < bestEnergy) {
          bestSolution = cloneDeep(currentSolution);
          bestEnergy = currentEnergy;
        }
      }
      
      temperature *= coolingRate;
      iteration++;
    }

    const metrics: OptimizationMetrics = {
      objectiveValue: this.evaluateObjective(bestSolution, request),
      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),
      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,
      optimality: 0.75,
      convergenceRate: iteration / this.maxIterations,
    };

    return this.buildResult(request, bestSolution, metrics, iteration, 'simulated_annealing');
  }

  /**
   * Interior Point Method
   * Best for convex optimization with inequality constraints
   */
  private async interiorPointMethod(request: OptimizationRequest): Promise<OptimizationResult> {
    const numChannels = request.channels.length;
    let x = this.getInitialSolution(request);
    let mu = 1.0; // Barrier parameter
    let iteration = 0;
    let converged = false;

    while (iteration < this.maxIterations && !converged && mu > 1e-8) {
      // Solve barrier subproblem
      const result = this.solveBarrierSubproblem(x, mu, request);
      x = result.solution;
      converged = result.converged;
      
      // Update barrier parameter
      mu *= 0.1;
      iteration++;
    }

    const metrics: OptimizationMetrics = {
      objectiveValue: this.evaluateObjective(x, request),
      constraintViolations: this.evaluateConstraintViolations(x, request),
      feasibility: this.evaluateConstraintViolations(x, request) < 1e-6,
      optimality: converged ? 0.95 : 0.7,
      convergenceRate: iteration / this.maxIterations,
    };

    return this.buildResult(request, x, metrics, iteration, 'interior_point');
  }

  // Helper methods...
  private selectAlgorithm(request: OptimizationRequest): string {
    const numChannels = request.channels.length;
    const numConstraints = request.constraints.length;
    const hasNonlinearConstraints = request.constraints.some(c => 
      c.type === 'roas_target' || c.type === 'cpa_target'
    );

    if (numChannels <= 5 && !hasNonlinearConstraints) {
      return 'sqp'; // Small, smooth problems
    } else if (numChannels > 20 || hasNonlinearConstraints) {
      return 'genetic'; // Large or complex problems
    } else if (request.objective.primary === 'maximize_roas') {
      return 'pso'; // Continuous optimization
    } else {
      return 'interior_point'; // General case
    }
  }

  private getInitialSolution(request: OptimizationRequest): number[] {
    // Equal allocation as starting point
    const numChannels = request.channels.length;
    return Array(numChannels).fill(1 / numChannels);
  }

  private evaluateObjective(allocation: number[], request: OptimizationRequest): number {
    let objective = 0;
    const totalBudget = request.totalBudget;

    allocation.forEach((weight, i) => {
      const channel = request.channels[i];
      const budget = weight * totalBudget;
      
      // Calculate expected revenue based on channel model
      const conversions = this.calculateConversions(budget, channel);
      const revenue = conversions * channel.averageOrderValue;
      
      switch (request.objective.primary) {
        case 'maximize_revenue':
          objective += revenue;
          break;
        case 'maximize_conversions':
          objective += conversions;
          break;
        case 'minimize_cpa':
          objective -= budget / Math.max(conversions, 1);
          break;
        case 'maximize_roas':
          objective += revenue / Math.max(budget, 1);
          break;
      }
    });

    return objective;
  }

  private evaluateFitness(allocation: number[], request: OptimizationRequest): number {
    const objective = this.evaluateObjective(allocation, request);
    const violations = this.evaluateConstraintViolations(allocation, request);
    
    // Penalize constraint violations
    return objective - 1000 * violations;
  }

  private evaluateConstraintViolations(allocation: number[], request: OptimizationRequest): number {
    let violations = 0;
    const totalBudget = request.totalBudget;

    // Budget allocation must sum to 1
    const allocationSum = sum(allocation);
    violations += Math.abs(allocationSum - 1);

    // Check individual constraints
    request.constraints.forEach(constraint => {
      switch (constraint.type) {
        case 'total_budget':
          const totalSpend = allocationSum * totalBudget;
          if (constraint.operator === '<=' && totalSpend > constraint.value) {
            violations += (totalSpend - constraint.value) / constraint.value;
          }
          break;
        // Add other constraint types...
      }
    });

    return violations;
  }

  private calculateConversions(budget: number, channel: any): number {
    // Simplified conversion model with diminishing returns
    const baseConversions = budget * channel.conversionRate / channel.baseRate;
    const saturationFactor = channel.saturationPoint ? 
      Math.min(1, channel.saturationPoint / budget) : 1;
    
    return baseConversions * saturationFactor * channel.scalingFactor;
  }

  private buildResult(
    request: OptimizationRequest,
    allocation: number[],
    metrics: OptimizationMetrics,
    iterations: number,
    algorithm: string
  ): OptimizationResult {
    const allocations: BudgetAllocation[] = allocation.map((weight, i) => {
      const channel = request.channels[i];
      const budget = weight * request.totalBudget;
      const conversions = this.calculateConversions(budget, channel);
      const revenue = conversions * channel.averageOrderValue;

      return {
        channelId: channel.id,
        allocatedBudget: budget,
        expectedConversions: conversions,
        expectedRevenue: revenue,
        expectedCPA: budget / Math.max(conversions, 1),
        expectedROAS: revenue / Math.max(budget, 1),
        confidence: 0.8, // Classical methods have good confidence
        riskScore: 3, // Moderate risk
      };
    });

    return {
      id: `result_${Date.now()}`,
      requestId: request.id,
      status: metrics.feasibility ? 'success' : 'partial',
      method: 'classical',
      allocations,
      totalAllocated: sum(allocations.map(a => a.allocatedBudget)),
      expectedTotalRevenue: sum(allocations.map(a => a.expectedRevenue)),
      expectedTotalConversions: sum(allocations.map(a => a.expectedConversions)),
      overallROAS: sum(allocations.map(a => a.expectedRevenue)) / 
                   Math.max(sum(allocations.map(a => a.allocatedBudget)), 1),
      overallCPA: sum(allocations.map(a => a.allocatedBudget)) / 
                  Math.max(sum(allocations.map(a => a.expectedConversions)), 1),
      confidence: metrics.optimality,
      riskScore: metrics.feasibility ? 2 : 6,
      optimizationMetrics: {
        iterations,
        convergenceTime: 0, // Will be set by caller
        objectiveValue: metrics.objectiveValue,
        constraintViolations: [],
      },
      recommendations: [],
      createdAt: new Date(),
      computeTime: 0, // Will be set by caller
    };
  }

  // Additional helper methods for specific algorithms...
  private calculateGradient(x: number[], request: OptimizationRequest): number[] {
    const h = 1e-8;
    const gradient = new Array(x.length);
    
    for (let i = 0; i < x.length; i++) {
      const xPlus = [...x];
      const xMinus = [...x];
      xPlus[i] += h;
      xMinus[i] -= h;
      
      gradient[i] = (this.evaluateObjective(xPlus, request) - 
                    this.evaluateObjective(xMinus, request)) / (2 * h);
    }
    
    return gradient;
  }

  private calculateHessian(x: number[], request: OptimizationRequest): number[][] {
    const h = 1e-6;
    const n = x.length;
    const hessian = Array(n).fill(null).map(() => Array(n).fill(0));
    
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        const xPP = [...x]; xPP[i] += h; xPP[j] += h;
        const xPM = [...x]; xPM[i] += h; xPM[j] -= h;
        const xMP = [...x]; xMP[i] -= h; xMP[j] += h;
        const xMM = [...x]; xMM[i] -= h; xMM[j] -= h;
        
        hessian[i][j] = (
          this.evaluateObjective(xPP, request) -
          this.evaluateObjective(xPM, request) -
          this.evaluateObjective(xMP, request) +
          this.evaluateObjective(xMM, request)
        ) / (4 * h * h);
      }
    }
    
    return hessian;
  }

  private solveQPSubproblem(gradient: number[], hessian: number[][], x: number[], request: OptimizationRequest): number[] {
    // Simplified QP solver - in practice would use specialized library
    const n = x.length;
    const direction = new Array(n).fill(0);
    
    // Simple steepest descent direction
    for (let i = 0; i < n; i++) {
      direction[i] = -gradient[i];
    }
    
    return direction;
  }

  private lineSearch(x: number[], direction: number[], request: OptimizationRequest): number {
    let alpha = 1.0;
    const c1 = 1e-4; // Armijo condition parameter
    const rho = 0.5; // Backtracking parameter
    
    const f0 = this.evaluateObjective(x, request);
    const grad0 = this.calculateGradient(x, request);
    const directionalDerivative = sum(grad0.map((g, i) => g * direction[i]));
    
    while (alpha > 1e-8) {
      const newX = x.map((xi, i) => xi + alpha * direction[i]);
      const f1 = this.evaluateObjective(newX, request);
      
      if (f1 >= f0 + c1 * alpha * directionalDerivative) {
        return alpha;
      }
      
      alpha *= rho;
    }
    
    return alpha;
  }

  private calculateNorm(vector: number[]): number {
    return Math.sqrt(sum(vector.map(v => v * v)));
  }

  private initializePopulation(request: OptimizationRequest, size: number): number[][] {
    const numChannels = request.channels.length;
    const population: number[][] = [];
    
    for (let i = 0; i < size; i++) {
      const individual = Array(numChannels).fill(0).map(() => Math.random());
      const total = sum(individual);
      // Normalize to sum to 1
      population.push(individual.map(x => x / total));
    }
    
    return population;
  }

  private tournamentSelection(population: number[][], fitness: number[], size: number): number[][] {
    const selected: number[][] = [];
    const tournamentSize = 3;
    
    for (let i = 0; i < size; i++) {
      let best = Math.floor(Math.random() * population.length);
      
      for (let j = 1; j < tournamentSize; j++) {
        const candidate = Math.floor(Math.random() * population.length);
        if (fitness[candidate] > fitness[best]) {
          best = candidate;
        }
      }
      
      selected.push(cloneDeep(population[best]));
    }
    
    return selected;
  }

  private crossoverAndMutation(parents: number[][], request: OptimizationRequest): number[][] {
    const offspring: number[][] = [];
    const mutationRate = 0.1;
    const crossoverRate = 0.8;
    
    for (let i = 0; i < parents.length; i += 2) {
      let child1 = cloneDeep(parents[i]);
      let child2 = cloneDeep(parents[i + 1] || parents[i]);
      
      // Crossover
      if (Math.random() < crossoverRate) {
        const crossoverPoint = Math.floor(Math.random() * child1.length);
        for (let j = crossoverPoint; j < child1.length; j++) {
          [child1[j], child2[j]] = [child2[j], child1[j]];
        }
      }
      
      // Mutation
      if (Math.random() < mutationRate) {
        const mutationPoint = Math.floor(Math.random() * child1.length);
        child1[mutationPoint] += (Math.random() - 0.5) * 0.1;
        child1[mutationPoint] = Math.max(0, child1[mutationPoint]);
      }
      
      // Normalize
      const total1 = sum(child1);
      const total2 = sum(child2);
      child1 = child1.map(x => x / total1);
      child2 = child2.map(x => x / total2);
      
      offspring.push(child1, child2);
    }
    
    return offspring.slice(0, parents.length);
  }

  private generateNeighbor(solution: number[], request: OptimizationRequest): number[] {
    const neighbor = cloneDeep(solution);
    const perturbationSize = 0.05;
    
    // Randomly perturb one or two elements
    const numPerturbations = Math.random() < 0.5 ? 1 : 2;
    
    for (let i = 0; i < numPerturbations; i++) {
      const index = Math.floor(Math.random() * neighbor.length);
      neighbor[index] += (Math.random() - 0.5) * perturbationSize;
      neighbor[index] = Math.max(0, neighbor[index]);
    }
    
    // Normalize
    const total = sum(neighbor);
    return neighbor.map(x => x / total);
  }

  private solveBarrierSubproblem(x: number[], mu: number, request: OptimizationRequest): { solution: number[]; converged: boolean } {
    // Simplified barrier method implementation
    let currentX = cloneDeep(x);
    let converged = false;
    const maxInnerIterations = 50;
    
    for (let iter = 0; iter < maxInnerIterations; iter++) {
      const gradient = this.calculateBarrierGradient(currentX, mu, request);
      const stepSize = this.lineSearch(currentX, gradient.map(g => -g), request);
      
      const newX = currentX.map((xi, i) => xi - stepSize * gradient[i]);
      
      // Ensure feasibility
      const total = sum(newX);
      const normalizedX = newX.map(xi => Math.max(1e-8, xi / total));
      
      const change = this.calculateNorm(normalizedX.map((xi, i) => xi - currentX[i]));
      if (change < this.convergenceThreshold) {
        converged = true;
        break;
      }
      
      currentX = normalizedX;
    }
    
    return { solution: currentX, converged };
  }

  private calculateBarrierGradient(x: number[], mu: number, request: OptimizationRequest): number[] {
    const objectiveGradient = this.calculateGradient(x, request);
    const barrierGradient = new Array(x.length);
    
    // Add barrier terms for bound constraints
    for (let i = 0; i < x.length; i++) {
      barrierGradient[i] = objectiveGradient[i] - mu / Math.max(x[i], 1e-8);
    }
    
    return barrierGradient;
  }
}
