{"version": 3, "sources": ["../src/types/index.ts", "../src/quantum/willow-bridge.ts", "../src/classical/solver.ts", "../src/quantum/optimizer.ts", "../src/utils/index.ts", "../src/index.ts"], "sourcesContent": ["import { z } from 'zod';\n\n// Budget Constraint Types\nexport const BudgetConstraintSchema = z.object({\n  id: z.string(),\n  type: z.enum(['total_budget', 'channel_budget', 'daily_budget', 'cpa_target', 'roas_target']),\n  value: z.number().positive(),\n  operator: z.enum(['<=', '>=', '=', '<', '>']),\n  priority: z.number().min(1).max(10).default(5),\n  flexible: z.boolean().default(false),\n  tolerance: z.number().min(0).max(1).default(0.1),\n});\n\nexport type BudgetConstraint = z.infer<typeof BudgetConstraintSchema>;\n\n// Channel Configuration\nexport const ChannelConfigSchema = z.object({\n  id: z.string(),\n  name: z.string(),\n  type: z.enum(['facebook', 'google', 'linkedin', 'twitter', 'tiktok', 'email', 'sms']),\n  minBudget: z.number().min(0),\n  maxBudget: z.number().positive(),\n  costModel: z.enum(['cpc', 'cpm', 'cpa', 'cpv', 'flat']),\n  baseRate: z.number().positive(),\n  scalingFactor: z.number().positive().default(1.0),\n  saturationPoint: z.number().positive().optional(),\n  conversionRate: z.number().min(0).max(1),\n  averageOrderValue: z.number().positive(),\n  seasonality: z.array(z.number()).length(12).default(Array(12).fill(1.0)),\n  competitiveIndex: z.number().min(0).max(10).default(5),\n});\n\nexport type ChannelConfig = z.infer<typeof ChannelConfigSchema>;\n\n// Optimization Objective\nexport const OptimizationObjectiveSchema = z.object({\n  primary: z.enum(['maximize_revenue', 'maximize_conversions', 'minimize_cpa', 'maximize_roas']),\n  secondary: z.enum(['maximize_reach', 'minimize_cost', 'maximize_engagement']).optional(),\n  weights: z.object({\n    primary: z.number().min(0).max(1).default(0.8),\n    secondary: z.number().min(0).max(1).default(0.2),\n  }),\n  timeHorizon: z.enum(['daily', 'weekly', 'monthly', 'quarterly']).default('monthly'),\n});\n\nexport type OptimizationObjective = z.infer<typeof OptimizationObjectiveSchema>;\n\n// Budget Allocation Result\nexport const BudgetAllocationSchema = z.object({\n  channelId: z.string(),\n  allocatedBudget: z.number().min(0),\n  expectedConversions: z.number().min(0),\n  expectedRevenue: z.number().min(0),\n  expectedCPA: z.number().positive(),\n  expectedROAS: z.number().positive(),\n  confidence: z.number().min(0).max(1),\n  riskScore: z.number().min(0).max(10),\n});\n\nexport type BudgetAllocation = z.infer<typeof BudgetAllocationSchema>;\n\n// Optimization Request\nexport const OptimizationRequestSchema = z.object({\n  id: z.string(),\n  totalBudget: z.number().positive(),\n  channels: z.array(ChannelConfigSchema),\n  constraints: z.array(BudgetConstraintSchema),\n  objective: OptimizationObjectiveSchema,\n  timeframe: z.object({\n    start: z.date(),\n    end: z.date(),\n  }),\n  historicalData: z.object({\n    channelPerformance: z.record(z.string(), z.array(z.object({\n      date: z.date(),\n      spend: z.number().min(0),\n      conversions: z.number().min(0),\n      revenue: z.number().min(0),\n    }))),\n    marketConditions: z.array(z.object({\n      date: z.date(),\n      competitiveIndex: z.number().min(0).max(10),\n      seasonalityFactor: z.number().positive(),\n      economicIndicator: z.number(),\n    })),\n  }).optional(),\n  preferences: z.object({\n    useQuantumOptimization: z.boolean().default(true),\n    quantumFallbackThreshold: z.number().min(0).max(1).default(0.95),\n    maxIterations: z.number().positive().default(1000),\n    convergenceThreshold: z.number().positive().default(0.001),\n    riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).default('moderate'),\n  }).default({}),\n});\n\nexport type OptimizationRequest = z.infer<typeof OptimizationRequestSchema>;\n\n// Optimization Result\nexport const OptimizationResultSchema = z.object({\n  id: z.string(),\n  requestId: z.string(),\n  status: z.enum(['success', 'partial', 'failed']),\n  method: z.enum(['quantum', 'classical', 'hybrid']),\n  allocations: z.array(BudgetAllocationSchema),\n  totalAllocated: z.number().min(0),\n  expectedTotalRevenue: z.number().min(0),\n  expectedTotalConversions: z.number().min(0),\n  overallROAS: z.number().positive(),\n  overallCPA: z.number().positive(),\n  confidence: z.number().min(0).max(1),\n  riskScore: z.number().min(0).max(10),\n  optimizationMetrics: z.object({\n    iterations: z.number().positive(),\n    convergenceTime: z.number().positive(),\n    objectiveValue: z.number(),\n    constraintViolations: z.array(z.object({\n      constraintId: z.string(),\n      violation: z.number(),\n      severity: z.enum(['low', 'medium', 'high']),\n    })),\n  }),\n  quantumMetrics: z.object({\n    quantumAdvantage: z.number().min(0).max(1).optional(),\n    coherenceTime: z.number().positive().optional(),\n    gateCount: z.number().positive().optional(),\n    errorRate: z.number().min(0).max(1).optional(),\n    willowChipUtilization: z.number().min(0).max(1).optional(),\n  }).optional(),\n  recommendations: z.array(z.object({\n    type: z.enum(['budget_increase', 'channel_rebalance', 'constraint_relaxation', 'timing_adjustment']),\n    description: z.string(),\n    impact: z.object({\n      revenueChange: z.number(),\n      costChange: z.number(),\n      riskChange: z.number(),\n    }),\n    priority: z.enum(['low', 'medium', 'high']),\n  })),\n  createdAt: z.date(),\n  computeTime: z.number().positive(),\n});\n\nexport type OptimizationResult = z.infer<typeof OptimizationResultSchema>;\n\n// Quantum Circuit Configuration\nexport const QuantumCircuitConfigSchema = z.object({\n  qubits: z.number().positive().max(105), // Willow chip limit\n  depth: z.number().positive(),\n  gateSet: z.array(z.enum(['H', 'X', 'Y', 'Z', 'CNOT', 'CZ', 'RX', 'RY', 'RZ', 'SWAP'])),\n  errorCorrection: z.boolean().default(true),\n  coherenceTime: z.number().positive().default(100), // microseconds\n  fidelity: z.number().min(0).max(1).default(0.999),\n});\n\nexport type QuantumCircuitConfig = z.infer<typeof QuantumCircuitConfigSchema>;\n\n// Willow API Configuration\nexport const WillowConfigSchema = z.object({\n  endpoint: z.string().url(),\n  apiKey: z.string(),\n  projectId: z.string(),\n  region: z.enum(['us-central1', 'europe-west1', 'asia-east1']).default('us-central1'),\n  timeout: z.number().positive().default(30000),\n  retries: z.number().min(0).max(5).default(3),\n  circuitConfig: QuantumCircuitConfigSchema,\n});\n\nexport type WillowConfig = z.infer<typeof WillowConfigSchema>;\n\n// Error Types\nexport class QuantumOptimizationError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public details?: Record<string, any>\n  ) {\n    super(message);\n    this.name = 'QuantumOptimizationError';\n  }\n}\n\nexport class WillowAPIError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number,\n    public response?: any\n  ) {\n    super(message);\n    this.name = 'WillowAPIError';\n  }\n}\n\nexport class ClassicalOptimizationError extends Error {\n  constructor(\n    message: string,\n    public algorithm: string,\n    public details?: Record<string, any>\n  ) {\n    super(message);\n    this.name = 'ClassicalOptimizationError';\n  }\n}\n\n// Utility Types\nexport type Matrix = number[][];\nexport type Vector = number[];\n\nexport interface OptimizationMetrics {\n  objectiveValue: number;\n  constraintViolations: number;\n  feasibility: boolean;\n  optimality: number;\n  convergenceRate: number;\n}\n\nexport interface PerformanceMetrics {\n  computeTime: number;\n  memoryUsage: number;\n  iterations: number;\n  accuracy: number;\n  stability: number;\n}\n\n// Export all schemas for validation\nexport const Schemas = {\n  BudgetConstraint: BudgetConstraintSchema,\n  ChannelConfig: ChannelConfigSchema,\n  OptimizationObjective: OptimizationObjectiveSchema,\n  BudgetAllocation: BudgetAllocationSchema,\n  OptimizationRequest: OptimizationRequestSchema,\n  OptimizationResult: OptimizationResultSchema,\n  QuantumCircuitConfig: QuantumCircuitConfigSchema,\n  WillowConfig: WillowConfigSchema,\n} as const;\n", "import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { WillowConfig, QuantumCircuitConfig, WillowAPIError, OptimizationRequest } from '../types';\n\n/**\n * Google Willow Quantum Chip REST API Bridge\n * \n * This bridge interfaces with Google's Willow quantum computing platform\n * to perform quantum-enhanced budget optimization calculations.\n * \n * Willow Features:\n * - 105 qubits with breakthrough error correction\n * - Sub-millisecond coherence times\n * - Real-time error correction below threshold\n * - Quantum advantage for optimization problems\n */\nexport class WillowBridge {\n  private client: AxiosInstance;\n  private config: WillowConfig;\n  private circuitCache: Map<string, any> = new Map();\n\n  constructor(config: WillowConfig) {\n    this.config = config;\n    this.client = axios.create({\n      baseURL: config.endpoint,\n      timeout: config.timeout,\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json',\n        'X-Goog-User-Project': config.projectId,\n        'X-Quantum-Engine': 'willow-v1',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor for logging and validation\n    this.client.interceptors.request.use(\n      (config) => {\n        console.log(`[Willow] ${config.method?.toUpperCase()} ${config.url}`);\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor for error handling\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        const willowError = new WillowAPIError(\n          error.response?.data?.message || error.message,\n          error.response?.status || 500,\n          error.response?.data\n        );\n        return Promise.reject(willowError);\n      }\n    );\n  }\n\n  /**\n   * Check Willow quantum processor availability and status\n   */\n  async getProcessorStatus(): Promise<{\n    available: boolean;\n    qubits: number;\n    coherenceTime: number;\n    errorRate: number;\n    queueLength: number;\n    estimatedWaitTime: number;\n  }> {\n    try {\n      const response = await this.client.get('/v1/processors/willow/status');\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to get processor status',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Create a quantum circuit for budget optimization\n   */\n  async createOptimizationCircuit(\n    request: OptimizationRequest,\n    circuitConfig: QuantumCircuitConfig\n  ): Promise<string> {\n    const circuitKey = this.generateCircuitKey(request, circuitConfig);\n    \n    if (this.circuitCache.has(circuitKey)) {\n      return this.circuitCache.get(circuitKey);\n    }\n\n    const circuit = this.buildQuantumCircuit(request, circuitConfig);\n    \n    try {\n      const response = await this.client.post('/v1/circuits', {\n        name: `budget-optimization-${request.id}`,\n        circuit: circuit,\n        config: circuitConfig,\n        metadata: {\n          channels: request.channels.length,\n          constraints: request.constraints.length,\n          totalBudget: request.totalBudget,\n          objective: request.objective.primary,\n        },\n      });\n\n      const circuitId = response.data.circuitId;\n      this.circuitCache.set(circuitKey, circuitId);\n      return circuitId;\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to create quantum circuit',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Execute quantum optimization on Willow processor\n   */\n  async executeOptimization(\n    circuitId: string,\n    parameters: Record<string, number>\n  ): Promise<{\n    result: number[];\n    confidence: number;\n    quantumAdvantage: number;\n    metrics: {\n      gateCount: number;\n      depth: number;\n      errorRate: number;\n      coherenceTime: number;\n      executionTime: number;\n    };\n  }> {\n    try {\n      const response = await this.client.post(`/v1/circuits/${circuitId}/execute`, {\n        parameters,\n        shots: 10000, // Number of quantum measurements\n        errorCorrection: this.config.circuitConfig.errorCorrection,\n        optimization: {\n          level: 'aggressive',\n          errorMitigation: true,\n          readoutCorrection: true,\n        },\n      });\n\n      return this.processQuantumResult(response.data);\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to execute quantum optimization',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Run variational quantum eigensolver (VQE) for optimization\n   */\n  async runVQE(\n    hamiltonian: number[][],\n    initialParameters: number[],\n    maxIterations: number = 100\n  ): Promise<{\n    eigenvalue: number;\n    eigenvector: number[];\n    parameters: number[];\n    iterations: number;\n    convergence: boolean;\n  }> {\n    try {\n      const response = await this.client.post('/v1/algorithms/vqe', {\n        hamiltonian,\n        initialParameters,\n        maxIterations,\n        convergenceThreshold: 1e-6,\n        optimizer: 'COBYLA',\n        ansatz: 'hardware_efficient',\n      });\n\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'VQE execution failed',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Run Quantum Approximate Optimization Algorithm (QAOA)\n   */\n  async runQAOA(\n    costFunction: number[][],\n    mixingHamiltonian: number[][],\n    layers: number = 3\n  ): Promise<{\n    optimalParameters: number[];\n    expectationValue: number;\n    probability: number[];\n    iterations: number;\n  }> {\n    try {\n      const response = await this.client.post('/v1/algorithms/qaoa', {\n        costFunction,\n        mixingHamiltonian,\n        layers,\n        optimizer: 'NELDER_MEAD',\n        shots: 8192,\n      });\n\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'QAOA execution failed',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Get quantum processor metrics and utilization\n   */\n  async getProcessorMetrics(): Promise<{\n    utilization: number;\n    queueLength: number;\n    averageJobTime: number;\n    errorRates: Record<string, number>;\n    calibrationStatus: string;\n    temperature: number;\n  }> {\n    try {\n      const response = await this.client.get('/v1/processors/willow/metrics');\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to get processor metrics',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Build quantum circuit for budget optimization problem\n   */\n  private buildQuantumCircuit(\n    request: OptimizationRequest,\n    config: QuantumCircuitConfig\n  ): any {\n    const numChannels = request.channels.length;\n    const numQubits = Math.min(numChannels * 4, config.qubits); // 4 qubits per channel\n    \n    const circuit = {\n      qubits: numQubits,\n      gates: [] as any[],\n      measurements: [] as any[],\n    };\n\n    // Initialize superposition\n    for (let i = 0; i < numQubits; i++) {\n      circuit.gates.push({ type: 'H', qubit: i });\n    }\n\n    // Encode budget constraints as quantum gates\n    this.encodeConstraints(circuit, request.constraints, numChannels);\n    \n    // Encode optimization objective\n    this.encodeObjective(circuit, request.objective, numChannels);\n    \n    // Add entanglement for channel correlations\n    this.addEntanglement(circuit, numChannels);\n    \n    // Add measurements\n    for (let i = 0; i < numQubits; i++) {\n      circuit.measurements.push({ qubit: i, classical_bit: i });\n    }\n\n    return circuit;\n  }\n\n  private encodeConstraints(circuit: any, constraints: any[], numChannels: number): void {\n    constraints.forEach((constraint, idx) => {\n      if (constraint.type === 'total_budget') {\n        // Encode total budget constraint using controlled rotations\n        for (let i = 0; i < numChannels; i++) {\n          const angle = (constraint.value / 1000000) * Math.PI; // Normalize\n          circuit.gates.push({\n            type: 'RY',\n            qubit: i * 4,\n            parameter: angle,\n          });\n        }\n      }\n    });\n  }\n\n  private encodeObjective(circuit: any, objective: any, numChannels: number): void {\n    // Encode optimization objective as quantum phase rotations\n    const weight = objective.weights.primary;\n    \n    for (let i = 0; i < numChannels; i++) {\n      const phase = weight * Math.PI / 2;\n      circuit.gates.push({\n        type: 'RZ',\n        qubit: i * 4 + 1,\n        parameter: phase,\n      });\n    }\n  }\n\n  private addEntanglement(circuit: any, numChannels: number): void {\n    // Add CNOT gates for channel correlations\n    for (let i = 0; i < numChannels - 1; i++) {\n      circuit.gates.push({\n        type: 'CNOT',\n        control: i * 4,\n        target: (i + 1) * 4,\n      });\n    }\n  }\n\n  private processQuantumResult(rawResult: any): any {\n    const measurements = rawResult.measurements;\n    const counts = rawResult.counts;\n    \n    // Convert quantum measurements to budget allocations\n    const result = this.decodeMeasurements(measurements, counts);\n    \n    // Calculate quantum advantage metric\n    const quantumAdvantage = this.calculateQuantumAdvantage(rawResult);\n    \n    return {\n      result: result.allocations,\n      confidence: result.confidence,\n      quantumAdvantage,\n      metrics: {\n        gateCount: rawResult.circuitMetrics.gateCount,\n        depth: rawResult.circuitMetrics.depth,\n        errorRate: rawResult.errorMetrics.averageErrorRate,\n        coherenceTime: rawResult.coherenceMetrics.averageCoherenceTime,\n        executionTime: rawResult.timing.executionTime,\n      },\n    };\n  }\n\n  private decodeMeasurements(measurements: any, counts: any): any {\n    // Decode quantum measurements into budget allocations\n    const totalShots = Object.values(counts).reduce((a: number, b: any) => a + Number(b), 0);\n    const allocations: number[] = [];\n    let confidence = 0;\n\n    // Find most probable measurement outcome\n    let maxCount: number = 0;\n    let bestOutcome = '';\n\n    for (const [outcome, count] of Object.entries(counts)) {\n      const countNum = Number(count);\n      if (countNum > maxCount) {\n        maxCount = countNum;\n        bestOutcome = outcome;\n      }\n    }\n\n    confidence = maxCount / totalShots;\n    \n    // Convert binary outcome to budget allocations\n    const binaryString = bestOutcome;\n    const numChannels = binaryString.length / 4;\n    \n    for (let i = 0; i < numChannels; i++) {\n      const channelBits = binaryString.slice(i * 4, (i + 1) * 4);\n      const allocation = parseInt(channelBits, 2) / 15; // Normalize to [0,1]\n      allocations.push(allocation);\n    }\n\n    return { allocations, confidence };\n  }\n\n  private calculateQuantumAdvantage(result: any): number {\n    // Calculate quantum advantage based on coherence and error rates\n    const coherenceTime = result.coherenceMetrics.averageCoherenceTime;\n    const errorRate = result.errorMetrics.averageErrorRate;\n    const gateTime = result.timing.averageGateTime;\n    \n    // Quantum advantage when coherence time >> gate time and error rate is low\n    const coherenceAdvantage = Math.min(coherenceTime / (gateTime * 100), 1);\n    const errorAdvantage = Math.max(0, 1 - errorRate * 100);\n    \n    return (coherenceAdvantage + errorAdvantage) / 2;\n  }\n\n  private generateCircuitKey(request: OptimizationRequest, config: QuantumCircuitConfig): string {\n    const keyData = {\n      channels: request.channels.length,\n      constraints: request.constraints.length,\n      objective: request.objective.primary,\n      qubits: config.qubits,\n      depth: config.depth,\n    };\n    \n    return Buffer.from(JSON.stringify(keyData)).toString('base64');\n  }\n\n  /**\n   * Clean up resources and close connections\n   */\n  async cleanup(): Promise<void> {\n    this.circuitCache.clear();\n    // Additional cleanup if needed\n  }\n}\n", "import { Matrix } from 'ml-matrix';\nimport { evaluate } from 'mathjs';\nimport { cloneDeep, sum, maxBy, minBy } from 'lodash';\nimport {\n  OptimizationRequest,\n  OptimizationResult,\n  BudgetAllocation,\n  ClassicalOptimizationError,\n  OptimizationMetrics,\n  PerformanceMetrics,\n} from '../types';\n\n/**\n * Classical Budget Optimization Solver\n * \n * Implements multiple classical optimization algorithms as fallback\n * when quantum optimization is unavailable or suboptimal.\n * \n * Algorithms:\n * - Sequential Quadratic Programming (SQP)\n * - Genetic Algorithm (GA)\n * - Particle Swarm Optimization (PSO)\n * - Simulated Annealing (SA)\n * - Interior Point Method\n */\nexport class ClassicalSolver {\n  private maxIterations: number;\n  private convergenceThreshold: number;\n  private populationSize: number;\n\n  constructor(options: {\n    maxIterations?: number;\n    convergenceThreshold?: number;\n    populationSize?: number;\n  } = {}) {\n    this.maxIterations = options.maxIterations || 1000;\n    this.convergenceThreshold = options.convergenceThreshold || 1e-6;\n    this.populationSize = options.populationSize || 100;\n  }\n\n  /**\n   * Main optimization entry point\n   */\n  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Choose best algorithm based on problem characteristics\n      const algorithm = this.selectAlgorithm(request);\n      console.log(`[Classical] Using ${algorithm} algorithm`);\n\n      let result: OptimizationResult;\n      \n      switch (algorithm) {\n        case 'sqp':\n          result = await this.sequentialQuadraticProgramming(request);\n          break;\n        case 'genetic':\n          result = await this.geneticAlgorithm(request);\n          break;\n        case 'pso':\n          result = await this.particleSwarmOptimization(request);\n          break;\n        case 'simulated_annealing':\n          result = await this.simulatedAnnealing(request);\n          break;\n        case 'interior_point':\n          result = await this.interiorPointMethod(request);\n          break;\n        default:\n          throw new ClassicalOptimizationError(\n            `Unknown algorithm: ${algorithm}`,\n            algorithm\n          );\n      }\n\n      result.computeTime = Date.now() - startTime;\n      result.method = 'classical';\n      \n      return result;\n    } catch (error) {\n      throw new ClassicalOptimizationError(\n        `Classical optimization failed: ${error.message}`,\n        'unknown',\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Sequential Quadratic Programming (SQP)\n   * Best for smooth, differentiable problems with equality/inequality constraints\n   */\n  private async sequentialQuadraticProgramming(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    let x = this.getInitialSolution(request); // Initial budget allocation\n    let iteration = 0;\n    let converged = false;\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: 0,\n      constraintViolations: 0,\n      feasibility: false,\n      optimality: 0,\n      convergenceRate: 0,\n    };\n\n    while (iteration < this.maxIterations && !converged) {\n      // Calculate gradient and Hessian\n      const gradient = this.calculateGradient(x, request);\n      const hessian = this.calculateHessian(x, request);\n      \n      // Solve QP subproblem\n      const direction = this.solveQPSubproblem(gradient, hessian, x, request);\n      \n      // Line search\n      const stepSize = this.lineSearch(x, direction, request);\n      \n      // Update solution\n      const newX = x.map((xi, i) => xi + stepSize * direction[i]);\n      \n      // Check convergence\n      const change = this.calculateNorm(newX.map((xi, i) => xi - x[i]));\n      converged = change < this.convergenceThreshold;\n      \n      x = newX;\n      iteration++;\n      \n      // Update metrics\n      metrics.objectiveValue = this.evaluateObjective(x, request);\n      metrics.constraintViolations = this.evaluateConstraintViolations(x, request);\n    }\n\n    metrics.feasibility = metrics.constraintViolations < 1e-6;\n    metrics.optimality = converged ? 1.0 : 0.5;\n    metrics.convergenceRate = iteration / this.maxIterations;\n\n    return this.buildResult(request, x, metrics, iteration, 'sqp');\n  }\n\n  /**\n   * Genetic Algorithm\n   * Best for discrete, combinatorial problems with complex constraints\n   */\n  private async geneticAlgorithm(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    let population = this.initializePopulation(request, this.populationSize);\n    let generation = 0;\n    let bestSolution = population[0];\n    let bestFitness = this.evaluateFitness(bestSolution, request);\n\n    while (generation < this.maxIterations) {\n      // Evaluate fitness for all individuals\n      const fitness = population.map(individual => this.evaluateFitness(individual, request));\n      \n      // Find best individual\n      const maxFitnessIndex = fitness.indexOf(Math.max(...fitness));\n      if (fitness[maxFitnessIndex] > bestFitness) {\n        bestSolution = cloneDeep(population[maxFitnessIndex]);\n        bestFitness = fitness[maxFitnessIndex];\n      }\n\n      // Selection (tournament selection)\n      const parents = this.tournamentSelection(population, fitness, this.populationSize);\n      \n      // Crossover and mutation\n      const offspring = this.crossoverAndMutation(parents, request);\n      \n      // Replace population\n      population = offspring;\n      generation++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(bestSolution, request),\n      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),\n      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,\n      optimality: 0.8, // GA doesn't guarantee global optimum\n      convergenceRate: generation / this.maxIterations,\n    };\n\n    return this.buildResult(request, bestSolution, metrics, generation, 'genetic');\n  }\n\n  /**\n   * Particle Swarm Optimization\n   * Best for continuous optimization with multiple local optima\n   */\n  private async particleSwarmOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    const numParticles = this.populationSize;\n    \n    // Initialize particles\n    const particles = Array(numParticles).fill(null).map(() => ({\n      position: this.getInitialSolution(request),\n      velocity: Array(numChannels).fill(0).map(() => (Math.random() - 0.5) * 0.1),\n      bestPosition: [] as number[],\n      bestFitness: -Infinity,\n    }));\n\n    let globalBestPosition = particles[0].position;\n    let globalBestFitness = -Infinity;\n    let iteration = 0;\n\n    // PSO parameters\n    const w = 0.7; // Inertia weight\n    const c1 = 1.5; // Cognitive parameter\n    const c2 = 1.5; // Social parameter\n\n    while (iteration < this.maxIterations) {\n      for (const particle of particles) {\n        const fitness = this.evaluateFitness(particle.position, request);\n        \n        // Update personal best\n        if (fitness > particle.bestFitness) {\n          particle.bestPosition = cloneDeep(particle.position);\n          particle.bestFitness = fitness;\n        }\n        \n        // Update global best\n        if (fitness > globalBestFitness) {\n          globalBestPosition = cloneDeep(particle.position);\n          globalBestFitness = fitness;\n        }\n        \n        // Update velocity and position\n        for (let i = 0; i < numChannels; i++) {\n          const r1 = Math.random();\n          const r2 = Math.random();\n          \n          particle.velocity[i] = w * particle.velocity[i] +\n            c1 * r1 * (particle.bestPosition[i] - particle.position[i]) +\n            c2 * r2 * (globalBestPosition[i] - particle.position[i]);\n          \n          particle.position[i] += particle.velocity[i];\n          \n          // Ensure bounds\n          particle.position[i] = Math.max(0, Math.min(1, particle.position[i]));\n        }\n      }\n      \n      iteration++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(globalBestPosition, request),\n      constraintViolations: this.evaluateConstraintViolations(globalBestPosition, request),\n      feasibility: this.evaluateConstraintViolations(globalBestPosition, request) < 1e-6,\n      optimality: 0.85,\n      convergenceRate: iteration / this.maxIterations,\n    };\n\n    return this.buildResult(request, globalBestPosition, metrics, iteration, 'pso');\n  }\n\n  /**\n   * Simulated Annealing\n   * Best for discrete optimization with many local optima\n   */\n  private async simulatedAnnealing(request: OptimizationRequest): Promise<OptimizationResult> {\n    let currentSolution = this.getInitialSolution(request);\n    let currentEnergy = -this.evaluateFitness(currentSolution, request);\n    let bestSolution = cloneDeep(currentSolution);\n    let bestEnergy = currentEnergy;\n    \n    const initialTemp = 1000;\n    const finalTemp = 0.01;\n    const coolingRate = 0.95;\n    let temperature = initialTemp;\n    let iteration = 0;\n\n    while (temperature > finalTemp && iteration < this.maxIterations) {\n      // Generate neighbor solution\n      const neighbor = this.generateNeighbor(currentSolution, request);\n      const neighborEnergy = -this.evaluateFitness(neighbor, request);\n      \n      // Accept or reject neighbor\n      const deltaE = neighborEnergy - currentEnergy;\n      if (deltaE < 0 || Math.random() < Math.exp(-deltaE / temperature)) {\n        currentSolution = neighbor;\n        currentEnergy = neighborEnergy;\n        \n        if (currentEnergy < bestEnergy) {\n          bestSolution = cloneDeep(currentSolution);\n          bestEnergy = currentEnergy;\n        }\n      }\n      \n      temperature *= coolingRate;\n      iteration++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(bestSolution, request),\n      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),\n      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,\n      optimality: 0.75,\n      convergenceRate: iteration / this.maxIterations,\n    };\n\n    return this.buildResult(request, bestSolution, metrics, iteration, 'simulated_annealing');\n  }\n\n  /**\n   * Interior Point Method\n   * Best for convex optimization with inequality constraints\n   */\n  private async interiorPointMethod(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    let x = this.getInitialSolution(request);\n    let mu = 1.0; // Barrier parameter\n    let iteration = 0;\n    let converged = false;\n\n    while (iteration < this.maxIterations && !converged && mu > 1e-8) {\n      // Solve barrier subproblem\n      const result = this.solveBarrierSubproblem(x, mu, request);\n      x = result.solution;\n      converged = result.converged;\n      \n      // Update barrier parameter\n      mu *= 0.1;\n      iteration++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(x, request),\n      constraintViolations: this.evaluateConstraintViolations(x, request),\n      feasibility: this.evaluateConstraintViolations(x, request) < 1e-6,\n      optimality: converged ? 0.95 : 0.7,\n      convergenceRate: iteration / this.maxIterations,\n    };\n\n    return this.buildResult(request, x, metrics, iteration, 'interior_point');\n  }\n\n  // Helper methods...\n  private selectAlgorithm(request: OptimizationRequest): string {\n    const numChannels = request.channels.length;\n    const numConstraints = request.constraints.length;\n    const hasNonlinearConstraints = request.constraints.some(c => \n      c.type === 'roas_target' || c.type === 'cpa_target'\n    );\n\n    if (numChannels <= 5 && !hasNonlinearConstraints) {\n      return 'sqp'; // Small, smooth problems\n    } else if (numChannels > 20 || hasNonlinearConstraints) {\n      return 'genetic'; // Large or complex problems\n    } else if (request.objective.primary === 'maximize_roas') {\n      return 'pso'; // Continuous optimization\n    } else {\n      return 'interior_point'; // General case\n    }\n  }\n\n  private getInitialSolution(request: OptimizationRequest): number[] {\n    // Equal allocation as starting point\n    const numChannels = request.channels.length;\n    return Array(numChannels).fill(1 / numChannels);\n  }\n\n  private evaluateObjective(allocation: number[], request: OptimizationRequest): number {\n    let objective = 0;\n    const totalBudget = request.totalBudget;\n\n    allocation.forEach((weight, i) => {\n      const channel = request.channels[i];\n      const budget = weight * totalBudget;\n      \n      // Calculate expected revenue based on channel model\n      const conversions = this.calculateConversions(budget, channel);\n      const revenue = conversions * channel.averageOrderValue;\n      \n      switch (request.objective.primary) {\n        case 'maximize_revenue':\n          objective += revenue;\n          break;\n        case 'maximize_conversions':\n          objective += conversions;\n          break;\n        case 'minimize_cpa':\n          objective -= budget / Math.max(conversions, 1);\n          break;\n        case 'maximize_roas':\n          objective += revenue / Math.max(budget, 1);\n          break;\n      }\n    });\n\n    return objective;\n  }\n\n  private evaluateFitness(allocation: number[], request: OptimizationRequest): number {\n    const objective = this.evaluateObjective(allocation, request);\n    const violations = this.evaluateConstraintViolations(allocation, request);\n    \n    // Penalize constraint violations\n    return objective - 1000 * violations;\n  }\n\n  private evaluateConstraintViolations(allocation: number[], request: OptimizationRequest): number {\n    let violations = 0;\n    const totalBudget = request.totalBudget;\n\n    // Budget allocation must sum to 1\n    const allocationSum = sum(allocation);\n    violations += Math.abs(allocationSum - 1);\n\n    // Check individual constraints\n    request.constraints.forEach(constraint => {\n      switch (constraint.type) {\n        case 'total_budget':\n          const totalSpend = allocationSum * totalBudget;\n          if (constraint.operator === '<=' && totalSpend > constraint.value) {\n            violations += (totalSpend - constraint.value) / constraint.value;\n          }\n          break;\n        // Add other constraint types...\n      }\n    });\n\n    return violations;\n  }\n\n  private calculateConversions(budget: number, channel: any): number {\n    // Simplified conversion model with diminishing returns\n    const baseConversions = budget * channel.conversionRate / channel.baseRate;\n    const saturationFactor = channel.saturationPoint ? \n      Math.min(1, channel.saturationPoint / budget) : 1;\n    \n    return baseConversions * saturationFactor * channel.scalingFactor;\n  }\n\n  private buildResult(\n    request: OptimizationRequest,\n    allocation: number[],\n    metrics: OptimizationMetrics,\n    iterations: number,\n    algorithm: string\n  ): OptimizationResult {\n    const allocations: BudgetAllocation[] = allocation.map((weight, i) => {\n      const channel = request.channels[i];\n      const budget = weight * request.totalBudget;\n      const conversions = this.calculateConversions(budget, channel);\n      const revenue = conversions * channel.averageOrderValue;\n\n      return {\n        channelId: channel.id,\n        allocatedBudget: budget,\n        expectedConversions: conversions,\n        expectedRevenue: revenue,\n        expectedCPA: budget / Math.max(conversions, 1),\n        expectedROAS: revenue / Math.max(budget, 1),\n        confidence: 0.8, // Classical methods have good confidence\n        riskScore: 3, // Moderate risk\n      };\n    });\n\n    return {\n      id: `result_${Date.now()}`,\n      requestId: request.id,\n      status: metrics.feasibility ? 'success' : 'partial',\n      method: 'classical',\n      allocations,\n      totalAllocated: sum(allocations.map(a => a.allocatedBudget)),\n      expectedTotalRevenue: sum(allocations.map(a => a.expectedRevenue)),\n      expectedTotalConversions: sum(allocations.map(a => a.expectedConversions)),\n      overallROAS: sum(allocations.map(a => a.expectedRevenue)) / \n                   Math.max(sum(allocations.map(a => a.allocatedBudget)), 1),\n      overallCPA: sum(allocations.map(a => a.allocatedBudget)) / \n                  Math.max(sum(allocations.map(a => a.expectedConversions)), 1),\n      confidence: metrics.optimality,\n      riskScore: metrics.feasibility ? 2 : 6,\n      optimizationMetrics: {\n        iterations,\n        convergenceTime: 0, // Will be set by caller\n        objectiveValue: metrics.objectiveValue,\n        constraintViolations: [],\n      },\n      recommendations: [],\n      createdAt: new Date(),\n      computeTime: 0, // Will be set by caller\n    };\n  }\n\n  // Additional helper methods for specific algorithms...\n  private calculateGradient(x: number[], request: OptimizationRequest): number[] {\n    const h = 1e-8;\n    const gradient = new Array(x.length);\n    \n    for (let i = 0; i < x.length; i++) {\n      const xPlus = [...x];\n      const xMinus = [...x];\n      xPlus[i] += h;\n      xMinus[i] -= h;\n      \n      gradient[i] = (this.evaluateObjective(xPlus, request) - \n                    this.evaluateObjective(xMinus, request)) / (2 * h);\n    }\n    \n    return gradient;\n  }\n\n  private calculateHessian(x: number[], request: OptimizationRequest): number[][] {\n    const h = 1e-6;\n    const n = x.length;\n    const hessian = Array(n).fill(null).map(() => Array(n).fill(0));\n    \n    for (let i = 0; i < n; i++) {\n      for (let j = 0; j < n; j++) {\n        const xPP = [...x]; xPP[i] += h; xPP[j] += h;\n        const xPM = [...x]; xPM[i] += h; xPM[j] -= h;\n        const xMP = [...x]; xMP[i] -= h; xMP[j] += h;\n        const xMM = [...x]; xMM[i] -= h; xMM[j] -= h;\n        \n        hessian[i][j] = (\n          this.evaluateObjective(xPP, request) -\n          this.evaluateObjective(xPM, request) -\n          this.evaluateObjective(xMP, request) +\n          this.evaluateObjective(xMM, request)\n        ) / (4 * h * h);\n      }\n    }\n    \n    return hessian;\n  }\n\n  private solveQPSubproblem(gradient: number[], hessian: number[][], x: number[], request: OptimizationRequest): number[] {\n    // Simplified QP solver - in practice would use specialized library\n    const n = x.length;\n    const direction = new Array(n).fill(0);\n    \n    // Simple steepest descent direction\n    for (let i = 0; i < n; i++) {\n      direction[i] = -gradient[i];\n    }\n    \n    return direction;\n  }\n\n  private lineSearch(x: number[], direction: number[], request: OptimizationRequest): number {\n    let alpha = 1.0;\n    const c1 = 1e-4; // Armijo condition parameter\n    const rho = 0.5; // Backtracking parameter\n    \n    const f0 = this.evaluateObjective(x, request);\n    const grad0 = this.calculateGradient(x, request);\n    const directionalDerivative = sum(grad0.map((g, i) => g * direction[i]));\n    \n    while (alpha > 1e-8) {\n      const newX = x.map((xi, i) => xi + alpha * direction[i]);\n      const f1 = this.evaluateObjective(newX, request);\n      \n      if (f1 >= f0 + c1 * alpha * directionalDerivative) {\n        return alpha;\n      }\n      \n      alpha *= rho;\n    }\n    \n    return alpha;\n  }\n\n  private calculateNorm(vector: number[]): number {\n    return Math.sqrt(sum(vector.map(v => v * v)));\n  }\n\n  private initializePopulation(request: OptimizationRequest, size: number): number[][] {\n    const numChannels = request.channels.length;\n    const population: number[][] = [];\n    \n    for (let i = 0; i < size; i++) {\n      const individual = Array(numChannels).fill(0).map(() => Math.random());\n      const total = sum(individual);\n      // Normalize to sum to 1\n      population.push(individual.map(x => x / total));\n    }\n    \n    return population;\n  }\n\n  private tournamentSelection(population: number[][], fitness: number[], size: number): number[][] {\n    const selected: number[][] = [];\n    const tournamentSize = 3;\n    \n    for (let i = 0; i < size; i++) {\n      let best = Math.floor(Math.random() * population.length);\n      \n      for (let j = 1; j < tournamentSize; j++) {\n        const candidate = Math.floor(Math.random() * population.length);\n        if (fitness[candidate] > fitness[best]) {\n          best = candidate;\n        }\n      }\n      \n      selected.push(cloneDeep(population[best]));\n    }\n    \n    return selected;\n  }\n\n  private crossoverAndMutation(parents: number[][], request: OptimizationRequest): number[][] {\n    const offspring: number[][] = [];\n    const mutationRate = 0.1;\n    const crossoverRate = 0.8;\n    \n    for (let i = 0; i < parents.length; i += 2) {\n      let child1 = cloneDeep(parents[i]);\n      let child2 = cloneDeep(parents[i + 1] || parents[i]);\n      \n      // Crossover\n      if (Math.random() < crossoverRate) {\n        const crossoverPoint = Math.floor(Math.random() * child1.length);\n        for (let j = crossoverPoint; j < child1.length; j++) {\n          [child1[j], child2[j]] = [child2[j], child1[j]];\n        }\n      }\n      \n      // Mutation\n      if (Math.random() < mutationRate) {\n        const mutationPoint = Math.floor(Math.random() * child1.length);\n        child1[mutationPoint] += (Math.random() - 0.5) * 0.1;\n        child1[mutationPoint] = Math.max(0, child1[mutationPoint]);\n      }\n      \n      // Normalize\n      const total1 = sum(child1);\n      const total2 = sum(child2);\n      child1 = child1.map(x => x / total1);\n      child2 = child2.map(x => x / total2);\n      \n      offspring.push(child1, child2);\n    }\n    \n    return offspring.slice(0, parents.length);\n  }\n\n  private generateNeighbor(solution: number[], request: OptimizationRequest): number[] {\n    const neighbor = cloneDeep(solution);\n    const perturbationSize = 0.05;\n    \n    // Randomly perturb one or two elements\n    const numPerturbations = Math.random() < 0.5 ? 1 : 2;\n    \n    for (let i = 0; i < numPerturbations; i++) {\n      const index = Math.floor(Math.random() * neighbor.length);\n      neighbor[index] += (Math.random() - 0.5) * perturbationSize;\n      neighbor[index] = Math.max(0, neighbor[index]);\n    }\n    \n    // Normalize\n    const total = sum(neighbor);\n    return neighbor.map(x => x / total);\n  }\n\n  private solveBarrierSubproblem(x: number[], mu: number, request: OptimizationRequest): { solution: number[]; converged: boolean } {\n    // Simplified barrier method implementation\n    let currentX = cloneDeep(x);\n    let converged = false;\n    const maxInnerIterations = 50;\n    \n    for (let iter = 0; iter < maxInnerIterations; iter++) {\n      const gradient = this.calculateBarrierGradient(currentX, mu, request);\n      const stepSize = this.lineSearch(currentX, gradient.map(g => -g), request);\n      \n      const newX = currentX.map((xi, i) => xi - stepSize * gradient[i]);\n      \n      // Ensure feasibility\n      const total = sum(newX);\n      const normalizedX = newX.map(xi => Math.max(1e-8, xi / total));\n      \n      const change = this.calculateNorm(normalizedX.map((xi, i) => xi - currentX[i]));\n      if (change < this.convergenceThreshold) {\n        converged = true;\n        break;\n      }\n      \n      currentX = normalizedX;\n    }\n    \n    return { solution: currentX, converged };\n  }\n\n  private calculateBarrierGradient(x: number[], mu: number, request: OptimizationRequest): number[] {\n    const objectiveGradient = this.calculateGradient(x, request);\n    const barrierGradient = new Array(x.length);\n    \n    // Add barrier terms for bound constraints\n    for (let i = 0; i < x.length; i++) {\n      barrierGradient[i] = objectiveGradient[i] - mu / Math.max(x[i], 1e-8);\n    }\n    \n    return barrierGradient;\n  }\n}\n", "import { WillowBridge } from './willow-bridge';\nimport { ClassicalSolver } from '../classical/solver';\nimport {\n  OptimizationRequest,\n  OptimizationResult,\n  WillowConfig,\n  QuantumOptimizationError,\n  BudgetAllocation,\n} from '../types';\n\n/**\n * Quantum Budget Optimizer\n * \n * Main orchestrator that attempts quantum optimization first,\n * then falls back to classical methods if needed.\n * \n * Features:\n * - Automatic quantum/classical selection\n * - Hybrid optimization strategies\n * - Performance monitoring and comparison\n * - Adaptive algorithm selection\n */\nexport class QuantumOptimizer {\n  private willowBridge: WillowBridge;\n  private classicalSolver: ClassicalSolver;\n  private config: WillowConfig;\n  private performanceHistory: Map<string, number[]> = new Map();\n\n  constructor(config: WillowConfig) {\n    this.config = config;\n    this.willowBridge = new WillowBridge(config);\n    this.classicalSolver = new ClassicalSolver({\n      maxIterations: 1000,\n      convergenceThreshold: 1e-6,\n      populationSize: 100,\n    });\n  }\n\n  /**\n   * Main optimization entry point\n   */\n  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Determine optimization strategy\n      const strategy = await this.selectOptimizationStrategy(request);\n      console.log(`[Quantum] Using ${strategy} optimization strategy`);\n\n      let result: OptimizationResult;\n\n      switch (strategy) {\n        case 'quantum':\n          result = await this.quantumOptimization(request);\n          break;\n        case 'classical':\n          result = await this.classicalOptimization(request);\n          break;\n        case 'hybrid':\n          result = await this.hybridOptimization(request);\n          break;\n        default:\n          throw new QuantumOptimizationError(\n            `Unknown optimization strategy: ${strategy}`,\n            'INVALID_STRATEGY'\n          );\n      }\n\n      // Update performance history\n      this.updatePerformanceHistory(strategy, result.computeTime);\n\n      // Add quantum-specific recommendations\n      result.recommendations.push(...this.generateQuantumRecommendations(result));\n\n      return result;\n    } catch (error) {\n      console.error('[Quantum] Optimization failed, falling back to classical:', error);\n      \n      // Emergency fallback to classical\n      const fallbackResult = await this.classicalOptimization(request);\n      fallbackResult.status = 'partial';\n      fallbackResult.recommendations.unshift({\n        type: 'budget_increase',\n        description: 'Quantum optimization failed. Consider upgrading to ensure quantum advantage.',\n        impact: {\n          revenueChange: 0,\n          costChange: 0,\n          riskChange: 1,\n        },\n        priority: 'medium',\n      });\n\n      return fallbackResult;\n    }\n  }\n\n  /**\n   * Pure quantum optimization using Willow\n   */\n  private async quantumOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    console.log('[Quantum] Starting quantum optimization on Willow');\n\n    // Check processor availability\n    const status = await this.willowBridge.getProcessorStatus();\n    if (!status.available) {\n      throw new QuantumOptimizationError(\n        'Willow processor not available',\n        'PROCESSOR_UNAVAILABLE',\n        { status }\n      );\n    }\n\n    // Create quantum circuit\n    const circuitId = await this.willowBridge.createOptimizationCircuit(\n      request,\n      this.config.circuitConfig\n    );\n\n    // Prepare quantum parameters\n    const parameters = this.prepareQuantumParameters(request);\n\n    // Execute quantum optimization\n    const quantumResult = await this.willowBridge.executeOptimization(circuitId, parameters);\n\n    // Convert quantum result to budget allocations\n    const allocations = this.convertQuantumToBudgetAllocations(\n      quantumResult.result,\n      request\n    );\n\n    // Build optimization result\n    const result: OptimizationResult = {\n      id: `quantum_${Date.now()}`,\n      requestId: request.id,\n      status: 'success',\n      method: 'quantum',\n      allocations,\n      totalAllocated: allocations.reduce((sum, a) => sum + a.allocatedBudget, 0),\n      expectedTotalRevenue: allocations.reduce((sum, a) => sum + a.expectedRevenue, 0),\n      expectedTotalConversions: allocations.reduce((sum, a) => sum + a.expectedConversions, 0),\n      overallROAS: 0, // Will be calculated\n      overallCPA: 0, // Will be calculated\n      confidence: quantumResult.confidence,\n      riskScore: this.calculateQuantumRiskScore(quantumResult),\n      optimizationMetrics: {\n        iterations: 1, // Quantum is single-shot\n        convergenceTime: quantumResult.metrics.executionTime,\n        objectiveValue: this.calculateObjectiveValue(allocations, request),\n        constraintViolations: [],\n      },\n      quantumMetrics: {\n        quantumAdvantage: quantumResult.quantumAdvantage,\n        coherenceTime: quantumResult.metrics.coherenceTime,\n        gateCount: quantumResult.metrics.gateCount,\n        errorRate: quantumResult.metrics.errorRate,\n        willowChipUtilization: status.qubits / 105, // Willow has 105 qubits\n      },\n      recommendations: [],\n      createdAt: new Date(),\n      computeTime: quantumResult.metrics.executionTime,\n    };\n\n    // Calculate derived metrics\n    result.overallROAS = result.expectedTotalRevenue / Math.max(result.totalAllocated, 1);\n    result.overallCPA = result.totalAllocated / Math.max(result.expectedTotalConversions, 1);\n\n    return result;\n  }\n\n  /**\n   * Classical optimization fallback\n   */\n  private async classicalOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    console.log('[Quantum] Using classical optimization fallback');\n    return await this.classicalSolver.optimize(request);\n  }\n\n  /**\n   * Hybrid quantum-classical optimization\n   */\n  private async hybridOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    console.log('[Quantum] Starting hybrid optimization');\n\n    try {\n      // Start with quantum optimization for global exploration\n      const quantumResult = await this.quantumOptimization(request);\n\n      // Use quantum result as starting point for classical refinement\n      const refinedRequest = this.createRefinementRequest(request, quantumResult);\n      const classicalResult = await this.classicalSolver.optimize(refinedRequest);\n\n      // Combine results\n      const hybridResult = this.combineResults(quantumResult, classicalResult);\n      hybridResult.method = 'hybrid';\n\n      return hybridResult;\n    } catch (error) {\n      console.warn('[Quantum] Hybrid optimization failed, using classical only:', error);\n      return await this.classicalOptimization(request);\n    }\n  }\n\n  /**\n   * Select the best optimization strategy based on problem characteristics\n   */\n  private async selectOptimizationStrategy(request: OptimizationRequest): Promise<'quantum' | 'classical' | 'hybrid'> {\n    const numChannels = request.channels.length;\n    const numConstraints = request.constraints.length;\n    const totalBudget = request.totalBudget;\n\n    // Check if quantum optimization is preferred\n    if (!request.preferences.useQuantumOptimization) {\n      return 'classical';\n    }\n\n    // Check Willow availability\n    try {\n      const status = await this.willowBridge.getProcessorStatus();\n      if (!status.available || status.queueLength > 10) {\n        return 'classical';\n      }\n\n      // Quantum advantage criteria\n      const quantumAdvantageScore = this.calculateQuantumAdvantageScore(request);\n      \n      if (quantumAdvantageScore > 0.8) {\n        return 'quantum';\n      } else if (quantumAdvantageScore > 0.5) {\n        return 'hybrid';\n      } else {\n        return 'classical';\n      }\n    } catch (error) {\n      console.warn('[Quantum] Cannot access Willow, using classical:', error);\n      return 'classical';\n    }\n  }\n\n  /**\n   * Calculate quantum advantage score for the problem\n   */\n  private calculateQuantumAdvantageScore(request: OptimizationRequest): number {\n    let score = 0;\n\n    // Problem size factor (quantum advantage increases with problem size)\n    const numChannels = request.channels.length;\n    if (numChannels >= 10) score += 0.3;\n    if (numChannels >= 20) score += 0.2;\n\n    // Constraint complexity (quantum handles complex constraints better)\n    const complexConstraints = request.constraints.filter(c => \n      c.type === 'roas_target' || c.type === 'cpa_target'\n    ).length;\n    score += Math.min(complexConstraints * 0.15, 0.3);\n\n    // Optimization objective (some objectives benefit more from quantum)\n    if (request.objective.primary === 'maximize_roas') score += 0.2;\n    if (request.objective.secondary) score += 0.1;\n\n    // Historical performance\n    const quantumHistory = this.performanceHistory.get('quantum') || [];\n    const classicalHistory = this.performanceHistory.get('classical') || [];\n    \n    if (quantumHistory.length > 0 && classicalHistory.length > 0) {\n      const avgQuantumTime = quantumHistory.reduce((a, b) => a + b) / quantumHistory.length;\n      const avgClassicalTime = classicalHistory.reduce((a, b) => a + b) / classicalHistory.length;\n      \n      if (avgQuantumTime < avgClassicalTime) score += 0.2;\n    }\n\n    return Math.min(score, 1.0);\n  }\n\n  /**\n   * Prepare parameters for quantum circuit\n   */\n  private prepareQuantumParameters(request: OptimizationRequest): Record<string, number> {\n    const parameters: Record<string, number> = {};\n\n    // Budget constraints\n    parameters.totalBudget = request.totalBudget / 1000000; // Normalize\n    \n    // Channel parameters\n    request.channels.forEach((channel, i) => {\n      parameters[`channel_${i}_min`] = channel.minBudget / request.totalBudget;\n      parameters[`channel_${i}_max`] = channel.maxBudget / request.totalBudget;\n      parameters[`channel_${i}_conversion_rate`] = channel.conversionRate;\n      parameters[`channel_${i}_aov`] = channel.averageOrderValue / 1000; // Normalize\n    });\n\n    // Objective weights\n    parameters.primary_weight = request.objective.weights.primary;\n    parameters.secondary_weight = request.objective.weights.secondary || 0;\n\n    return parameters;\n  }\n\n  /**\n   * Convert quantum measurement results to budget allocations\n   */\n  private convertQuantumToBudgetAllocations(\n    quantumResult: number[],\n    request: OptimizationRequest\n  ): BudgetAllocation[] {\n    const allocations: BudgetAllocation[] = [];\n    const totalBudget = request.totalBudget;\n\n    // Normalize quantum results to valid budget allocations\n    const sum = quantumResult.reduce((a, b) => a + b, 0);\n    const normalizedWeights = quantumResult.map(w => w / sum);\n\n    request.channels.forEach((channel, i) => {\n      const weight = normalizedWeights[i] || 0;\n      const budget = weight * totalBudget;\n      \n      // Ensure budget is within channel constraints\n      const constrainedBudget = Math.max(\n        channel.minBudget,\n        Math.min(channel.maxBudget, budget)\n      );\n\n      // Calculate expected performance\n      const conversions = this.calculateExpectedConversions(constrainedBudget, channel);\n      const revenue = conversions * channel.averageOrderValue;\n\n      allocations.push({\n        channelId: channel.id,\n        allocatedBudget: constrainedBudget,\n        expectedConversions: conversions,\n        expectedRevenue: revenue,\n        expectedCPA: constrainedBudget / Math.max(conversions, 1),\n        expectedROAS: revenue / Math.max(constrainedBudget, 1),\n        confidence: 0.9, // Quantum typically has high confidence\n        riskScore: 2, // Quantum optimization is generally low risk\n      });\n    });\n\n    return allocations;\n  }\n\n  /**\n   * Calculate expected conversions for a channel given budget\n   */\n  private calculateExpectedConversions(budget: number, channel: any): number {\n    // Simplified model with diminishing returns\n    const baseConversions = budget * channel.conversionRate / channel.baseRate;\n    \n    // Apply saturation curve\n    let saturationFactor = 1;\n    if (channel.saturationPoint && budget > channel.saturationPoint) {\n      saturationFactor = Math.sqrt(channel.saturationPoint / budget);\n    }\n    \n    return baseConversions * saturationFactor * channel.scalingFactor;\n  }\n\n  /**\n   * Calculate quantum-specific risk score\n   */\n  private calculateQuantumRiskScore(quantumResult: any): number {\n    let riskScore = 2; // Base low risk for quantum\n\n    // Increase risk if error rate is high\n    if (quantumResult.metrics.errorRate > 0.01) {\n      riskScore += 2;\n    }\n\n    // Increase risk if coherence time is low\n    if (quantumResult.metrics.coherenceTime < 50) {\n      riskScore += 1;\n    }\n\n    // Decrease risk if quantum advantage is high\n    if (quantumResult.quantumAdvantage > 0.8) {\n      riskScore -= 1;\n    }\n\n    return Math.max(1, Math.min(10, riskScore));\n  }\n\n  /**\n   * Calculate objective value for allocations\n   */\n  private calculateObjectiveValue(allocations: BudgetAllocation[], request: OptimizationRequest): number {\n    let value = 0;\n\n    switch (request.objective.primary) {\n      case 'maximize_revenue':\n        value = allocations.reduce((sum, a) => sum + a.expectedRevenue, 0);\n        break;\n      case 'maximize_conversions':\n        value = allocations.reduce((sum, a) => sum + a.expectedConversions, 0);\n        break;\n      case 'minimize_cpa':\n        const totalCPA = allocations.reduce((sum, a) => sum + a.expectedCPA, 0) / allocations.length;\n        value = -totalCPA; // Negative because we want to minimize\n        break;\n      case 'maximize_roas':\n        const totalROAS = allocations.reduce((sum, a) => sum + a.expectedROAS, 0) / allocations.length;\n        value = totalROAS;\n        break;\n    }\n\n    return value;\n  }\n\n  /**\n   * Generate quantum-specific recommendations\n   */\n  private generateQuantumRecommendations(result: OptimizationResult): any[] {\n    const recommendations: any[] = [];\n\n    // Quantum advantage recommendation\n    if (result.quantumMetrics?.quantumAdvantage && result.quantumMetrics.quantumAdvantage > 0.8) {\n      recommendations.push({\n        type: 'budget_increase',\n        description: 'High quantum advantage detected. Consider increasing budget to maximize quantum benefits.',\n        impact: {\n          revenueChange: result.expectedTotalRevenue * 0.15,\n          costChange: result.totalAllocated * 0.1,\n          riskChange: -0.5,\n        },\n        priority: 'high',\n      });\n    }\n\n    // Error rate recommendation\n    if (result.quantumMetrics?.errorRate && result.quantumMetrics.errorRate > 0.01) {\n      recommendations.push({\n        type: 'timing_adjustment',\n        description: 'Higher than optimal quantum error rate. Consider running optimization during off-peak hours.',\n        impact: {\n          revenueChange: result.expectedTotalRevenue * 0.05,\n          costChange: 0,\n          riskChange: -1,\n        },\n        priority: 'medium',\n      });\n    }\n\n    return recommendations;\n  }\n\n  /**\n   * Create refinement request for hybrid optimization\n   */\n  private createRefinementRequest(original: OptimizationRequest, quantumResult: OptimizationResult): OptimizationRequest {\n    // Use quantum result to create tighter constraints for classical refinement\n    const refinedRequest = { ...original };\n    \n    // Add budget allocation constraints based on quantum result\n    quantumResult.allocations.forEach((allocation, i) => {\n      const tolerance = 0.2; // 20% tolerance around quantum solution\n      const minBudget = allocation.allocatedBudget * (1 - tolerance);\n      const maxBudget = allocation.allocatedBudget * (1 + tolerance);\n      \n      refinedRequest.constraints.push({\n        id: `quantum_constraint_${i}`,\n        type: 'channel_budget',\n        value: maxBudget,\n        operator: '<=',\n        priority: 7,\n        flexible: true,\n        tolerance: 0.1,\n      });\n    });\n\n    return refinedRequest;\n  }\n\n  /**\n   * Combine quantum and classical results\n   */\n  private combineResults(quantumResult: OptimizationResult, classicalResult: OptimizationResult): OptimizationResult {\n    // Use quantum result as base and incorporate classical improvements\n    const combined = { ...quantumResult };\n    \n    // Take the better objective value\n    if (classicalResult.optimizationMetrics.objectiveValue > quantumResult.optimizationMetrics.objectiveValue) {\n      combined.allocations = classicalResult.allocations;\n      combined.totalAllocated = classicalResult.totalAllocated;\n      combined.expectedTotalRevenue = classicalResult.expectedTotalRevenue;\n      combined.expectedTotalConversions = classicalResult.expectedTotalConversions;\n      combined.overallROAS = classicalResult.overallROAS;\n      combined.overallCPA = classicalResult.overallCPA;\n    }\n\n    // Combine confidence scores\n    combined.confidence = (quantumResult.confidence + classicalResult.confidence) / 2;\n    \n    // Take minimum risk score\n    combined.riskScore = Math.min(quantumResult.riskScore, classicalResult.riskScore);\n\n    return combined;\n  }\n\n  /**\n   * Update performance history for algorithm selection\n   */\n  private updatePerformanceHistory(strategy: string, computeTime: number): void {\n    if (!this.performanceHistory.has(strategy)) {\n      this.performanceHistory.set(strategy, []);\n    }\n    \n    const history = this.performanceHistory.get(strategy)!;\n    history.push(computeTime);\n    \n    // Keep only last 10 results\n    if (history.length > 10) {\n      history.shift();\n    }\n  }\n\n  /**\n   * Clean up resources\n   */\n  async cleanup(): Promise<void> {\n    await this.willowBridge.cleanup();\n    this.performanceHistory.clear();\n  }\n}\n", "import { QuantumOptimizer } from '../quantum/optimizer';\nimport {\n  OptimizationRequest,\n  OptimizationR<PERSON>ult,\n  WillowConfig,\n  Schemas,\n  QuantumOptimizationError,\n} from '../types';\n\n/**\n * Utility functions for the optimizer package\n */\n\n/**\n * Create a configured optimizer instance\n */\nexport function createOptimizer(config?: Partial<WillowConfig>): QuantumOptimizer {\n  const defaultConfig: WillowConfig = {\n    endpoint: process.env.WILLOW_ENDPOINT || 'https://quantum.googleapis.com',\n    apiKey: process.env.WILLOW_API_KEY || '',\n    projectId: process.env.GOOGLE_CLOUD_PROJECT || '',\n    region: 'us-central1',\n    timeout: 30000,\n    retries: 3,\n    circuitConfig: {\n      qubits: 20,\n      depth: 100,\n      gateSet: ['H', 'X', 'Y', 'Z', 'CNOT', 'CZ', 'RX', 'RY', 'RZ'],\n      errorCorrection: true,\n      coherenceTime: 100,\n      fidelity: 0.999,\n    },\n  };\n\n  const mergedConfig = { ...defaultConfig, ...config };\n  \n  // Validate required fields\n  if (!mergedConfig.apiKey) {\n    throw new QuantumOptimizationError(\n      'Willow API key is required',\n      'MISSING_API_KEY'\n    );\n  }\n\n  if (!mergedConfig.projectId) {\n    throw new QuantumOptimizationError(\n      'Google Cloud project ID is required',\n      'MISSING_PROJECT_ID'\n    );\n  }\n\n  return new QuantumOptimizer(mergedConfig);\n}\n\n/**\n * Validate optimization request\n */\nexport function validateRequest(request: OptimizationRequest): {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n} {\n  const errors: string[] = [];\n  const warnings: string[] = [];\n\n  try {\n    // Use Zod schema validation\n    Schemas.OptimizationRequest.parse(request);\n  } catch (error: any) {\n    if (error.errors) {\n      errors.push(...error.errors.map((e: any) => `${e.path.join('.')}: ${e.message}`));\n    } else {\n      errors.push(error.message);\n    }\n  }\n\n  // Additional business logic validation\n  if (request.channels.length === 0) {\n    errors.push('At least one channel is required');\n  }\n\n  if (request.channels.length > 50) {\n    warnings.push('Large number of channels may impact performance');\n  }\n\n  // Check budget constraints\n  const totalMinBudget = request.channels.reduce((sum, c) => sum + c.minBudget, 0);\n  if (totalMinBudget > request.totalBudget) {\n    errors.push('Total minimum budget exceeds available budget');\n  }\n\n  // Check for conflicting constraints\n  const budgetConstraints = request.constraints.filter(c => c.type === 'total_budget');\n  if (budgetConstraints.length > 1) {\n    warnings.push('Multiple total budget constraints detected');\n  }\n\n  // Validate timeframe\n  if (request.timeframe.end <= request.timeframe.start) {\n    errors.push('End date must be after start date');\n  }\n\n  const timeframeDays = (request.timeframe.end.getTime() - request.timeframe.start.getTime()) / (1000 * 60 * 60 * 24);\n  if (timeframeDays > 365) {\n    warnings.push('Long timeframes may reduce optimization accuracy');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors,\n    warnings,\n  };\n}\n\n/**\n * Format optimization result for display\n */\nexport function formatResult(result: OptimizationResult): {\n  summary: string;\n  allocations: Array<{\n    channel: string;\n    budget: string;\n    percentage: string;\n    expectedROAS: string;\n    expectedCPA: string;\n    confidence: string;\n  }>;\n  metrics: {\n    totalBudget: string;\n    expectedRevenue: string;\n    overallROAS: string;\n    overallCPA: string;\n    confidence: string;\n    riskScore: string;\n    method: string;\n    computeTime: string;\n  };\n  recommendations: string[];\n} {\n  const formatCurrency = (amount: number) => \n    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);\n  \n  const formatPercentage = (value: number) => \n    new Intl.NumberFormat('en-US', { style: 'percent', minimumFractionDigits: 1 }).format(value);\n\n  const summary = `${result.method.toUpperCase()} optimization ${result.status} with ${formatCurrency(result.expectedTotalRevenue)} expected revenue and ${formatPercentage(result.confidence)} confidence.`;\n\n  const allocations = result.allocations.map(allocation => ({\n    channel: allocation.channelId,\n    budget: formatCurrency(allocation.allocatedBudget),\n    percentage: formatPercentage(allocation.allocatedBudget / result.totalAllocated),\n    expectedROAS: allocation.expectedROAS.toFixed(2) + 'x',\n    expectedCPA: formatCurrency(allocation.expectedCPA),\n    confidence: formatPercentage(allocation.confidence),\n  }));\n\n  const metrics = {\n    totalBudget: formatCurrency(result.totalAllocated),\n    expectedRevenue: formatCurrency(result.expectedTotalRevenue),\n    overallROAS: result.overallROAS.toFixed(2) + 'x',\n    overallCPA: formatCurrency(result.overallCPA),\n    confidence: formatPercentage(result.confidence),\n    riskScore: `${result.riskScore}/10`,\n    method: result.method.toUpperCase(),\n    computeTime: `${result.computeTime}ms`,\n  };\n\n  const recommendations = result.recommendations.map(rec => \n    `${rec.priority.toUpperCase()}: ${rec.description}`\n  );\n\n  return {\n    summary,\n    allocations,\n    metrics,\n    recommendations,\n  };\n}\n\n/**\n * Calculate portfolio risk metrics\n */\nexport function calculatePortfolioRisk(result: OptimizationResult): {\n  diversificationScore: number;\n  concentrationRisk: number;\n  volatilityScore: number;\n  overallRisk: number;\n} {\n  const allocations = result.allocations;\n  const weights = allocations.map(a => a.allocatedBudget / result.totalAllocated);\n\n  // Diversification score (higher is better)\n  const diversificationScore = 1 - calculateHerfindahlIndex(weights);\n\n  // Concentration risk (percentage in largest allocation)\n  const concentrationRisk = Math.max(...weights);\n\n  // Volatility score based on risk scores\n  const avgRiskScore = allocations.reduce((sum, a) => sum + a.riskScore, 0) / allocations.length;\n  const volatilityScore = avgRiskScore / 10;\n\n  // Overall risk (0-1, lower is better)\n  const overallRisk = (concentrationRisk + volatilityScore + (1 - diversificationScore)) / 3;\n\n  return {\n    diversificationScore,\n    concentrationRisk,\n    volatilityScore,\n    overallRisk,\n  };\n}\n\n/**\n * Calculate Herfindahl-Hirschman Index for concentration\n */\nfunction calculateHerfindahlIndex(weights: number[]): number {\n  return weights.reduce((sum, weight) => sum + weight * weight, 0);\n}\n\n/**\n * Generate optimization insights\n */\nexport function generateInsights(result: OptimizationResult): {\n  topPerformers: string[];\n  underperformers: string[];\n  opportunities: string[];\n  risks: string[];\n} {\n  const allocations = result.allocations;\n  \n  // Sort by ROAS\n  const sortedByROAS = [...allocations].sort((a, b) => b.expectedROAS - a.expectedROAS);\n  \n  const topPerformers = sortedByROAS\n    .slice(0, Math.ceil(allocations.length * 0.3))\n    .map(a => a.channelId);\n\n  const underperformers = sortedByROAS\n    .slice(-Math.ceil(allocations.length * 0.3))\n    .filter(a => a.expectedROAS < result.overallROAS)\n    .map(a => a.channelId);\n\n  const opportunities: string[] = [];\n  const risks: string[] = [];\n\n  allocations.forEach(allocation => {\n    if (allocation.confidence < 0.7) {\n      risks.push(`Low confidence in ${allocation.channelId} allocation`);\n    }\n    \n    if (allocation.riskScore > 7) {\n      risks.push(`High risk detected for ${allocation.channelId}`);\n    }\n    \n    if (allocation.expectedROAS > result.overallROAS * 1.5) {\n      opportunities.push(`Consider increasing budget for high-performing ${allocation.channelId}`);\n    }\n    \n    if (allocation.allocatedBudget / result.totalAllocated > 0.5) {\n      risks.push(`High concentration risk in ${allocation.channelId}`);\n    }\n  });\n\n  return {\n    topPerformers,\n    underperformers,\n    opportunities,\n    risks,\n  };\n}\n\n/**\n * Export optimization result to various formats\n */\nexport function exportResult(result: OptimizationResult, format: 'json' | 'csv' | 'xlsx'): string | Buffer {\n  switch (format) {\n    case 'json':\n      return JSON.stringify(result, null, 2);\n    \n    case 'csv':\n      const headers = ['Channel', 'Budget', 'Expected Revenue', 'Expected Conversions', 'CPA', 'ROAS', 'Confidence', 'Risk Score'];\n      const rows = result.allocations.map(a => [\n        a.channelId,\n        a.allocatedBudget.toString(),\n        a.expectedRevenue.toString(),\n        a.expectedConversions.toString(),\n        a.expectedCPA.toString(),\n        a.expectedROAS.toString(),\n        a.confidence.toString(),\n        a.riskScore.toString(),\n      ]);\n      \n      return [headers, ...rows].map(row => row.join(',')).join('\\n');\n    \n    case 'xlsx':\n      // In a real implementation, you'd use a library like xlsx\n      throw new Error('XLSX export not implemented in this example');\n    \n    default:\n      throw new Error(`Unsupported export format: ${format}`);\n  }\n}\n\n/**\n * Compare two optimization results\n */\nexport function compareResults(result1: OptimizationResult, result2: OptimizationResult): {\n  revenueDifference: number;\n  roasDifference: number;\n  cpaDifference: number;\n  confidenceDifference: number;\n  riskDifference: number;\n  betterResult: 'result1' | 'result2' | 'similar';\n  summary: string;\n} {\n  const revenueDifference = result1.expectedTotalRevenue - result2.expectedTotalRevenue;\n  const roasDifference = result1.overallROAS - result2.overallROAS;\n  const cpaDifference = result1.overallCPA - result2.overallCPA;\n  const confidenceDifference = result1.confidence - result2.confidence;\n  const riskDifference = result1.riskScore - result2.riskScore;\n\n  let betterResult: 'result1' | 'result2' | 'similar' = 'similar';\n  let score1 = 0;\n  let score2 = 0;\n\n  // Score based on multiple criteria\n  if (revenueDifference > 0) score1++; else if (revenueDifference < 0) score2++;\n  if (roasDifference > 0) score1++; else if (roasDifference < 0) score2++;\n  if (cpaDifference < 0) score1++; else if (cpaDifference > 0) score2++; // Lower CPA is better\n  if (confidenceDifference > 0) score1++; else if (confidenceDifference < 0) score2++;\n  if (riskDifference < 0) score1++; else if (riskDifference > 0) score2++; // Lower risk is better\n\n  if (score1 > score2) betterResult = 'result1';\n  else if (score2 > score1) betterResult = 'result2';\n\n  const summary = `Result comparison: ${betterResult === 'similar' ? 'Results are similar' : \n    betterResult === 'result1' ? 'First result is better' : 'Second result is better'} \n    (Revenue: ${revenueDifference > 0 ? '+' : ''}${revenueDifference.toFixed(0)}, \n    ROAS: ${roasDifference > 0 ? '+' : ''}${roasDifference.toFixed(2)})`;\n\n  return {\n    revenueDifference,\n    roasDifference,\n    cpaDifference,\n    confidenceDifference,\n    riskDifference,\n    betterResult,\n    summary,\n  };\n}\n", "/**\n * @metamorphic-flux/optimizer\n * \n * Quantum-enhanced budget optimization with Google Willow integration\n * and classical fallback algorithms.\n * \n * Features:\n * - Google Willow quantum processor integration\n * - Multiple classical optimization algorithms\n * - Hybrid quantum-classical optimization\n * - Automatic algorithm selection\n * - Performance monitoring and comparison\n * - Enterprise-grade error handling\n */\n\n// Main exports\nexport { QuantumOptimizer } from './quantum/optimizer';\nexport { Willow<PERSON>ridge } from './quantum/willow-bridge';\nexport { ClassicalSolver } from './classical/solver';\n\n// Type exports\nexport type {\n  OptimizationRequest,\n  OptimizationResult,\n  BudgetConstraint,\n  ChannelConfig,\n  OptimizationObjective,\n  BudgetAllocation,\n  WillowConfig,\n  QuantumCircuitConfig,\n  Matrix,\n  Vector,\n  OptimizationMetrics,\n  PerformanceMetrics,\n} from './types';\n\n// Error exports\nexport {\n  QuantumOptimizationError,\n  WillowAPIError,\n  ClassicalOptimizationError,\n} from './types';\n\n// Schema exports for validation\nexport { Schemas } from './types';\n\n// Utility functions\nexport { createOptimizer, validateRequest, formatResult } from './utils/index';\n\n// Import types for internal use\nimport type {\n  OptimizationRequest,\n  OptimizationResult,\n  ChannelConfig,\n  WillowConfig,\n} from './types';\nimport { QuantumOptimizer } from './quantum/optimizer';\nimport { WillowBridge } from './quantum/willow-bridge';\nimport { ClassicalSolver } from './classical/solver';\n\n// Constants\nexport const QUANTUM_CONSTANTS = {\n  WILLOW_MAX_QUBITS: 105,\n  DEFAULT_COHERENCE_TIME: 100, // microseconds\n  DEFAULT_ERROR_THRESHOLD: 0.001,\n  MAX_CIRCUIT_DEPTH: 1000,\n  DEFAULT_SHOTS: 10000,\n} as const;\n\nexport const OPTIMIZATION_ALGORITHMS = {\n  QUANTUM: ['vqe', 'qaoa', 'quantum_annealing'] as const,\n  CLASSICAL: ['sqp', 'genetic', 'pso', 'simulated_annealing', 'interior_point'] as const,\n  HYBRID: ['quantum_classical', 'adaptive'] as const,\n} as const;\n\n// Version\nexport const VERSION = '1.0.0';\n\n/**\n * Quick start function for simple optimization\n */\nexport async function optimizeBudget(\n  totalBudget: number,\n  channels: any[],\n  objective: 'maximize_revenue' | 'maximize_conversions' | 'minimize_cpa' | 'maximize_roas' = 'maximize_revenue',\n  willowConfig?: any\n): Promise<any> {\n  const { createOptimizer } = await import('./utils/index');\n  const optimizer = createOptimizer(willowConfig);\n\n  const request = {\n    id: `quick_${Date.now()}`,\n    totalBudget,\n    channels,\n    constraints: [\n      {\n        id: 'total_budget',\n        type: 'total_budget' as const,\n        value: totalBudget,\n        operator: '<=' as const,\n        priority: 10,\n        flexible: false,\n        tolerance: 0,\n      },\n    ],\n    objective: {\n      primary: objective,\n      weights: { primary: 1.0, secondary: 0.0 },\n      timeHorizon: 'monthly' as const,\n    },\n    timeframe: {\n      start: new Date(),\n      end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days\n    },\n    preferences: {\n      useQuantumOptimization: true,\n      quantumFallbackThreshold: 0.95,\n      maxIterations: 1000,\n      convergenceThreshold: 0.001,\n      riskTolerance: 'moderate' as const,\n    },\n  };\n\n  return await optimizer.optimize(request);\n}\n\n/**\n * Batch optimization for multiple scenarios\n */\nexport async function optimizeBatch(\n  requests: any[],\n  willowConfig?: any\n): Promise<any[]> {\n  const { createOptimizer } = await import('./utils/index');\n  const optimizer = createOptimizer(willowConfig);\n  const results: any[] = [];\n\n  for (const request of requests) {\n    try {\n      const result = await optimizer.optimize(request);\n      results.push(result);\n    } catch (error) {\n      console.error(`Batch optimization failed for request ${request.id}:`, error);\n      // Continue with other requests\n    }\n  }\n\n  await optimizer.cleanup();\n  return results;\n}\n\n/**\n * Compare quantum vs classical performance\n */\nexport async function compareOptimizationMethods(\n  request: any,\n  willowConfig?: any\n): Promise<{\n  quantum: any | null;\n  classical: any;\n  comparison: {\n    quantumAdvantage: number;\n    performanceDifference: number;\n    recommendation: 'quantum' | 'classical' | 'hybrid';\n  };\n}> {\n  const { createOptimizer } = await import('./utils/index');\n  const { ClassicalSolver } = await import('./classical/solver');\n  const optimizer = createOptimizer(willowConfig);\n  const classicalSolver = new ClassicalSolver();\n\n  // Run classical optimization\n  const classicalResult = await classicalSolver.optimize(request);\n\n  // Attempt quantum optimization\n  let quantumResult: any | null = null;\n  try {\n    const quantumRequest = { ...request, preferences: { ...request.preferences, useQuantumOptimization: true } };\n    quantumResult = await optimizer.optimize(quantumRequest);\n  } catch (error) {\n    console.warn('Quantum optimization failed during comparison:', error);\n  }\n\n  // Calculate comparison metrics\n  let quantumAdvantage = 0;\n  let performanceDifference = 0;\n  let recommendation: 'quantum' | 'classical' | 'hybrid' = 'classical';\n\n  if (quantumResult) {\n    quantumAdvantage = quantumResult.quantumMetrics?.quantumAdvantage || 0;\n    \n    const quantumObjective = quantumResult.optimizationMetrics.objectiveValue;\n    const classicalObjective = classicalResult.optimizationMetrics.objectiveValue;\n    \n    performanceDifference = (quantumObjective - classicalObjective) / Math.abs(classicalObjective);\n    \n    if (performanceDifference > 0.1 && quantumAdvantage > 0.7) {\n      recommendation = 'quantum';\n    } else if (performanceDifference > 0.05 || quantumAdvantage > 0.5) {\n      recommendation = 'hybrid';\n    }\n  }\n\n  await optimizer.cleanup();\n\n  return {\n    quantum: quantumResult,\n    classical: classicalResult,\n    comparison: {\n      quantumAdvantage,\n      performanceDifference,\n      recommendation,\n    },\n  };\n}\n\n/**\n * Real-time optimization monitoring\n */\nexport class OptimizationMonitor {\n  private optimizer: any;\n  private isMonitoring = false;\n  private monitoringInterval?: NodeJS.Timeout;\n\n  constructor(willowConfig?: any) {\n    // Lazy initialization to avoid circular imports\n    this.optimizer = null as any;\n    this.initializeOptimizer(willowConfig);\n  }\n\n  private async initializeOptimizer(willowConfig?: any) {\n    const { createOptimizer } = await import('./utils/index');\n    this.optimizer = createOptimizer(willowConfig);\n  }\n\n  async startMonitoring(\n    request: any,\n    callback: (result: any) => void,\n    intervalMs: number = 60000 // 1 minute\n  ): Promise<void> {\n    if (this.isMonitoring) {\n      throw new Error('Monitoring already active');\n    }\n\n    // Ensure optimizer is initialized\n    if (!this.optimizer) {\n      await this.initializeOptimizer();\n    }\n\n    this.isMonitoring = true;\n\n    const runOptimization = async () => {\n      try {\n        const result = await this.optimizer.optimize(request);\n        callback(result);\n      } catch (error) {\n        console.error('Monitoring optimization failed:', error);\n      }\n    };\n\n    // Run initial optimization\n    await runOptimization();\n\n    // Set up periodic optimization\n    this.monitoringInterval = setInterval(runOptimization, intervalMs);\n  }\n\n  stopMonitoring(): void {\n    this.isMonitoring = false;\n    if (this.monitoringInterval) {\n      clearInterval(this.monitoringInterval);\n      this.monitoringInterval = undefined as any;\n    }\n  }\n\n  async cleanup(): Promise<void> {\n    this.stopMonitoring();\n    await this.optimizer.cleanup();\n  }\n}\n\n// Default export\nconst defaultExport = {\n  QuantumOptimizer,\n  WillowBridge,\n  ClassicalSolver,\n  optimizeBudget,\n  optimizeBatch,\n  compareOptimizationMethods,\n  OptimizationMonitor,\n  QUANTUM_CONSTANTS,\n  OPTIMIZATION_ALGORITHMS,\n  VERSION,\n};\n\nexport default defaultExport;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gBAGa,wBAaA,qBAmBA,6BAaA,wBAcA,2BAoCA,0BA+CA,4BAYA,oBAaA,0BAWA,gBAWA,4BAgCA;AAhOb;AAAA;AAAA,iBAAkB;AAGX,IAAM,yBAAyB,aAAE,OAAO;AAAA,MAC7C,IAAI,aAAE,OAAO;AAAA,MACb,MAAM,aAAE,KAAK,CAAC,gBAAgB,kBAAkB,gBAAgB,cAAc,aAAa,CAAC;AAAA,MAC5F,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,MAC3B,UAAU,aAAE,KAAK,CAAC,MAAM,MAAM,KAAK,KAAK,GAAG,CAAC;AAAA,MAC5C,UAAU,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC;AAAA,MAC7C,UAAU,aAAE,QAAQ,EAAE,QAAQ,KAAK;AAAA,MACnC,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;AAAA,IACjD,CAAC;AAKM,IAAM,sBAAsB,aAAE,OAAO;AAAA,MAC1C,IAAI,aAAE,OAAO;AAAA,MACb,MAAM,aAAE,OAAO;AAAA,MACf,MAAM,aAAE,KAAK,CAAC,YAAY,UAAU,YAAY,WAAW,UAAU,SAAS,KAAK,CAAC;AAAA,MACpF,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MAC3B,WAAW,aAAE,OAAO,EAAE,SAAS;AAAA,MAC/B,WAAW,aAAE,KAAK,CAAC,OAAO,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,MACtD,UAAU,aAAE,OAAO,EAAE,SAAS;AAAA,MAC9B,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAG;AAAA,MAChD,iBAAiB,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,MAChD,gBAAgB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,MACvC,mBAAmB,aAAE,OAAO,EAAE,SAAS;AAAA,MACvC,aAAa,aAAE,MAAM,aAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,QAAQ,MAAM,EAAE,EAAE,KAAK,CAAG,CAAC;AAAA,MACvE,kBAAkB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC;AAAA,IACvD,CAAC;AAKM,IAAM,8BAA8B,aAAE,OAAO;AAAA,MAClD,SAAS,aAAE,KAAK,CAAC,oBAAoB,wBAAwB,gBAAgB,eAAe,CAAC;AAAA,MAC7F,WAAW,aAAE,KAAK,CAAC,kBAAkB,iBAAiB,qBAAqB,CAAC,EAAE,SAAS;AAAA,MACvF,SAAS,aAAE,OAAO;AAAA,QAChB,SAAS,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;AAAA,QAC7C,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;AAAA,MACjD,CAAC;AAAA,MACD,aAAa,aAAE,KAAK,CAAC,SAAS,UAAU,WAAW,WAAW,CAAC,EAAE,QAAQ,SAAS;AAAA,IACpF,CAAC;AAKM,IAAM,yBAAyB,aAAE,OAAO;AAAA,MAC7C,WAAW,aAAE,OAAO;AAAA,MACpB,iBAAiB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MACjC,qBAAqB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MACrC,iBAAiB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MACjC,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,MACjC,cAAc,aAAE,OAAO,EAAE,SAAS;AAAA,MAClC,YAAY,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,MACnC,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAAA,IACrC,CAAC;AAKM,IAAM,4BAA4B,aAAE,OAAO;AAAA,MAChD,IAAI,aAAE,OAAO;AAAA,MACb,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,MACjC,UAAU,aAAE,MAAM,mBAAmB;AAAA,MACrC,aAAa,aAAE,MAAM,sBAAsB;AAAA,MAC3C,WAAW;AAAA,MACX,WAAW,aAAE,OAAO;AAAA,QAClB,OAAO,aAAE,KAAK;AAAA,QACd,KAAK,aAAE,KAAK;AAAA,MACd,CAAC;AAAA,MACD,gBAAgB,aAAE,OAAO;AAAA,QACvB,oBAAoB,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,MAAM,aAAE,OAAO;AAAA,UACxD,MAAM,aAAE,KAAK;AAAA,UACb,OAAO,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,UACvB,aAAa,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,UAC7B,SAAS,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,QAC3B,CAAC,CAAC,CAAC;AAAA,QACH,kBAAkB,aAAE,MAAM,aAAE,OAAO;AAAA,UACjC,MAAM,aAAE,KAAK;AAAA,UACb,kBAAkB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAAA,UAC1C,mBAAmB,aAAE,OAAO,EAAE,SAAS;AAAA,UACvC,mBAAmB,aAAE,OAAO;AAAA,QAC9B,CAAC,CAAC;AAAA,MACJ,CAAC,EAAE,SAAS;AAAA,MACZ,aAAa,aAAE,OAAO;AAAA,QACpB,wBAAwB,aAAE,QAAQ,EAAE,QAAQ,IAAI;AAAA,QAChD,0BAA0B,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,IAAI;AAAA,QAC/D,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,GAAI;AAAA,QACjD,sBAAsB,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,IAAK;AAAA,QACzD,eAAe,aAAE,KAAK,CAAC,gBAAgB,YAAY,YAAY,CAAC,EAAE,QAAQ,UAAU;AAAA,MACtF,CAAC,EAAE,QAAQ,CAAC,CAAC;AAAA,IACf,CAAC;AAKM,IAAM,2BAA2B,aAAE,OAAO;AAAA,MAC/C,IAAI,aAAE,OAAO;AAAA,MACb,WAAW,aAAE,OAAO;AAAA,MACpB,QAAQ,aAAE,KAAK,CAAC,WAAW,WAAW,QAAQ,CAAC;AAAA,MAC/C,QAAQ,aAAE,KAAK,CAAC,WAAW,aAAa,QAAQ,CAAC;AAAA,MACjD,aAAa,aAAE,MAAM,sBAAsB;AAAA,MAC3C,gBAAgB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MAChC,sBAAsB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MACtC,0BAA0B,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MAC1C,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,MACjC,YAAY,aAAE,OAAO,EAAE,SAAS;AAAA,MAChC,YAAY,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,MACnC,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAAA,MACnC,qBAAqB,aAAE,OAAO;AAAA,QAC5B,YAAY,aAAE,OAAO,EAAE,SAAS;AAAA,QAChC,iBAAiB,aAAE,OAAO,EAAE,SAAS;AAAA,QACrC,gBAAgB,aAAE,OAAO;AAAA,QACzB,sBAAsB,aAAE,MAAM,aAAE,OAAO;AAAA,UACrC,cAAc,aAAE,OAAO;AAAA,UACvB,WAAW,aAAE,OAAO;AAAA,UACpB,UAAU,aAAE,KAAK,CAAC,OAAO,UAAU,MAAM,CAAC;AAAA,QAC5C,CAAC,CAAC;AAAA,MACJ,CAAC;AAAA,MACD,gBAAgB,aAAE,OAAO;AAAA,QACvB,kBAAkB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,QACpD,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,QAC9C,WAAW,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,QAC1C,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,QAC7C,uBAAuB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,MAC3D,CAAC,EAAE,SAAS;AAAA,MACZ,iBAAiB,aAAE,MAAM,aAAE,OAAO;AAAA,QAChC,MAAM,aAAE,KAAK,CAAC,mBAAmB,qBAAqB,yBAAyB,mBAAmB,CAAC;AAAA,QACnG,aAAa,aAAE,OAAO;AAAA,QACtB,QAAQ,aAAE,OAAO;AAAA,UACf,eAAe,aAAE,OAAO;AAAA,UACxB,YAAY,aAAE,OAAO;AAAA,UACrB,YAAY,aAAE,OAAO;AAAA,QACvB,CAAC;AAAA,QACD,UAAU,aAAE,KAAK,CAAC,OAAO,UAAU,MAAM,CAAC;AAAA,MAC5C,CAAC,CAAC;AAAA,MACF,WAAW,aAAE,KAAK;AAAA,MAClB,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,IACnC,CAAC;AAKM,IAAM,6BAA6B,aAAE,OAAO;AAAA,MACjD,QAAQ,aAAE,OAAO,EAAE,SAAS,EAAE,IAAI,GAAG;AAAA;AAAA,MACrC,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,MAC3B,SAAS,aAAE,MAAM,aAAE,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,CAAC,CAAC;AAAA,MACrF,iBAAiB,aAAE,QAAQ,EAAE,QAAQ,IAAI;AAAA,MACzC,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,GAAG;AAAA;AAAA,MAChD,UAAU,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAK;AAAA,IAClD,CAAC;AAKM,IAAM,qBAAqB,aAAE,OAAO;AAAA,MACzC,UAAU,aAAE,OAAO,EAAE,IAAI;AAAA,MACzB,QAAQ,aAAE,OAAO;AAAA,MACjB,WAAW,aAAE,OAAO;AAAA,MACpB,QAAQ,aAAE,KAAK,CAAC,eAAe,gBAAgB,YAAY,CAAC,EAAE,QAAQ,aAAa;AAAA,MACnF,SAAS,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,GAAK;AAAA,MAC5C,SAAS,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC;AAAA,MAC3C,eAAe;AAAA,IACjB,CAAC;AAKM,IAAM,2BAAN,cAAuC,MAAM;AAAA,MAClD,YACE,SACO,MACA,SACP;AACA,cAAM,OAAO;AAHN;AACA;AAGP,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEO,IAAM,iBAAN,cAA6B,MAAM;AAAA,MACxC,YACE,SACO,YACA,UACP;AACA,cAAM,OAAO;AAHN;AACA;AAGP,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEO,IAAM,6BAAN,cAAyC,MAAM;AAAA,MACpD,YACE,SACO,WACA,SACP;AACA,cAAM,OAAO;AAHN;AACA;AAGP,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAuBO,IAAM,UAAU;AAAA,MACrB,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,uBAAuB;AAAA,MACvB,kBAAkB;AAAA,MAClB,qBAAqB;AAAA,MACrB,oBAAoB;AAAA,MACpB,sBAAsB;AAAA,MACtB,cAAc;AAAA,IAChB;AAAA;AAAA;;;ACzOA,kBAea;AAfb;AAAA;AAAA,mBAAoD;AACpD;AAcO,IAAM,eAAN,MAAmB;AAAA,MAChB;AAAA,MACA;AAAA,MACA,eAAiC,oBAAI,IAAI;AAAA,MAEjD,YAAY,QAAsB;AAChC,aAAK,SAAS;AACd,aAAK,SAAS,aAAAA,QAAM,OAAO;AAAA,UACzB,SAAS,OAAO;AAAA,UAChB,SAAS,OAAO;AAAA,UAChB,SAAS;AAAA,YACP,iBAAiB,UAAU,OAAO,MAAM;AAAA,YACxC,gBAAgB;AAAA,YAChB,uBAAuB,OAAO;AAAA,YAC9B,oBAAoB;AAAA,UACtB;AAAA,QACF,CAAC;AAED,aAAK,kBAAkB;AAAA,MACzB;AAAA,MAEQ,oBAA0B;AAEhC,aAAK,OAAO,aAAa,QAAQ;AAAA,UAC/B,CAAC,WAAW;AACV,oBAAQ,IAAI,YAAY,OAAO,QAAQ,YAAY,CAAC,IAAI,OAAO,GAAG,EAAE;AACpE,mBAAO;AAAA,UACT;AAAA,UACA,CAAC,UAAU,QAAQ,OAAO,KAAK;AAAA,QACjC;AAGA,aAAK,OAAO,aAAa,SAAS;AAAA,UAChC,CAAC,aAAa;AAAA,UACd,CAAC,UAAU;AACT,kBAAM,cAAc,IAAI;AAAA,cACtB,MAAM,UAAU,MAAM,WAAW,MAAM;AAAA,cACvC,MAAM,UAAU,UAAU;AAAA,cAC1B,MAAM,UAAU;AAAA,YAClB;AACA,mBAAO,QAAQ,OAAO,WAAW;AAAA,UACnC;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,qBAOH;AACD,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,OAAO,IAAI,8BAA8B;AACrE,iBAAO,SAAS;AAAA,QAClB,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,0BACJ,SACA,eACiB;AACjB,cAAM,aAAa,KAAK,mBAAmB,SAAS,aAAa;AAEjE,YAAI,KAAK,aAAa,IAAI,UAAU,GAAG;AACrC,iBAAO,KAAK,aAAa,IAAI,UAAU;AAAA,QACzC;AAEA,cAAM,UAAU,KAAK,oBAAoB,SAAS,aAAa;AAE/D,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,OAAO,KAAK,gBAAgB;AAAA,YACtD,MAAM,uBAAuB,QAAQ,EAAE;AAAA,YACvC;AAAA,YACA,QAAQ;AAAA,YACR,UAAU;AAAA,cACR,UAAU,QAAQ,SAAS;AAAA,cAC3B,aAAa,QAAQ,YAAY;AAAA,cACjC,aAAa,QAAQ;AAAA,cACrB,WAAW,QAAQ,UAAU;AAAA,YAC/B;AAAA,UACF,CAAC;AAED,gBAAM,YAAY,SAAS,KAAK;AAChC,eAAK,aAAa,IAAI,YAAY,SAAS;AAC3C,iBAAO;AAAA,QACT,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,oBACJ,WACA,YAYC;AACD,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,OAAO,KAAK,gBAAgB,SAAS,YAAY;AAAA,YAC3E;AAAA,YACA,OAAO;AAAA;AAAA,YACP,iBAAiB,KAAK,OAAO,cAAc;AAAA,YAC3C,cAAc;AAAA,cACZ,OAAO;AAAA,cACP,iBAAiB;AAAA,cACjB,mBAAmB;AAAA,YACrB;AAAA,UACF,CAAC;AAED,iBAAO,KAAK,qBAAqB,SAAS,IAAI;AAAA,QAChD,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,OACJ,aACA,mBACA,gBAAwB,KAOvB;AACD,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,OAAO,KAAK,sBAAsB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,YACA,sBAAsB;AAAA,YACtB,WAAW;AAAA,YACX,QAAQ;AAAA,UACV,CAAC;AAED,iBAAO,SAAS;AAAA,QAClB,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,QACJ,cACA,mBACA,SAAiB,GAMhB;AACD,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,OAAO,KAAK,uBAAuB;AAAA,YAC7D;AAAA,YACA;AAAA,YACA;AAAA,YACA,WAAW;AAAA,YACX,OAAO;AAAA,UACT,CAAC;AAED,iBAAO,SAAS;AAAA,QAClB,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,sBAOH;AACD,YAAI;AACF,gBAAM,WAAW,MAAM,KAAK,OAAO,IAAI,+BAA+B;AACtE,iBAAO,SAAS;AAAA,QAClB,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKQ,oBACN,SACA,QACK;AACL,cAAM,cAAc,QAAQ,SAAS;AACrC,cAAM,YAAY,KAAK,IAAI,cAAc,GAAG,OAAO,MAAM;AAEzD,cAAM,UAAU;AAAA,UACd,QAAQ;AAAA,UACR,OAAO,CAAC;AAAA,UACR,cAAc,CAAC;AAAA,QACjB;AAGA,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,kBAAQ,MAAM,KAAK,EAAE,MAAM,KAAK,OAAO,EAAE,CAAC;AAAA,QAC5C;AAGA,aAAK,kBAAkB,SAAS,QAAQ,aAAa,WAAW;AAGhE,aAAK,gBAAgB,SAAS,QAAQ,WAAW,WAAW;AAG5D,aAAK,gBAAgB,SAAS,WAAW;AAGzC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,kBAAQ,aAAa,KAAK,EAAE,OAAO,GAAG,eAAe,EAAE,CAAC;AAAA,QAC1D;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,kBAAkB,SAAc,aAAoB,aAA2B;AACrF,oBAAY,QAAQ,CAAC,YAAY,QAAQ;AACvC,cAAI,WAAW,SAAS,gBAAgB;AAEtC,qBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,oBAAM,QAAS,WAAW,QAAQ,MAAW,KAAK;AAClD,sBAAQ,MAAM,KAAK;AAAA,gBACjB,MAAM;AAAA,gBACN,OAAO,IAAI;AAAA,gBACX,WAAW;AAAA,cACb,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MAEQ,gBAAgB,SAAc,WAAgB,aAA2B;AAE/E,cAAM,SAAS,UAAU,QAAQ;AAEjC,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,gBAAM,QAAQ,SAAS,KAAK,KAAK;AACjC,kBAAQ,MAAM,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,OAAO,IAAI,IAAI;AAAA,YACf,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MAEQ,gBAAgB,SAAc,aAA2B;AAE/D,iBAAS,IAAI,GAAG,IAAI,cAAc,GAAG,KAAK;AACxC,kBAAQ,MAAM,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,SAAS,IAAI;AAAA,YACb,SAAS,IAAI,KAAK;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MAEQ,qBAAqB,WAAqB;AAChD,cAAM,eAAe,UAAU;AAC/B,cAAM,SAAS,UAAU;AAGzB,cAAM,SAAS,KAAK,mBAAmB,cAAc,MAAM;AAG3D,cAAM,mBAAmB,KAAK,0BAA0B,SAAS;AAEjE,eAAO;AAAA,UACL,QAAQ,OAAO;AAAA,UACf,YAAY,OAAO;AAAA,UACnB;AAAA,UACA,SAAS;AAAA,YACP,WAAW,UAAU,eAAe;AAAA,YACpC,OAAO,UAAU,eAAe;AAAA,YAChC,WAAW,UAAU,aAAa;AAAA,YAClC,eAAe,UAAU,iBAAiB;AAAA,YAC1C,eAAe,UAAU,OAAO;AAAA,UAClC;AAAA,QACF;AAAA,MACF;AAAA,MAEQ,mBAAmB,cAAmB,QAAkB;AAE9D,cAAM,aAAa,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,GAAW,MAAW,IAAI,OAAO,CAAC,GAAG,CAAC;AACvF,cAAM,cAAwB,CAAC;AAC/B,YAAI,aAAa;AAGjB,YAAI,WAAmB;AACvB,YAAI,cAAc;AAElB,mBAAW,CAAC,SAAS,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACrD,gBAAM,WAAW,OAAO,KAAK;AAC7B,cAAI,WAAW,UAAU;AACvB,uBAAW;AACX,0BAAc;AAAA,UAChB;AAAA,QACF;AAEA,qBAAa,WAAW;AAGxB,cAAM,eAAe;AACrB,cAAM,cAAc,aAAa,SAAS;AAE1C,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,gBAAM,cAAc,aAAa,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC;AACzD,gBAAM,aAAa,SAAS,aAAa,CAAC,IAAI;AAC9C,sBAAY,KAAK,UAAU;AAAA,QAC7B;AAEA,eAAO,EAAE,aAAa,WAAW;AAAA,MACnC;AAAA,MAEQ,0BAA0B,QAAqB;AAErD,cAAM,gBAAgB,OAAO,iBAAiB;AAC9C,cAAM,YAAY,OAAO,aAAa;AACtC,cAAM,WAAW,OAAO,OAAO;AAG/B,cAAM,qBAAqB,KAAK,IAAI,iBAAiB,WAAW,MAAM,CAAC;AACvE,cAAM,iBAAiB,KAAK,IAAI,GAAG,IAAI,YAAY,GAAG;AAEtD,gBAAQ,qBAAqB,kBAAkB;AAAA,MACjD;AAAA,MAEQ,mBAAmB,SAA8B,QAAsC;AAC7F,cAAM,UAAU;AAAA,UACd,UAAU,QAAQ,SAAS;AAAA,UAC3B,aAAa,QAAQ,YAAY;AAAA,UACjC,WAAW,QAAQ,UAAU;AAAA,UAC7B,QAAQ,OAAO;AAAA,UACf,OAAO,OAAO;AAAA,QAChB;AAEA,eAAO,OAAO,KAAK,KAAK,UAAU,OAAO,CAAC,EAAE,SAAS,QAAQ;AAAA,MAC/D;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,UAAyB;AAC7B,aAAK,aAAa,MAAM;AAAA,MAE1B;AAAA,IACF;AAAA;AAAA;;;ACpaA;AAAA;AAAA;AAAA;AAAA,IAEA,eAuBa;AAzBb;AAAA;AAEA,oBAA6C;AAC7C;AAsBO,IAAM,kBAAN,MAAsB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MAER,YAAY,UAIR,CAAC,GAAG;AACN,aAAK,gBAAgB,QAAQ,iBAAiB;AAC9C,aAAK,uBAAuB,QAAQ,wBAAwB;AAC5D,aAAK,iBAAiB,QAAQ,kBAAkB;AAAA,MAClD;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,SAAS,SAA2D;AACxE,cAAM,YAAY,KAAK,IAAI;AAE3B,YAAI;AAEF,gBAAM,YAAY,KAAK,gBAAgB,OAAO;AAC9C,kBAAQ,IAAI,qBAAqB,SAAS,YAAY;AAEtD,cAAI;AAEJ,kBAAQ,WAAW;AAAA,YACjB,KAAK;AACH,uBAAS,MAAM,KAAK,+BAA+B,OAAO;AAC1D;AAAA,YACF,KAAK;AACH,uBAAS,MAAM,KAAK,iBAAiB,OAAO;AAC5C;AAAA,YACF,KAAK;AACH,uBAAS,MAAM,KAAK,0BAA0B,OAAO;AACrD;AAAA,YACF,KAAK;AACH,uBAAS,MAAM,KAAK,mBAAmB,OAAO;AAC9C;AAAA,YACF,KAAK;AACH,uBAAS,MAAM,KAAK,oBAAoB,OAAO;AAC/C;AAAA,YACF;AACE,oBAAM,IAAI;AAAA,gBACR,sBAAsB,SAAS;AAAA,gBAC/B;AAAA,cACF;AAAA,UACJ;AAEA,iBAAO,cAAc,KAAK,IAAI,IAAI;AAClC,iBAAO,SAAS;AAEhB,iBAAO;AAAA,QACT,SAAS,OAAO;AACd,gBAAM,IAAI;AAAA,YACR,kCAAkC,MAAM,OAAO;AAAA,YAC/C;AAAA,YACA,EAAE,eAAe,MAAM;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAc,+BAA+B,SAA2D;AACtG,cAAM,cAAc,QAAQ,SAAS;AACrC,YAAI,IAAI,KAAK,mBAAmB,OAAO;AACvC,YAAI,YAAY;AAChB,YAAI,YAAY;AAEhB,cAAM,UAA+B;AAAA,UACnC,gBAAgB;AAAA,UAChB,sBAAsB;AAAA,UACtB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,iBAAiB;AAAA,QACnB;AAEA,eAAO,YAAY,KAAK,iBAAiB,CAAC,WAAW;AAEnD,gBAAM,WAAW,KAAK,kBAAkB,GAAG,OAAO;AAClD,gBAAM,UAAU,KAAK,iBAAiB,GAAG,OAAO;AAGhD,gBAAM,YAAY,KAAK,kBAAkB,UAAU,SAAS,GAAG,OAAO;AAGtE,gBAAM,WAAW,KAAK,WAAW,GAAG,WAAW,OAAO;AAGtD,gBAAM,OAAO,EAAE,IAAI,CAAC,IAAI,MAAM,KAAK,WAAW,UAAU,CAAC,CAAC;AAG1D,gBAAM,SAAS,KAAK,cAAc,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC;AAChE,sBAAY,SAAS,KAAK;AAE1B,cAAI;AACJ;AAGA,kBAAQ,iBAAiB,KAAK,kBAAkB,GAAG,OAAO;AAC1D,kBAAQ,uBAAuB,KAAK,6BAA6B,GAAG,OAAO;AAAA,QAC7E;AAEA,gBAAQ,cAAc,QAAQ,uBAAuB;AACrD,gBAAQ,aAAa,YAAY,IAAM;AACvC,gBAAQ,kBAAkB,YAAY,KAAK;AAE3C,eAAO,KAAK,YAAY,SAAS,GAAG,SAAS,WAAW,KAAK;AAAA,MAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAc,iBAAiB,SAA2D;AACxF,cAAM,cAAc,QAAQ,SAAS;AACrC,YAAI,aAAa,KAAK,qBAAqB,SAAS,KAAK,cAAc;AACvE,YAAI,aAAa;AACjB,YAAI,eAAe,WAAW,CAAC;AAC/B,YAAI,cAAc,KAAK,gBAAgB,cAAc,OAAO;AAE5D,eAAO,aAAa,KAAK,eAAe;AAEtC,gBAAM,UAAU,WAAW,IAAI,gBAAc,KAAK,gBAAgB,YAAY,OAAO,CAAC;AAGtF,gBAAM,kBAAkB,QAAQ,QAAQ,KAAK,IAAI,GAAG,OAAO,CAAC;AAC5D,cAAI,QAAQ,eAAe,IAAI,aAAa;AAC1C,+BAAe,yBAAU,WAAW,eAAe,CAAC;AACpD,0BAAc,QAAQ,eAAe;AAAA,UACvC;AAGA,gBAAM,UAAU,KAAK,oBAAoB,YAAY,SAAS,KAAK,cAAc;AAGjF,gBAAM,YAAY,KAAK,qBAAqB,SAAS,OAAO;AAG5D,uBAAa;AACb;AAAA,QACF;AAEA,cAAM,UAA+B;AAAA,UACnC,gBAAgB,KAAK,kBAAkB,cAAc,OAAO;AAAA,UAC5D,sBAAsB,KAAK,6BAA6B,cAAc,OAAO;AAAA,UAC7E,aAAa,KAAK,6BAA6B,cAAc,OAAO,IAAI;AAAA,UACxE,YAAY;AAAA;AAAA,UACZ,iBAAiB,aAAa,KAAK;AAAA,QACrC;AAEA,eAAO,KAAK,YAAY,SAAS,cAAc,SAAS,YAAY,SAAS;AAAA,MAC/E;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAc,0BAA0B,SAA2D;AACjG,cAAM,cAAc,QAAQ,SAAS;AACrC,cAAM,eAAe,KAAK;AAG1B,cAAM,YAAY,MAAM,YAAY,EAAE,KAAK,IAAI,EAAE,IAAI,OAAO;AAAA,UAC1D,UAAU,KAAK,mBAAmB,OAAO;AAAA,UACzC,UAAU,MAAM,WAAW,EAAE,KAAK,CAAC,EAAE,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,GAAG;AAAA,UAC1E,cAAc,CAAC;AAAA,UACf,aAAa;AAAA,QACf,EAAE;AAEF,YAAI,qBAAqB,UAAU,CAAC,EAAE;AACtC,YAAI,oBAAoB;AACxB,YAAI,YAAY;AAGhB,cAAM,IAAI;AACV,cAAM,KAAK;AACX,cAAM,KAAK;AAEX,eAAO,YAAY,KAAK,eAAe;AACrC,qBAAW,YAAY,WAAW;AAChC,kBAAM,UAAU,KAAK,gBAAgB,SAAS,UAAU,OAAO;AAG/D,gBAAI,UAAU,SAAS,aAAa;AAClC,uBAAS,mBAAe,yBAAU,SAAS,QAAQ;AACnD,uBAAS,cAAc;AAAA,YACzB;AAGA,gBAAI,UAAU,mBAAmB;AAC/B,uCAAqB,yBAAU,SAAS,QAAQ;AAChD,kCAAoB;AAAA,YACtB;AAGA,qBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,oBAAM,KAAK,KAAK,OAAO;AACvB,oBAAM,KAAK,KAAK,OAAO;AAEvB,uBAAS,SAAS,CAAC,IAAI,IAAI,SAAS,SAAS,CAAC,IAC5C,KAAK,MAAM,SAAS,aAAa,CAAC,IAAI,SAAS,SAAS,CAAC,KACzD,KAAK,MAAM,mBAAmB,CAAC,IAAI,SAAS,SAAS,CAAC;AAExD,uBAAS,SAAS,CAAC,KAAK,SAAS,SAAS,CAAC;AAG3C,uBAAS,SAAS,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,SAAS,SAAS,CAAC,CAAC,CAAC;AAAA,YACtE;AAAA,UACF;AAEA;AAAA,QACF;AAEA,cAAM,UAA+B;AAAA,UACnC,gBAAgB,KAAK,kBAAkB,oBAAoB,OAAO;AAAA,UAClE,sBAAsB,KAAK,6BAA6B,oBAAoB,OAAO;AAAA,UACnF,aAAa,KAAK,6BAA6B,oBAAoB,OAAO,IAAI;AAAA,UAC9E,YAAY;AAAA,UACZ,iBAAiB,YAAY,KAAK;AAAA,QACpC;AAEA,eAAO,KAAK,YAAY,SAAS,oBAAoB,SAAS,WAAW,KAAK;AAAA,MAChF;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAc,mBAAmB,SAA2D;AAC1F,YAAI,kBAAkB,KAAK,mBAAmB,OAAO;AACrD,YAAI,gBAAgB,CAAC,KAAK,gBAAgB,iBAAiB,OAAO;AAClE,YAAI,mBAAe,yBAAU,eAAe;AAC5C,YAAI,aAAa;AAEjB,cAAM,cAAc;AACpB,cAAM,YAAY;AAClB,cAAM,cAAc;AACpB,YAAI,cAAc;AAClB,YAAI,YAAY;AAEhB,eAAO,cAAc,aAAa,YAAY,KAAK,eAAe;AAEhE,gBAAM,WAAW,KAAK,iBAAiB,iBAAiB,OAAO;AAC/D,gBAAM,iBAAiB,CAAC,KAAK,gBAAgB,UAAU,OAAO;AAG9D,gBAAM,SAAS,iBAAiB;AAChC,cAAI,SAAS,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,SAAS,WAAW,GAAG;AACjE,8BAAkB;AAClB,4BAAgB;AAEhB,gBAAI,gBAAgB,YAAY;AAC9B,iCAAe,yBAAU,eAAe;AACxC,2BAAa;AAAA,YACf;AAAA,UACF;AAEA,yBAAe;AACf;AAAA,QACF;AAEA,cAAM,UAA+B;AAAA,UACnC,gBAAgB,KAAK,kBAAkB,cAAc,OAAO;AAAA,UAC5D,sBAAsB,KAAK,6BAA6B,cAAc,OAAO;AAAA,UAC7E,aAAa,KAAK,6BAA6B,cAAc,OAAO,IAAI;AAAA,UACxE,YAAY;AAAA,UACZ,iBAAiB,YAAY,KAAK;AAAA,QACpC;AAEA,eAAO,KAAK,YAAY,SAAS,cAAc,SAAS,WAAW,qBAAqB;AAAA,MAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,MAMA,MAAc,oBAAoB,SAA2D;AAC3F,cAAM,cAAc,QAAQ,SAAS;AACrC,YAAI,IAAI,KAAK,mBAAmB,OAAO;AACvC,YAAI,KAAK;AACT,YAAI,YAAY;AAChB,YAAI,YAAY;AAEhB,eAAO,YAAY,KAAK,iBAAiB,CAAC,aAAa,KAAK,MAAM;AAEhE,gBAAM,SAAS,KAAK,uBAAuB,GAAG,IAAI,OAAO;AACzD,cAAI,OAAO;AACX,sBAAY,OAAO;AAGnB,gBAAM;AACN;AAAA,QACF;AAEA,cAAM,UAA+B;AAAA,UACnC,gBAAgB,KAAK,kBAAkB,GAAG,OAAO;AAAA,UACjD,sBAAsB,KAAK,6BAA6B,GAAG,OAAO;AAAA,UAClE,aAAa,KAAK,6BAA6B,GAAG,OAAO,IAAI;AAAA,UAC7D,YAAY,YAAY,OAAO;AAAA,UAC/B,iBAAiB,YAAY,KAAK;AAAA,QACpC;AAEA,eAAO,KAAK,YAAY,SAAS,GAAG,SAAS,WAAW,gBAAgB;AAAA,MAC1E;AAAA;AAAA,MAGQ,gBAAgB,SAAsC;AAC5D,cAAM,cAAc,QAAQ,SAAS;AACrC,cAAM,iBAAiB,QAAQ,YAAY;AAC3C,cAAM,0BAA0B,QAAQ,YAAY;AAAA,UAAK,OACvD,EAAE,SAAS,iBAAiB,EAAE,SAAS;AAAA,QACzC;AAEA,YAAI,eAAe,KAAK,CAAC,yBAAyB;AAChD,iBAAO;AAAA,QACT,WAAW,cAAc,MAAM,yBAAyB;AACtD,iBAAO;AAAA,QACT,WAAW,QAAQ,UAAU,YAAY,iBAAiB;AACxD,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,MAEQ,mBAAmB,SAAwC;AAEjE,cAAM,cAAc,QAAQ,SAAS;AACrC,eAAO,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW;AAAA,MAChD;AAAA,MAEQ,kBAAkB,YAAsB,SAAsC;AACpF,YAAI,YAAY;AAChB,cAAM,cAAc,QAAQ;AAE5B,mBAAW,QAAQ,CAAC,QAAQ,MAAM;AAChC,gBAAM,UAAU,QAAQ,SAAS,CAAC;AAClC,gBAAM,SAAS,SAAS;AAGxB,gBAAM,cAAc,KAAK,qBAAqB,QAAQ,OAAO;AAC7D,gBAAM,UAAU,cAAc,QAAQ;AAEtC,kBAAQ,QAAQ,UAAU,SAAS;AAAA,YACjC,KAAK;AACH,2BAAa;AACb;AAAA,YACF,KAAK;AACH,2BAAa;AACb;AAAA,YACF,KAAK;AACH,2BAAa,SAAS,KAAK,IAAI,aAAa,CAAC;AAC7C;AAAA,YACF,KAAK;AACH,2BAAa,UAAU,KAAK,IAAI,QAAQ,CAAC;AACzC;AAAA,UACJ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MAEQ,gBAAgB,YAAsB,SAAsC;AAClF,cAAM,YAAY,KAAK,kBAAkB,YAAY,OAAO;AAC5D,cAAM,aAAa,KAAK,6BAA6B,YAAY,OAAO;AAGxE,eAAO,YAAY,MAAO;AAAA,MAC5B;AAAA,MAEQ,6BAA6B,YAAsB,SAAsC;AAC/F,YAAI,aAAa;AACjB,cAAM,cAAc,QAAQ;AAG5B,cAAM,oBAAgB,mBAAI,UAAU;AACpC,sBAAc,KAAK,IAAI,gBAAgB,CAAC;AAGxC,gBAAQ,YAAY,QAAQ,gBAAc;AACxC,kBAAQ,WAAW,MAAM;AAAA,YACvB,KAAK;AACH,oBAAM,aAAa,gBAAgB;AACnC,kBAAI,WAAW,aAAa,QAAQ,aAAa,WAAW,OAAO;AACjE,+BAAe,aAAa,WAAW,SAAS,WAAW;AAAA,cAC7D;AACA;AAAA,UAEJ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MAEQ,qBAAqB,QAAgB,SAAsB;AAEjE,cAAM,kBAAkB,SAAS,QAAQ,iBAAiB,QAAQ;AAClE,cAAM,mBAAmB,QAAQ,kBAC/B,KAAK,IAAI,GAAG,QAAQ,kBAAkB,MAAM,IAAI;AAElD,eAAO,kBAAkB,mBAAmB,QAAQ;AAAA,MACtD;AAAA,MAEQ,YACN,SACA,YACA,SACA,YACA,WACoB;AACpB,cAAM,cAAkC,WAAW,IAAI,CAAC,QAAQ,MAAM;AACpE,gBAAM,UAAU,QAAQ,SAAS,CAAC;AAClC,gBAAM,SAAS,SAAS,QAAQ;AAChC,gBAAM,cAAc,KAAK,qBAAqB,QAAQ,OAAO;AAC7D,gBAAM,UAAU,cAAc,QAAQ;AAEtC,iBAAO;AAAA,YACL,WAAW,QAAQ;AAAA,YACnB,iBAAiB;AAAA,YACjB,qBAAqB;AAAA,YACrB,iBAAiB;AAAA,YACjB,aAAa,SAAS,KAAK,IAAI,aAAa,CAAC;AAAA,YAC7C,cAAc,UAAU,KAAK,IAAI,QAAQ,CAAC;AAAA,YAC1C,YAAY;AAAA;AAAA,YACZ,WAAW;AAAA;AAAA,UACb;AAAA,QACF,CAAC;AAED,eAAO;AAAA,UACL,IAAI,UAAU,KAAK,IAAI,CAAC;AAAA,UACxB,WAAW,QAAQ;AAAA,UACnB,QAAQ,QAAQ,cAAc,YAAY;AAAA,UAC1C,QAAQ;AAAA,UACR;AAAA,UACA,oBAAgB,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC;AAAA,UAC3D,0BAAsB,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC;AAAA,UACjE,8BAA0B,mBAAI,YAAY,IAAI,OAAK,EAAE,mBAAmB,CAAC;AAAA,UACzE,iBAAa,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC,IAC3C,KAAK,QAAI,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC,GAAG,CAAC;AAAA,UACrE,gBAAY,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC,IAC3C,KAAK,QAAI,mBAAI,YAAY,IAAI,OAAK,EAAE,mBAAmB,CAAC,GAAG,CAAC;AAAA,UACxE,YAAY,QAAQ;AAAA,UACpB,WAAW,QAAQ,cAAc,IAAI;AAAA,UACrC,qBAAqB;AAAA,YACnB;AAAA,YACA,iBAAiB;AAAA;AAAA,YACjB,gBAAgB,QAAQ;AAAA,YACxB,sBAAsB,CAAC;AAAA,UACzB;AAAA,UACA,iBAAiB,CAAC;AAAA,UAClB,WAAW,oBAAI,KAAK;AAAA,UACpB,aAAa;AAAA;AAAA,QACf;AAAA,MACF;AAAA;AAAA,MAGQ,kBAAkB,GAAa,SAAwC;AAC7E,cAAM,IAAI;AACV,cAAM,WAAW,IAAI,MAAM,EAAE,MAAM;AAEnC,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,gBAAM,QAAQ,CAAC,GAAG,CAAC;AACnB,gBAAM,SAAS,CAAC,GAAG,CAAC;AACpB,gBAAM,CAAC,KAAK;AACZ,iBAAO,CAAC,KAAK;AAEb,mBAAS,CAAC,KAAK,KAAK,kBAAkB,OAAO,OAAO,IACtC,KAAK,kBAAkB,QAAQ,OAAO,MAAM,IAAI;AAAA,QAChE;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,iBAAiB,GAAa,SAA0C;AAC9E,cAAM,IAAI;AACV,cAAM,IAAI,EAAE;AACZ,cAAM,UAAU,MAAM,CAAC,EAAE,KAAK,IAAI,EAAE,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AAE9D,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAM,MAAM,CAAC,GAAG,CAAC;AAAG,gBAAI,CAAC,KAAK;AAAG,gBAAI,CAAC,KAAK;AAC3C,kBAAM,MAAM,CAAC,GAAG,CAAC;AAAG,gBAAI,CAAC,KAAK;AAAG,gBAAI,CAAC,KAAK;AAC3C,kBAAM,MAAM,CAAC,GAAG,CAAC;AAAG,gBAAI,CAAC,KAAK;AAAG,gBAAI,CAAC,KAAK;AAC3C,kBAAM,MAAM,CAAC,GAAG,CAAC;AAAG,gBAAI,CAAC,KAAK;AAAG,gBAAI,CAAC,KAAK;AAE3C,oBAAQ,CAAC,EAAE,CAAC,KACV,KAAK,kBAAkB,KAAK,OAAO,IACnC,KAAK,kBAAkB,KAAK,OAAO,IACnC,KAAK,kBAAkB,KAAK,OAAO,IACnC,KAAK,kBAAkB,KAAK,OAAO,MAChC,IAAI,IAAI;AAAA,UACf;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,kBAAkB,UAAoB,SAAqB,GAAa,SAAwC;AAEtH,cAAM,IAAI,EAAE;AACZ,cAAM,YAAY,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AAGrC,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,oBAAU,CAAC,IAAI,CAAC,SAAS,CAAC;AAAA,QAC5B;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,WAAW,GAAa,WAAqB,SAAsC;AACzF,YAAI,QAAQ;AACZ,cAAM,KAAK;AACX,cAAM,MAAM;AAEZ,cAAM,KAAK,KAAK,kBAAkB,GAAG,OAAO;AAC5C,cAAM,QAAQ,KAAK,kBAAkB,GAAG,OAAO;AAC/C,cAAM,4BAAwB,mBAAI,MAAM,IAAI,CAAC,GAAG,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC;AAEvE,eAAO,QAAQ,MAAM;AACnB,gBAAM,OAAO,EAAE,IAAI,CAAC,IAAI,MAAM,KAAK,QAAQ,UAAU,CAAC,CAAC;AACvD,gBAAM,KAAK,KAAK,kBAAkB,MAAM,OAAO;AAE/C,cAAI,MAAM,KAAK,KAAK,QAAQ,uBAAuB;AACjD,mBAAO;AAAA,UACT;AAEA,mBAAS;AAAA,QACX;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,cAAc,QAA0B;AAC9C,eAAO,KAAK,SAAK,mBAAI,OAAO,IAAI,OAAK,IAAI,CAAC,CAAC,CAAC;AAAA,MAC9C;AAAA,MAEQ,qBAAqB,SAA8B,MAA0B;AACnF,cAAM,cAAc,QAAQ,SAAS;AACrC,cAAM,aAAyB,CAAC;AAEhC,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,gBAAM,aAAa,MAAM,WAAW,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,OAAO,CAAC;AACrE,gBAAM,YAAQ,mBAAI,UAAU;AAE5B,qBAAW,KAAK,WAAW,IAAI,OAAK,IAAI,KAAK,CAAC;AAAA,QAChD;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,oBAAoB,YAAwB,SAAmB,MAA0B;AAC/F,cAAM,WAAuB,CAAC;AAC9B,cAAM,iBAAiB;AAEvB,iBAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,cAAI,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM;AAEvD,mBAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,kBAAM,YAAY,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM;AAC9D,gBAAI,QAAQ,SAAS,IAAI,QAAQ,IAAI,GAAG;AACtC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,mBAAS,SAAK,yBAAU,WAAW,IAAI,CAAC,CAAC;AAAA,QAC3C;AAEA,eAAO;AAAA,MACT;AAAA,MAEQ,qBAAqB,SAAqB,SAA0C;AAC1F,cAAM,YAAwB,CAAC;AAC/B,cAAM,eAAe;AACrB,cAAM,gBAAgB;AAEtB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1C,cAAI,aAAS,yBAAU,QAAQ,CAAC,CAAC;AACjC,cAAI,aAAS,yBAAU,QAAQ,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;AAGnD,cAAI,KAAK,OAAO,IAAI,eAAe;AACjC,kBAAM,iBAAiB,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM;AAC/D,qBAAS,IAAI,gBAAgB,IAAI,OAAO,QAAQ,KAAK;AACnD,eAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,YAChD;AAAA,UACF;AAGA,cAAI,KAAK,OAAO,IAAI,cAAc;AAChC,kBAAM,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM;AAC9D,mBAAO,aAAa,MAAM,KAAK,OAAO,IAAI,OAAO;AACjD,mBAAO,aAAa,IAAI,KAAK,IAAI,GAAG,OAAO,aAAa,CAAC;AAAA,UAC3D;AAGA,gBAAM,aAAS,mBAAI,MAAM;AACzB,gBAAM,aAAS,mBAAI,MAAM;AACzB,mBAAS,OAAO,IAAI,OAAK,IAAI,MAAM;AACnC,mBAAS,OAAO,IAAI,OAAK,IAAI,MAAM;AAEnC,oBAAU,KAAK,QAAQ,MAAM;AAAA,QAC/B;AAEA,eAAO,UAAU,MAAM,GAAG,QAAQ,MAAM;AAAA,MAC1C;AAAA,MAEQ,iBAAiB,UAAoB,SAAwC;AACnF,cAAM,eAAW,yBAAU,QAAQ;AACnC,cAAM,mBAAmB;AAGzB,cAAM,mBAAmB,KAAK,OAAO,IAAI,MAAM,IAAI;AAEnD,iBAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,gBAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM;AACxD,mBAAS,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO;AAC3C,mBAAS,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,KAAK,CAAC;AAAA,QAC/C;AAGA,cAAM,YAAQ,mBAAI,QAAQ;AAC1B,eAAO,SAAS,IAAI,OAAK,IAAI,KAAK;AAAA,MACpC;AAAA,MAEQ,uBAAuB,GAAa,IAAY,SAA0E;AAEhI,YAAI,eAAW,yBAAU,CAAC;AAC1B,YAAI,YAAY;AAChB,cAAM,qBAAqB;AAE3B,iBAAS,OAAO,GAAG,OAAO,oBAAoB,QAAQ;AACpD,gBAAM,WAAW,KAAK,yBAAyB,UAAU,IAAI,OAAO;AACpE,gBAAM,WAAW,KAAK,WAAW,UAAU,SAAS,IAAI,OAAK,CAAC,CAAC,GAAG,OAAO;AAEzE,gBAAM,OAAO,SAAS,IAAI,CAAC,IAAI,MAAM,KAAK,WAAW,SAAS,CAAC,CAAC;AAGhE,gBAAM,YAAQ,mBAAI,IAAI;AACtB,gBAAM,cAAc,KAAK,IAAI,QAAM,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AAE7D,gBAAM,SAAS,KAAK,cAAc,YAAY,IAAI,CAAC,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;AAC9E,cAAI,SAAS,KAAK,sBAAsB;AACtC,wBAAY;AACZ;AAAA,UACF;AAEA,qBAAW;AAAA,QACb;AAEA,eAAO,EAAE,UAAU,UAAU,UAAU;AAAA,MACzC;AAAA,MAEQ,yBAAyB,GAAa,IAAY,SAAwC;AAChG,cAAM,oBAAoB,KAAK,kBAAkB,GAAG,OAAO;AAC3D,cAAM,kBAAkB,IAAI,MAAM,EAAE,MAAM;AAG1C,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,0BAAgB,CAAC,IAAI,kBAAkB,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI;AAAA,QACtE;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;ACtrBA,IAsBa;AAtBb;AAAA;AAAA;AACA;AACA;AAoBO,IAAM,mBAAN,MAAuB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA,qBAA4C,oBAAI,IAAI;AAAA,MAE5D,YAAY,QAAsB;AAChC,aAAK,SAAS;AACd,aAAK,eAAe,IAAI,aAAa,MAAM;AAC3C,aAAK,kBAAkB,IAAI,gBAAgB;AAAA,UACzC,eAAe;AAAA,UACf,sBAAsB;AAAA,UACtB,gBAAgB;AAAA,QAClB,CAAC;AAAA,MACH;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,SAAS,SAA2D;AACxE,cAAM,YAAY,KAAK,IAAI;AAE3B,YAAI;AAEF,gBAAM,WAAW,MAAM,KAAK,2BAA2B,OAAO;AAC9D,kBAAQ,IAAI,mBAAmB,QAAQ,wBAAwB;AAE/D,cAAI;AAEJ,kBAAQ,UAAU;AAAA,YAChB,KAAK;AACH,uBAAS,MAAM,KAAK,oBAAoB,OAAO;AAC/C;AAAA,YACF,KAAK;AACH,uBAAS,MAAM,KAAK,sBAAsB,OAAO;AACjD;AAAA,YACF,KAAK;AACH,uBAAS,MAAM,KAAK,mBAAmB,OAAO;AAC9C;AAAA,YACF;AACE,oBAAM,IAAI;AAAA,gBACR,kCAAkC,QAAQ;AAAA,gBAC1C;AAAA,cACF;AAAA,UACJ;AAGA,eAAK,yBAAyB,UAAU,OAAO,WAAW;AAG1D,iBAAO,gBAAgB,KAAK,GAAG,KAAK,+BAA+B,MAAM,CAAC;AAE1E,iBAAO;AAAA,QACT,SAAS,OAAO;AACd,kBAAQ,MAAM,6DAA6D,KAAK;AAGhF,gBAAM,iBAAiB,MAAM,KAAK,sBAAsB,OAAO;AAC/D,yBAAe,SAAS;AACxB,yBAAe,gBAAgB,QAAQ;AAAA,YACrC,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,eAAe;AAAA,cACf,YAAY;AAAA,cACZ,YAAY;AAAA,YACd;AAAA,YACA,UAAU;AAAA,UACZ,CAAC;AAED,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAc,oBAAoB,SAA2D;AAC3F,gBAAQ,IAAI,mDAAmD;AAG/D,cAAM,SAAS,MAAM,KAAK,aAAa,mBAAmB;AAC1D,YAAI,CAAC,OAAO,WAAW;AACrB,gBAAM,IAAI;AAAA,YACR;AAAA,YACA;AAAA,YACA,EAAE,OAAO;AAAA,UACX;AAAA,QACF;AAGA,cAAM,YAAY,MAAM,KAAK,aAAa;AAAA,UACxC;AAAA,UACA,KAAK,OAAO;AAAA,QACd;AAGA,cAAM,aAAa,KAAK,yBAAyB,OAAO;AAGxD,cAAM,gBAAgB,MAAM,KAAK,aAAa,oBAAoB,WAAW,UAAU;AAGvF,cAAM,cAAc,KAAK;AAAA,UACvB,cAAc;AAAA,UACd;AAAA,QACF;AAGA,cAAM,SAA6B;AAAA,UACjC,IAAI,WAAW,KAAK,IAAI,CAAC;AAAA,UACzB,WAAW,QAAQ;AAAA,UACnB,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR;AAAA,UACA,gBAAgB,YAAY,OAAO,CAACC,MAAK,MAAMA,OAAM,EAAE,iBAAiB,CAAC;AAAA,UACzE,sBAAsB,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,iBAAiB,CAAC;AAAA,UAC/E,0BAA0B,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,qBAAqB,CAAC;AAAA,UACvF,aAAa;AAAA;AAAA,UACb,YAAY;AAAA;AAAA,UACZ,YAAY,cAAc;AAAA,UAC1B,WAAW,KAAK,0BAA0B,aAAa;AAAA,UACvD,qBAAqB;AAAA,YACnB,YAAY;AAAA;AAAA,YACZ,iBAAiB,cAAc,QAAQ;AAAA,YACvC,gBAAgB,KAAK,wBAAwB,aAAa,OAAO;AAAA,YACjE,sBAAsB,CAAC;AAAA,UACzB;AAAA,UACA,gBAAgB;AAAA,YACd,kBAAkB,cAAc;AAAA,YAChC,eAAe,cAAc,QAAQ;AAAA,YACrC,WAAW,cAAc,QAAQ;AAAA,YACjC,WAAW,cAAc,QAAQ;AAAA,YACjC,uBAAuB,OAAO,SAAS;AAAA;AAAA,UACzC;AAAA,UACA,iBAAiB,CAAC;AAAA,UAClB,WAAW,oBAAI,KAAK;AAAA,UACpB,aAAa,cAAc,QAAQ;AAAA,QACrC;AAGA,eAAO,cAAc,OAAO,uBAAuB,KAAK,IAAI,OAAO,gBAAgB,CAAC;AACpF,eAAO,aAAa,OAAO,iBAAiB,KAAK,IAAI,OAAO,0BAA0B,CAAC;AAEvF,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKA,MAAc,sBAAsB,SAA2D;AAC7F,gBAAQ,IAAI,iDAAiD;AAC7D,eAAO,MAAM,KAAK,gBAAgB,SAAS,OAAO;AAAA,MACpD;AAAA;AAAA;AAAA;AAAA,MAKA,MAAc,mBAAmB,SAA2D;AAC1F,gBAAQ,IAAI,wCAAwC;AAEpD,YAAI;AAEF,gBAAM,gBAAgB,MAAM,KAAK,oBAAoB,OAAO;AAG5D,gBAAM,iBAAiB,KAAK,wBAAwB,SAAS,aAAa;AAC1E,gBAAM,kBAAkB,MAAM,KAAK,gBAAgB,SAAS,cAAc;AAG1E,gBAAM,eAAe,KAAK,eAAe,eAAe,eAAe;AACvE,uBAAa,SAAS;AAEtB,iBAAO;AAAA,QACT,SAAS,OAAO;AACd,kBAAQ,KAAK,+DAA+D,KAAK;AACjF,iBAAO,MAAM,KAAK,sBAAsB,OAAO;AAAA,QACjD;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAc,2BAA2B,SAA2E;AAClH,cAAM,cAAc,QAAQ,SAAS;AACrC,cAAM,iBAAiB,QAAQ,YAAY;AAC3C,cAAM,cAAc,QAAQ;AAG5B,YAAI,CAAC,QAAQ,YAAY,wBAAwB;AAC/C,iBAAO;AAAA,QACT;AAGA,YAAI;AACF,gBAAM,SAAS,MAAM,KAAK,aAAa,mBAAmB;AAC1D,cAAI,CAAC,OAAO,aAAa,OAAO,cAAc,IAAI;AAChD,mBAAO;AAAA,UACT;AAGA,gBAAM,wBAAwB,KAAK,+BAA+B,OAAO;AAEzE,cAAI,wBAAwB,KAAK;AAC/B,mBAAO;AAAA,UACT,WAAW,wBAAwB,KAAK;AACtC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,SAAS,OAAO;AACd,kBAAQ,KAAK,oDAAoD,KAAK;AACtE,iBAAO;AAAA,QACT;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKQ,+BAA+B,SAAsC;AAC3E,YAAI,QAAQ;AAGZ,cAAM,cAAc,QAAQ,SAAS;AACrC,YAAI,eAAe,GAAI,UAAS;AAChC,YAAI,eAAe,GAAI,UAAS;AAGhC,cAAM,qBAAqB,QAAQ,YAAY;AAAA,UAAO,OACpD,EAAE,SAAS,iBAAiB,EAAE,SAAS;AAAA,QACzC,EAAE;AACF,iBAAS,KAAK,IAAI,qBAAqB,MAAM,GAAG;AAGhD,YAAI,QAAQ,UAAU,YAAY,gBAAiB,UAAS;AAC5D,YAAI,QAAQ,UAAU,UAAW,UAAS;AAG1C,cAAM,iBAAiB,KAAK,mBAAmB,IAAI,SAAS,KAAK,CAAC;AAClE,cAAM,mBAAmB,KAAK,mBAAmB,IAAI,WAAW,KAAK,CAAC;AAEtE,YAAI,eAAe,SAAS,KAAK,iBAAiB,SAAS,GAAG;AAC5D,gBAAM,iBAAiB,eAAe,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,eAAe;AAC/E,gBAAM,mBAAmB,iBAAiB,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,iBAAiB;AAErF,cAAI,iBAAiB,iBAAkB,UAAS;AAAA,QAClD;AAEA,eAAO,KAAK,IAAI,OAAO,CAAG;AAAA,MAC5B;AAAA;AAAA;AAAA;AAAA,MAKQ,yBAAyB,SAAsD;AACrF,cAAM,aAAqC,CAAC;AAG5C,mBAAW,cAAc,QAAQ,cAAc;AAG/C,gBAAQ,SAAS,QAAQ,CAAC,SAAS,MAAM;AACvC,qBAAW,WAAW,CAAC,MAAM,IAAI,QAAQ,YAAY,QAAQ;AAC7D,qBAAW,WAAW,CAAC,MAAM,IAAI,QAAQ,YAAY,QAAQ;AAC7D,qBAAW,WAAW,CAAC,kBAAkB,IAAI,QAAQ;AACrD,qBAAW,WAAW,CAAC,MAAM,IAAI,QAAQ,oBAAoB;AAAA,QAC/D,CAAC;AAGD,mBAAW,iBAAiB,QAAQ,UAAU,QAAQ;AACtD,mBAAW,mBAAmB,QAAQ,UAAU,QAAQ,aAAa;AAErE,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKQ,kCACN,eACA,SACoB;AACpB,cAAM,cAAkC,CAAC;AACzC,cAAM,cAAc,QAAQ;AAG5B,cAAMA,OAAM,cAAc,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC;AACnD,cAAM,oBAAoB,cAAc,IAAI,OAAK,IAAIA,IAAG;AAExD,gBAAQ,SAAS,QAAQ,CAAC,SAAS,MAAM;AACvC,gBAAM,SAAS,kBAAkB,CAAC,KAAK;AACvC,gBAAM,SAAS,SAAS;AAGxB,gBAAM,oBAAoB,KAAK;AAAA,YAC7B,QAAQ;AAAA,YACR,KAAK,IAAI,QAAQ,WAAW,MAAM;AAAA,UACpC;AAGA,gBAAM,cAAc,KAAK,6BAA6B,mBAAmB,OAAO;AAChF,gBAAM,UAAU,cAAc,QAAQ;AAEtC,sBAAY,KAAK;AAAA,YACf,WAAW,QAAQ;AAAA,YACnB,iBAAiB;AAAA,YACjB,qBAAqB;AAAA,YACrB,iBAAiB;AAAA,YACjB,aAAa,oBAAoB,KAAK,IAAI,aAAa,CAAC;AAAA,YACxD,cAAc,UAAU,KAAK,IAAI,mBAAmB,CAAC;AAAA,YACrD,YAAY;AAAA;AAAA,YACZ,WAAW;AAAA;AAAA,UACb,CAAC;AAAA,QACH,CAAC;AAED,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKQ,6BAA6B,QAAgB,SAAsB;AAEzE,cAAM,kBAAkB,SAAS,QAAQ,iBAAiB,QAAQ;AAGlE,YAAI,mBAAmB;AACvB,YAAI,QAAQ,mBAAmB,SAAS,QAAQ,iBAAiB;AAC/D,6BAAmB,KAAK,KAAK,QAAQ,kBAAkB,MAAM;AAAA,QAC/D;AAEA,eAAO,kBAAkB,mBAAmB,QAAQ;AAAA,MACtD;AAAA;AAAA;AAAA;AAAA,MAKQ,0BAA0B,eAA4B;AAC5D,YAAI,YAAY;AAGhB,YAAI,cAAc,QAAQ,YAAY,MAAM;AAC1C,uBAAa;AAAA,QACf;AAGA,YAAI,cAAc,QAAQ,gBAAgB,IAAI;AAC5C,uBAAa;AAAA,QACf;AAGA,YAAI,cAAc,mBAAmB,KAAK;AACxC,uBAAa;AAAA,QACf;AAEA,eAAO,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,CAAC;AAAA,MAC5C;AAAA;AAAA;AAAA;AAAA,MAKQ,wBAAwB,aAAiC,SAAsC;AACrG,YAAI,QAAQ;AAEZ,gBAAQ,QAAQ,UAAU,SAAS;AAAA,UACjC,KAAK;AACH,oBAAQ,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,iBAAiB,CAAC;AACjE;AAAA,UACF,KAAK;AACH,oBAAQ,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,qBAAqB,CAAC;AACrE;AAAA,UACF,KAAK;AACH,kBAAM,WAAW,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,aAAa,CAAC,IAAI,YAAY;AACtF,oBAAQ,CAAC;AACT;AAAA,UACF,KAAK;AACH,kBAAM,YAAY,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,cAAc,CAAC,IAAI,YAAY;AACxF,oBAAQ;AACR;AAAA,QACJ;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKQ,+BAA+B,QAAmC;AACxE,cAAM,kBAAyB,CAAC;AAGhC,YAAI,OAAO,gBAAgB,oBAAoB,OAAO,eAAe,mBAAmB,KAAK;AAC3F,0BAAgB,KAAK;AAAA,YACnB,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,eAAe,OAAO,uBAAuB;AAAA,cAC7C,YAAY,OAAO,iBAAiB;AAAA,cACpC,YAAY;AAAA,YACd;AAAA,YACA,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAGA,YAAI,OAAO,gBAAgB,aAAa,OAAO,eAAe,YAAY,MAAM;AAC9E,0BAAgB,KAAK;AAAA,YACnB,MAAM;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,cACN,eAAe,OAAO,uBAAuB;AAAA,cAC7C,YAAY;AAAA,cACZ,YAAY;AAAA,YACd;AAAA,YACA,UAAU;AAAA,UACZ,CAAC;AAAA,QACH;AAEA,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKQ,wBAAwB,UAA+B,eAAwD;AAErH,cAAM,iBAAiB,EAAE,GAAG,SAAS;AAGrC,sBAAc,YAAY,QAAQ,CAAC,YAAY,MAAM;AACnD,gBAAM,YAAY;AAClB,gBAAM,YAAY,WAAW,mBAAmB,IAAI;AACpD,gBAAM,YAAY,WAAW,mBAAmB,IAAI;AAEpD,yBAAe,YAAY,KAAK;AAAA,YAC9B,IAAI,sBAAsB,CAAC;AAAA,YAC3B,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,YACV,UAAU;AAAA,YACV,UAAU;AAAA,YACV,WAAW;AAAA,UACb,CAAC;AAAA,QACH,CAAC;AAED,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKQ,eAAe,eAAmC,iBAAyD;AAEjH,cAAM,WAAW,EAAE,GAAG,cAAc;AAGpC,YAAI,gBAAgB,oBAAoB,iBAAiB,cAAc,oBAAoB,gBAAgB;AACzG,mBAAS,cAAc,gBAAgB;AACvC,mBAAS,iBAAiB,gBAAgB;AAC1C,mBAAS,uBAAuB,gBAAgB;AAChD,mBAAS,2BAA2B,gBAAgB;AACpD,mBAAS,cAAc,gBAAgB;AACvC,mBAAS,aAAa,gBAAgB;AAAA,QACxC;AAGA,iBAAS,cAAc,cAAc,aAAa,gBAAgB,cAAc;AAGhF,iBAAS,YAAY,KAAK,IAAI,cAAc,WAAW,gBAAgB,SAAS;AAEhF,eAAO;AAAA,MACT;AAAA;AAAA;AAAA;AAAA,MAKQ,yBAAyB,UAAkB,aAA2B;AAC5E,YAAI,CAAC,KAAK,mBAAmB,IAAI,QAAQ,GAAG;AAC1C,eAAK,mBAAmB,IAAI,UAAU,CAAC,CAAC;AAAA,QAC1C;AAEA,cAAM,UAAU,KAAK,mBAAmB,IAAI,QAAQ;AACpD,gBAAQ,KAAK,WAAW;AAGxB,YAAI,QAAQ,SAAS,IAAI;AACvB,kBAAQ,MAAM;AAAA,QAChB;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAKA,MAAM,UAAyB;AAC7B,cAAM,KAAK,aAAa,QAAQ;AAChC,aAAK,mBAAmB,MAAM;AAAA,MAChC;AAAA,IACF;AAAA;AAAA;;;ACxgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBO,SAAS,gBAAgB,QAAkD;AAChF,QAAM,gBAA8B;AAAA,IAClC,UAAU,QAAQ,IAAI,mBAAmB;AAAA,IACzC,QAAQ,QAAQ,IAAI,kBAAkB;AAAA,IACtC,WAAW,QAAQ,IAAI,wBAAwB;AAAA,IAC/C,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,MACb,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,SAAS,CAAC,KAAK,KAAK,KAAK,KAAK,QAAQ,MAAM,MAAM,MAAM,IAAI;AAAA,MAC5D,iBAAiB;AAAA,MACjB,eAAe;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,EACF;AAEA,QAAM,eAAe,EAAE,GAAG,eAAe,GAAG,OAAO;AAGnD,MAAI,CAAC,aAAa,QAAQ;AACxB,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,aAAa,WAAW;AAC3B,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,iBAAiB,YAAY;AAC1C;AAKO,SAAS,gBAAgB,SAI9B;AACA,QAAM,SAAmB,CAAC;AAC1B,QAAM,WAAqB,CAAC;AAE5B,MAAI;AAEF,YAAQ,oBAAoB,MAAM,OAAO;AAAA,EAC3C,SAAS,OAAY;AACnB,QAAI,MAAM,QAAQ;AAChB,aAAO,KAAK,GAAG,MAAM,OAAO,IAAI,CAAC,MAAW,GAAG,EAAE,KAAK,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;AAAA,IAClF,OAAO;AACL,aAAO,KAAK,MAAM,OAAO;AAAA,IAC3B;AAAA,EACF;AAGA,MAAI,QAAQ,SAAS,WAAW,GAAG;AACjC,WAAO,KAAK,kCAAkC;AAAA,EAChD;AAEA,MAAI,QAAQ,SAAS,SAAS,IAAI;AAChC,aAAS,KAAK,iDAAiD;AAAA,EACjE;AAGA,QAAM,iBAAiB,QAAQ,SAAS,OAAO,CAACC,MAAK,MAAMA,OAAM,EAAE,WAAW,CAAC;AAC/E,MAAI,iBAAiB,QAAQ,aAAa;AACxC,WAAO,KAAK,+CAA+C;AAAA,EAC7D;AAGA,QAAM,oBAAoB,QAAQ,YAAY,OAAO,OAAK,EAAE,SAAS,cAAc;AACnF,MAAI,kBAAkB,SAAS,GAAG;AAChC,aAAS,KAAK,4CAA4C;AAAA,EAC5D;AAGA,MAAI,QAAQ,UAAU,OAAO,QAAQ,UAAU,OAAO;AACpD,WAAO,KAAK,mCAAmC;AAAA,EACjD;AAEA,QAAM,iBAAiB,QAAQ,UAAU,IAAI,QAAQ,IAAI,QAAQ,UAAU,MAAM,QAAQ,MAAM,MAAO,KAAK,KAAK;AAChH,MAAI,gBAAgB,KAAK;AACvB,aAAS,KAAK,kDAAkD;AAAA,EAClE;AAEA,SAAO;AAAA,IACL,SAAS,OAAO,WAAW;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACF;AAKO,SAAS,aAAa,QAqB3B;AACA,QAAM,iBAAiB,CAAC,WACtB,IAAI,KAAK,aAAa,SAAS,EAAE,OAAO,YAAY,UAAU,MAAM,CAAC,EAAE,OAAO,MAAM;AAEtF,QAAM,mBAAmB,CAAC,UACxB,IAAI,KAAK,aAAa,SAAS,EAAE,OAAO,WAAW,uBAAuB,EAAE,CAAC,EAAE,OAAO,KAAK;AAE7F,QAAM,UAAU,GAAG,OAAO,OAAO,YAAY,CAAC,iBAAiB,OAAO,MAAM,SAAS,eAAe,OAAO,oBAAoB,CAAC,yBAAyB,iBAAiB,OAAO,UAAU,CAAC;AAE5L,QAAM,cAAc,OAAO,YAAY,IAAI,iBAAe;AAAA,IACxD,SAAS,WAAW;AAAA,IACpB,QAAQ,eAAe,WAAW,eAAe;AAAA,IACjD,YAAY,iBAAiB,WAAW,kBAAkB,OAAO,cAAc;AAAA,IAC/E,cAAc,WAAW,aAAa,QAAQ,CAAC,IAAI;AAAA,IACnD,aAAa,eAAe,WAAW,WAAW;AAAA,IAClD,YAAY,iBAAiB,WAAW,UAAU;AAAA,EACpD,EAAE;AAEF,QAAM,UAAU;AAAA,IACd,aAAa,eAAe,OAAO,cAAc;AAAA,IACjD,iBAAiB,eAAe,OAAO,oBAAoB;AAAA,IAC3D,aAAa,OAAO,YAAY,QAAQ,CAAC,IAAI;AAAA,IAC7C,YAAY,eAAe,OAAO,UAAU;AAAA,IAC5C,YAAY,iBAAiB,OAAO,UAAU;AAAA,IAC9C,WAAW,GAAG,OAAO,SAAS;AAAA,IAC9B,QAAQ,OAAO,OAAO,YAAY;AAAA,IAClC,aAAa,GAAG,OAAO,WAAW;AAAA,EACpC;AAEA,QAAM,kBAAkB,OAAO,gBAAgB;AAAA,IAAI,SACjD,GAAG,IAAI,SAAS,YAAY,CAAC,KAAK,IAAI,WAAW;AAAA,EACnD;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKO,SAAS,uBAAuB,QAKrC;AACA,QAAM,cAAc,OAAO;AAC3B,QAAM,UAAU,YAAY,IAAI,OAAK,EAAE,kBAAkB,OAAO,cAAc;AAG9E,QAAM,uBAAuB,IAAI,yBAAyB,OAAO;AAGjE,QAAM,oBAAoB,KAAK,IAAI,GAAG,OAAO;AAG7C,QAAM,eAAe,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,WAAW,CAAC,IAAI,YAAY;AACxF,QAAM,kBAAkB,eAAe;AAGvC,QAAM,eAAe,oBAAoB,mBAAmB,IAAI,yBAAyB;AAEzF,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKA,SAAS,yBAAyB,SAA2B;AAC3D,SAAO,QAAQ,OAAO,CAACA,MAAK,WAAWA,OAAM,SAAS,QAAQ,CAAC;AACjE;AAKO,SAAS,iBAAiB,QAK/B;AACA,QAAM,cAAc,OAAO;AAG3B,QAAM,eAAe,CAAC,GAAG,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,eAAe,EAAE,YAAY;AAEpF,QAAM,gBAAgB,aACnB,MAAM,GAAG,KAAK,KAAK,YAAY,SAAS,GAAG,CAAC,EAC5C,IAAI,OAAK,EAAE,SAAS;AAEvB,QAAM,kBAAkB,aACrB,MAAM,CAAC,KAAK,KAAK,YAAY,SAAS,GAAG,CAAC,EAC1C,OAAO,OAAK,EAAE,eAAe,OAAO,WAAW,EAC/C,IAAI,OAAK,EAAE,SAAS;AAEvB,QAAM,gBAA0B,CAAC;AACjC,QAAM,QAAkB,CAAC;AAEzB,cAAY,QAAQ,gBAAc;AAChC,QAAI,WAAW,aAAa,KAAK;AAC/B,YAAM,KAAK,qBAAqB,WAAW,SAAS,aAAa;AAAA,IACnE;AAEA,QAAI,WAAW,YAAY,GAAG;AAC5B,YAAM,KAAK,0BAA0B,WAAW,SAAS,EAAE;AAAA,IAC7D;AAEA,QAAI,WAAW,eAAe,OAAO,cAAc,KAAK;AACtD,oBAAc,KAAK,kDAAkD,WAAW,SAAS,EAAE;AAAA,IAC7F;AAEA,QAAI,WAAW,kBAAkB,OAAO,iBAAiB,KAAK;AAC5D,YAAM,KAAK,8BAA8B,WAAW,SAAS,EAAE;AAAA,IACjE;AAAA,EACF,CAAC;AAED,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAKO,SAAS,aAAa,QAA4B,QAAkD;AACzG,UAAQ,QAAQ;AAAA,IACd,KAAK;AACH,aAAO,KAAK,UAAU,QAAQ,MAAM,CAAC;AAAA,IAEvC,KAAK;AACH,YAAM,UAAU,CAAC,WAAW,UAAU,oBAAoB,wBAAwB,OAAO,QAAQ,cAAc,YAAY;AAC3H,YAAM,OAAO,OAAO,YAAY,IAAI,OAAK;AAAA,QACvC,EAAE;AAAA,QACF,EAAE,gBAAgB,SAAS;AAAA,QAC3B,EAAE,gBAAgB,SAAS;AAAA,QAC3B,EAAE,oBAAoB,SAAS;AAAA,QAC/B,EAAE,YAAY,SAAS;AAAA,QACvB,EAAE,aAAa,SAAS;AAAA,QACxB,EAAE,WAAW,SAAS;AAAA,QACtB,EAAE,UAAU,SAAS;AAAA,MACvB,CAAC;AAED,aAAO,CAAC,SAAS,GAAG,IAAI,EAAE,IAAI,SAAO,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI;AAAA,IAE/D,KAAK;AAEH,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAE/D;AACE,YAAM,IAAI,MAAM,8BAA8B,MAAM,EAAE;AAAA,EAC1D;AACF;AAKO,SAAS,eAAe,SAA6B,SAQ1D;AACA,QAAM,oBAAoB,QAAQ,uBAAuB,QAAQ;AACjE,QAAM,iBAAiB,QAAQ,cAAc,QAAQ;AACrD,QAAM,gBAAgB,QAAQ,aAAa,QAAQ;AACnD,QAAM,uBAAuB,QAAQ,aAAa,QAAQ;AAC1D,QAAM,iBAAiB,QAAQ,YAAY,QAAQ;AAEnD,MAAI,eAAkD;AACtD,MAAI,SAAS;AACb,MAAI,SAAS;AAGb,MAAI,oBAAoB,EAAG;AAAA,WAAmB,oBAAoB,EAAG;AACrE,MAAI,iBAAiB,EAAG;AAAA,WAAmB,iBAAiB,EAAG;AAC/D,MAAI,gBAAgB,EAAG;AAAA,WAAmB,gBAAgB,EAAG;AAC7D,MAAI,uBAAuB,EAAG;AAAA,WAAmB,uBAAuB,EAAG;AAC3E,MAAI,iBAAiB,EAAG;AAAA,WAAmB,iBAAiB,EAAG;AAE/D,MAAI,SAAS,OAAQ,gBAAe;AAAA,WAC3B,SAAS,OAAQ,gBAAe;AAEzC,QAAM,UAAU,sBAAsB,iBAAiB,YAAY,wBACjE,iBAAiB,YAAY,2BAA2B,yBAAyB;AAAA,gBACrE,oBAAoB,IAAI,MAAM,EAAE,GAAG,kBAAkB,QAAQ,CAAC,CAAC;AAAA,YACnE,iBAAiB,IAAI,MAAM,EAAE,GAAG,eAAe,QAAQ,CAAC,CAAC;AAEnE,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AA7VA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AAmBA;AAOA;AAGA;AASA;AACA;AACA;AAGO,IAAM,oBAAoB;AAAA,EAC/B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA;AAAA,EACxB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,eAAe;AACjB;AAEO,IAAM,0BAA0B;AAAA,EACrC,SAAS,CAAC,OAAO,QAAQ,mBAAmB;AAAA,EAC5C,WAAW,CAAC,OAAO,WAAW,OAAO,uBAAuB,gBAAgB;AAAA,EAC5E,QAAQ,CAAC,qBAAqB,UAAU;AAC1C;AAGO,IAAM,UAAU;AAKvB,eAAsB,eACpB,aACA,UACA,YAA4F,oBAC5F,cACc;AACd,QAAM,EAAE,iBAAAC,iBAAgB,IAAI,MAAM;AAClC,QAAM,YAAYA,iBAAgB,YAAY;AAE9C,QAAM,UAAU;AAAA,IACd,IAAI,SAAS,KAAK,IAAI,CAAC;AAAA,IACvB;AAAA,IACA;AAAA,IACA,aAAa;AAAA,MACX;AAAA,QACE,IAAI;AAAA,QACJ,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,SAAS;AAAA,MACT,SAAS,EAAE,SAAS,GAAK,WAAW,EAAI;AAAA,MACxC,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO,oBAAI,KAAK;AAAA,MAChB,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,KAAK,GAAI;AAAA;AAAA,IACrD;AAAA,IACA,aAAa;AAAA,MACX,wBAAwB;AAAA,MACxB,0BAA0B;AAAA,MAC1B,eAAe;AAAA,MACf,sBAAsB;AAAA,MACtB,eAAe;AAAA,IACjB;AAAA,EACF;AAEA,SAAO,MAAM,UAAU,SAAS,OAAO;AACzC;AAKA,eAAsB,cACpB,UACA,cACgB;AAChB,QAAM,EAAE,iBAAAA,iBAAgB,IAAI,MAAM;AAClC,QAAM,YAAYA,iBAAgB,YAAY;AAC9C,QAAM,UAAiB,CAAC;AAExB,aAAW,WAAW,UAAU;AAC9B,QAAI;AACF,YAAM,SAAS,MAAM,UAAU,SAAS,OAAO;AAC/C,cAAQ,KAAK,MAAM;AAAA,IACrB,SAAS,OAAO;AACd,cAAQ,MAAM,yCAAyC,QAAQ,EAAE,KAAK,KAAK;AAAA,IAE7E;AAAA,EACF;AAEA,QAAM,UAAU,QAAQ;AACxB,SAAO;AACT;AAKA,eAAsB,2BACpB,SACA,cASC;AACD,QAAM,EAAE,iBAAAA,iBAAgB,IAAI,MAAM;AAClC,QAAM,EAAE,iBAAAC,iBAAgB,IAAI,MAAM;AAClC,QAAM,YAAYD,iBAAgB,YAAY;AAC9C,QAAM,kBAAkB,IAAIC,iBAAgB;AAG5C,QAAM,kBAAkB,MAAM,gBAAgB,SAAS,OAAO;AAG9D,MAAI,gBAA4B;AAChC,MAAI;AACF,UAAM,iBAAiB,EAAE,GAAG,SAAS,aAAa,EAAE,GAAG,QAAQ,aAAa,wBAAwB,KAAK,EAAE;AAC3G,oBAAgB,MAAM,UAAU,SAAS,cAAc;AAAA,EACzD,SAAS,OAAO;AACd,YAAQ,KAAK,kDAAkD,KAAK;AAAA,EACtE;AAGA,MAAI,mBAAmB;AACvB,MAAI,wBAAwB;AAC5B,MAAI,iBAAqD;AAEzD,MAAI,eAAe;AACjB,uBAAmB,cAAc,gBAAgB,oBAAoB;AAErE,UAAM,mBAAmB,cAAc,oBAAoB;AAC3D,UAAM,qBAAqB,gBAAgB,oBAAoB;AAE/D,6BAAyB,mBAAmB,sBAAsB,KAAK,IAAI,kBAAkB;AAE7F,QAAI,wBAAwB,OAAO,mBAAmB,KAAK;AACzD,uBAAiB;AAAA,IACnB,WAAW,wBAAwB,QAAQ,mBAAmB,KAAK;AACjE,uBAAiB;AAAA,IACnB;AAAA,EACF;AAEA,QAAM,UAAU,QAAQ;AAExB,SAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAKO,IAAM,sBAAN,MAA0B;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf;AAAA,EAER,YAAY,cAAoB;AAE9B,SAAK,YAAY;AACjB,SAAK,oBAAoB,YAAY;AAAA,EACvC;AAAA,EAEA,MAAc,oBAAoB,cAAoB;AACpD,UAAM,EAAE,iBAAAD,iBAAgB,IAAI,MAAM;AAClC,SAAK,YAAYA,iBAAgB,YAAY;AAAA,EAC/C;AAAA,EAEA,MAAM,gBACJ,SACA,UACA,aAAqB,KACN;AACf,QAAI,KAAK,cAAc;AACrB,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC7C;AAGA,QAAI,CAAC,KAAK,WAAW;AACnB,YAAM,KAAK,oBAAoB;AAAA,IACjC;AAEA,SAAK,eAAe;AAEpB,UAAM,kBAAkB,YAAY;AAClC,UAAI;AACF,cAAM,SAAS,MAAM,KAAK,UAAU,SAAS,OAAO;AACpD,iBAAS,MAAM;AAAA,MACjB,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AAAA,MACxD;AAAA,IACF;AAGA,UAAM,gBAAgB;AAGtB,SAAK,qBAAqB,YAAY,iBAAiB,UAAU;AAAA,EACnE;AAAA,EAEA,iBAAuB;AACrB,SAAK,eAAe;AACpB,QAAI,KAAK,oBAAoB;AAC3B,oBAAc,KAAK,kBAAkB;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EAEA,MAAM,UAAyB;AAC7B,SAAK,eAAe;AACpB,UAAM,KAAK,UAAU,QAAQ;AAAA,EAC/B;AACF;AAGA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,cAAQ;", "names": ["axios", "sum", "sum", "createOptimizer", "ClassicalSolver"]}