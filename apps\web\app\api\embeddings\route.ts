import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';
import { generateEmbedding, generatePersonaEmbedding, generateCreativeEmbedding } from '@/lib/ai/embeddings';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const supabase = createServerClient();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { type, data } = body;

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      );
    }

    let embedding: number[];

    switch (type) {
      case 'text':
        if (typeof data.text !== 'string') {
          return NextResponse.json(
            { error: 'Text field is required for text embeddings' },
            { status: 400 }
          );
        }
        embedding = await generateEmbedding(data.text);
        break;

      case 'persona':
        if (!data.name) {
          return NextResponse.json(
            { error: 'Name field is required for persona embeddings' },
            { status: 400 }
          );
        }
        embedding = await generatePersonaEmbedding(data);
        break;

      case 'creative':
        if (!data.name || !data.type) {
          return NextResponse.json(
            { error: 'Name and type fields are required for creative embeddings' },
            { status: 400 }
          );
        }
        embedding = await generateCreativeEmbedding(data);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid embedding type. Supported types: text, persona, creative' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      embedding,
      dimensions: embedding.length,
      type,
    });

  } catch (error) {
    console.error('Embedding generation error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate embedding',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(): Promise<NextResponse> {
  return NextResponse.json({
    message: 'Metamorphic Flux Embedding API',
    endpoints: {
      POST: {
        description: 'Generate embeddings for text, personas, or creatives',
        body: {
          type: 'text | persona | creative',
          data: 'object containing the data to embed'
        },
        examples: {
          text: {
            type: 'text',
            data: { text: 'Your text content here' }
          },
          persona: {
            type: 'persona',
            data: {
              name: 'Tech-Savvy Millennials',
              description: 'Young professionals interested in technology',
              demographics: { age_range: '25-35', income: '50k-100k' },
              interests: ['technology', 'innovation']
            }
          },
          creative: {
            type: 'creative',
            data: {
              name: 'Hero Image Campaign',
              type: 'image',
              copy_headline: 'Transform Your Marketing with AI',
              copy_description: 'Discover the power of AI-driven campaigns'
            }
          }
        }
      }
    }
  });
}
