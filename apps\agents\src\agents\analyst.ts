import { z } from 'zod';
import { BaseAgent } from './base';
import { 
  AnalystAgentInputSchema, 
  AgentOutputSchema,
  type AnalystAgentInput,
  type AgentOutput,
  type AgentContext,
  type AgentConfig 
} from '@/types';
import { AnalyticsService } from '@/utils/analytics';

export class Analyst<PERSON>gent extends BaseAgent {
  private analyticsService: AnalyticsService;

  constructor() {
    const config: AgentConfig = {
      name: 'Analyst Agent',
      type: 'analyst',
      model: 'gemini-1.5-flash',
      temperature: 0.3, // Lower temperature for more analytical responses
      maxTokens: 3072,
      systemPrompt: `You are an expert data analyst specializing in marketing campaign performance analysis. Your role is to analyze campaign metrics, identify trends, and provide actionable insights for optimization.

Key responsibilities:
- Analyze campaign performance data across multiple metrics
- Identify trends, patterns, and anomalies in the data
- Provide actionable recommendations for optimization
- Compare performance across different segments and time periods
- Predict future performance based on current trends
- Generate comprehensive reports with clear visualizations

Always respond with valid JSON containing:
- performance_summary: Overall performance overview
- key_metrics: Important metrics and their values
- trends_analysis: Trend identification and analysis
- segment_performance: Performance by different segments
- optimization_recommendations: Specific actionable recommendations
- predictions: Future performance predictions
- alerts: Any critical issues or opportunities
- confidence: Confidence score (0-1)
- reasoning: Explanation of analysis methodology`,
    };
    super(config);
    this.analyticsService = new AnalyticsService();
  }

  get inputSchema() {
    return AnalystAgentInputSchema;
  }

  get outputSchema() {
    return AgentOutputSchema;
  }

  protected async processInput(input: AnalystAgentInput, context: AgentContext): Promise<AgentOutput> {
    const { timeframe, metrics, campaignId, creativeIds } = input;

    try {
      // Fetch campaign performance data
      const performanceData = await this.analyticsService.getCampaignPerformance({
        campaignId,
        timeframe,
        metrics,
        ...(creativeIds && { creativeIds }),
      });

      // Fetch historical data for trend analysis
      const historicalData = await this.analyticsService.getHistoricalData({
        campaignId,
        timeframe: this.expandTimeframe(timeframe),
        metrics,
      });

      // Fetch benchmark data
      const benchmarkData = await this.analyticsService.getBenchmarkData({
        industry: context.campaign.objectives?.industry || 'general',
        channels: context.campaign.channels || [],
        metrics,
      });

      // Build analysis prompt
      const analysisPrompt = this.buildAnalysisPrompt(
        performanceData,
        historicalData,
        benchmarkData,
        context,
        input
      );

      const response = await this.callModel(this.config.systemPrompt, analysisPrompt, context);
      const parsedResponse = this.parseJsonResponse(response);

      // Calculate confidence based on data quality and analysis depth
      const confidence = this.calculateConfidence({
        inputQuality: this.assessDataQuality(performanceData),
        outputCoherence: this.assessAnalysisQuality(parsedResponse),
        alignmentWithObjectives: this.assessInsightRelevance(parsedResponse, context),
        technicalCorrectness: this.assessStatisticalValidity(parsedResponse, performanceData),
      });

      return {
        success: true,
        data: {
          performance_summary: parsedResponse.performance_summary,
          key_metrics: parsedResponse.key_metrics,
          trends_analysis: parsedResponse.trends_analysis,
          segment_performance: parsedResponse.segment_performance,
          optimization_recommendations: parsedResponse.optimization_recommendations,
          predictions: parsedResponse.predictions,
          alerts: parsedResponse.alerts,
          timeframe,
          data_points: performanceData.length,
          benchmark_comparison: this.compareToBenchmarks(performanceData, benchmarkData),
        },
        confidence,
        reasoning: parsedResponse.reasoning || 'Analysis based on campaign performance data and industry benchmarks',
        suggestions: this.generateAnalysisSuggestions(parsedResponse, performanceData),
        metadata: {
          timeframe,
          metrics_analyzed: metrics,
          campaign_id: campaignId,
          creative_ids: creativeIds,
          data_quality_score: this.assessDataQuality(performanceData),
          analysis_timestamp: new Date().toISOString(),
        },
      };

    } catch (error) {
      throw new Error(`Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private buildAnalysisPrompt(
    performanceData: any[],
    historicalData: any[],
    benchmarkData: any,
    context: AgentContext,
    input: AnalystAgentInput
  ): string {
    const campaignContext = this.formatCampaignContext(context.campaign);
    const performanceContext = this.formatPerformanceData(performanceData);
    const historicalContext = this.formatHistoricalData(historicalData);
    const benchmarkContext = this.formatBenchmarkData(benchmarkData);

    return `
Analyze the following campaign performance data:

CAMPAIGN CONTEXT:
${campaignContext}

CURRENT PERFORMANCE DATA (${input.timeframe}):
${performanceContext}

HISTORICAL TRENDS:
${historicalContext}

INDUSTRY BENCHMARKS:
${benchmarkContext}

ANALYSIS REQUIREMENTS:
- Timeframe: ${input.timeframe}
- Metrics to focus on: ${input.metrics.join(', ')}
- Campaign objectives: ${JSON.stringify(context.campaign.objectives || {})}

Please provide a comprehensive analysis including:
1. Overall performance assessment
2. Key metric trends and patterns
3. Segment performance comparison
4. Optimization opportunities
5. Future performance predictions
6. Critical alerts or recommendations

Respond with JSON in this exact format:
{
  "performance_summary": "Overall performance overview with key highlights",
  "key_metrics": {
    "metric_name": {
      "current_value": 0.0,
      "trend": "increasing/decreasing/stable",
      "vs_benchmark": "above/below/at benchmark",
      "significance": "high/medium/low"
    }
  },
  "trends_analysis": "Detailed trend analysis with patterns identified",
  "segment_performance": "Performance breakdown by segments",
  "optimization_recommendations": [
    "Specific actionable recommendation 1",
    "Specific actionable recommendation 2"
  ],
  "predictions": "Future performance predictions based on current trends",
  "alerts": ["Critical alert 1", "Opportunity alert 2"],
  "confidence": 0.85,
  "reasoning": "Explanation of analysis methodology and key findings"
}`;
  }

  private formatPerformanceData(data: any[]): string {
    if (!data || data.length === 0) {
      return 'No performance data available for the specified timeframe.';
    }

    return data.map(item => {
      const metrics = Object.entries(item.metrics || {})
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
      return `${item.date}: ${metrics}`;
    }).join('\n');
  }

  private formatHistoricalData(data: any[]): string {
    if (!data || data.length === 0) {
      return 'Limited historical data available.';
    }

    // Aggregate historical data by time periods
    const periods = this.aggregateByPeriods(data);
    return Object.entries(periods)
      .map(([period, metrics]) => `${period}: ${JSON.stringify(metrics)}`)
      .join('\n');
  }

  private formatBenchmarkData(data: any): string {
    if (!data) {
      return 'No benchmark data available.';
    }

    return Object.entries(data)
      .map(([metric, value]) => `${metric}: ${value} (industry average)`)
      .join('\n');
  }

  private expandTimeframe(timeframe: string): string {
    // Expand timeframe for historical analysis
    const timeframeMap: Record<string, string> = {
      '24h': '7d',
      '7d': '30d',
      '30d': '90d',
      '90d': '1y',
    };
    return timeframeMap[timeframe] || '30d';
  }

  private aggregateByPeriods(data: any[]): Record<string, any> {
    // Simple aggregation - in production this would be more sophisticated
    const periods: Record<string, any> = {};
    
    data.forEach(item => {
      const period = this.getPeriodKey(item.date);
      if (!periods[period]) {
        periods[period] = {};
      }
      
      Object.entries(item.metrics || {}).forEach(([metric, value]) => {
        if (!periods[period][metric]) {
          periods[period][metric] = [];
        }
        periods[period][metric].push(value);
      });
    });

    // Calculate averages
    Object.keys(periods).forEach(period => {
      Object.keys(periods[period]).forEach(metric => {
        const values = periods[period][metric];
        periods[period][metric] = values.reduce((sum: number, val: number) => sum + val, 0) / values.length;
      });
    });

    return periods;
  }

  private getPeriodKey(date: string): string {
    // Simple period grouping - could be enhanced
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}`;
  }

  private compareToBenchmarks(performanceData: any[], benchmarkData: any): Record<string, string> {
    const comparison: Record<string, string> = {};
    
    if (!benchmarkData || !performanceData.length) {
      return comparison;
    }

    // Calculate current averages
    const currentMetrics = this.calculateAverageMetrics(performanceData);
    
    Object.entries(currentMetrics).forEach(([metric, value]) => {
      const benchmark = benchmarkData[metric];
      if (benchmark) {
        const ratio = (value as number) / benchmark;
        if (ratio > 1.1) {
          comparison[metric] = 'significantly above benchmark';
        } else if (ratio > 1.05) {
          comparison[metric] = 'above benchmark';
        } else if (ratio > 0.95) {
          comparison[metric] = 'at benchmark';
        } else if (ratio > 0.9) {
          comparison[metric] = 'below benchmark';
        } else {
          comparison[metric] = 'significantly below benchmark';
        }
      }
    });

    return comparison;
  }

  private calculateAverageMetrics(data: any[]): Record<string, number> {
    const metrics: Record<string, number[]> = {};
    
    data.forEach(item => {
      Object.entries(item.metrics || {}).forEach(([metric, value]) => {
        if (!metrics[metric]) {
          metrics[metric] = [];
        }
        metrics[metric].push(value as number);
      });
    });

    const averages: Record<string, number> = {};
    Object.entries(metrics).forEach(([metric, values]) => {
      averages[metric] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    return averages;
  }

  private assessDataQuality(data: any[]): number {
    if (!data || data.length === 0) return 0.1;
    
    let score = 0.5;
    
    // Check data completeness
    if (data.length >= 7) score += 0.2; // At least a week of data
    if (data.length >= 30) score += 0.1; // At least a month of data
    
    // Check metric completeness
    const avgMetricsPerPoint = data.reduce((sum, item) => 
      sum + Object.keys(item.metrics || {}).length, 0) / data.length;
    
    if (avgMetricsPerPoint >= 3) score += 0.1;
    if (avgMetricsPerPoint >= 5) score += 0.1;

    return Math.min(score, 1);
  }

  private assessAnalysisQuality(output: any): number {
    let score = 0.5;

    // Check completeness of analysis
    if (output.performance_summary && output.performance_summary.length > 50) score += 0.1;
    if (output.trends_analysis && output.trends_analysis.length > 50) score += 0.1;
    if (output.optimization_recommendations && output.optimization_recommendations.length > 0) score += 0.2;
    if (output.predictions && output.predictions.length > 30) score += 0.1;

    return Math.min(score, 1);
  }

  private assessInsightRelevance(output: any, context: AgentContext): number {
    let score = 0.5;

    // Check if recommendations align with campaign objectives
    const recommendations = JSON.stringify(output.optimization_recommendations || []).toLowerCase();
    const objectives = JSON.stringify(context.campaign.objectives || {}).toLowerCase();

    if (objectives.includes('awareness') && recommendations.includes('reach')) score += 0.2;
    if (objectives.includes('conversion') && recommendations.includes('conversion')) score += 0.2;
    if (objectives.includes('engagement') && recommendations.includes('engagement')) score += 0.1;

    return Math.min(score, 1);
  }

  private assessStatisticalValidity(output: any, data: any[]): number {
    let score = 0.5;

    // Basic statistical validity checks
    if (data.length >= 30) score += 0.2; // Sufficient sample size
    if (output.confidence && output.confidence >= 0.7) score += 0.2;
    if (output.reasoning && output.reasoning.includes('trend')) score += 0.1;

    return Math.min(score, 1);
  }

  private generateAnalysisSuggestions(output: any, data: any[]): string[] {
    const suggestions: string[] = [];

    // Data-driven suggestions
    if (data.length < 7) {
      suggestions.push('Collect more data points for more reliable trend analysis');
    }

    if (output.alerts && output.alerts.length > 0) {
      suggestions.push('Address critical alerts identified in the analysis');
    }

    if (output.optimization_recommendations && output.optimization_recommendations.length > 3) {
      suggestions.push('Prioritize optimization recommendations by expected impact');
    }

    return suggestions;
  }
}
