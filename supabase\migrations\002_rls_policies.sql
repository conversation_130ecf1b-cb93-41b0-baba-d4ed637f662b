-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.creatives ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.campaign_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.optimization_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_logs ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's organization IDs
CREATE OR REPLACE FUNCTION get_user_organization_ids(user_uuid UUID)
RETURNS UUID[] AS $$
BEGIN
  RETURN ARRAY(
    SELECT organization_id 
    FROM organization_members 
    WHERE user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users policies
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Organizations policies
CREATE POLICY "Users can view organizations they belong to" ON organizations
  FOR SELECT USING (id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Organization admins can update organization" ON organizations
  FOR UPDATE USING (
    id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'owner')
    )
  );

-- Organization members policies
CREATE POLICY "Users can view organization members" ON organization_members
  FOR SELECT USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Organization admins can manage members" ON organization_members
  FOR ALL USING (
    organization_id IN (
      SELECT organization_id 
      FROM organization_members 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'owner')
    )
  );

-- Personas policies
CREATE POLICY "Users can view organization personas" ON personas
  FOR SELECT USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Users can create personas in their organizations" ON personas
  FOR INSERT WITH CHECK (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Users can update personas in their organizations" ON personas
  FOR UPDATE USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Users can delete personas in their organizations" ON personas
  FOR DELETE USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

-- Campaigns policies
CREATE POLICY "Users can view organization campaigns" ON campaigns
  FOR SELECT USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Users can create campaigns in their organizations" ON campaigns
  FOR INSERT WITH CHECK (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Users can update campaigns in their organizations" ON campaigns
  FOR UPDATE USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

CREATE POLICY "Users can delete campaigns in their organizations" ON campaigns
  FOR DELETE USING (organization_id = ANY(get_user_organization_ids(auth.uid())));

-- Creatives policies
CREATE POLICY "Users can view campaign creatives" ON creatives
  FOR SELECT USING (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

CREATE POLICY "Users can create creatives for their campaigns" ON creatives
  FOR INSERT WITH CHECK (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

CREATE POLICY "Users can update creatives for their campaigns" ON creatives
  FOR UPDATE USING (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

CREATE POLICY "Users can delete creatives for their campaigns" ON creatives
  FOR DELETE USING (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

-- Campaign logs policies
CREATE POLICY "Users can view campaign logs" ON campaign_logs
  FOR SELECT USING (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

CREATE POLICY "System can insert campaign logs" ON campaign_logs
  FOR INSERT WITH CHECK (true); -- Allow system inserts

-- Optimization jobs policies
CREATE POLICY "Users can view optimization jobs" ON optimization_jobs
  FOR SELECT USING (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

CREATE POLICY "Users can create optimization jobs" ON optimization_jobs
  FOR INSERT WITH CHECK (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

-- Agent logs policies
CREATE POLICY "Users can view agent logs" ON agent_logs
  FOR SELECT USING (
    campaign_id IN (
      SELECT id FROM campaigns 
      WHERE organization_id = ANY(get_user_organization_ids(auth.uid()))
    )
  );

CREATE POLICY "System can insert agent logs" ON agent_logs
  FOR INSERT WITH CHECK (true); -- Allow system inserts

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Grant limited permissions to anon for auth flows
GRANT SELECT ON users TO anon;
GRANT INSERT ON users TO anon;
