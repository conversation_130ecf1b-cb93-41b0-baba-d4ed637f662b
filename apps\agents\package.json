{"name": "@metamorphic-flux/agents", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@langchain/core": "^0.3.29", "@langchain/google-genai": "^0.1.6", "@langchain/langgraph": "^0.2.28", "@google/generative-ai": "^0.21.0", "@supabase/supabase-js": "^2.48.0", "dotenv": "^16.4.7", "zod": "^3.24.1", "uuid": "^11.0.3", "winston": "^3.17.0", "express": "^4.21.2", "cors": "^2.8.5", "helmet": "^8.0.0"}, "devDependencies": {"@types/node": "^22.10.2", "@types/uuid": "^10.0.0", "@types/express": "^5.0.0", "@types/cors": "^2.8.17", "typescript": "^5.7.2", "tsx": "^4.19.2", "vitest": "^2.1.8", "eslint": "^9.18.0", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1"}}