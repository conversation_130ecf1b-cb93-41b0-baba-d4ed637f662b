import { z } from 'zod';

// Budget Constraint Types
export const BudgetConstraintSchema = z.object({
  id: z.string(),
  type: z.enum(['total_budget', 'channel_budget', 'daily_budget', 'cpa_target', 'roas_target']),
  value: z.number().positive(),
  operator: z.enum(['<=', '>=', '=', '<', '>']),
  priority: z.number().min(1).max(10).default(5),
  flexible: z.boolean().default(false),
  tolerance: z.number().min(0).max(1).default(0.1),
});

export type BudgetConstraint = z.infer<typeof BudgetConstraintSchema>;

// Channel Configuration
export const ChannelConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['facebook', 'google', 'linkedin', 'twitter', 'tiktok', 'email', 'sms']),
  minBudget: z.number().min(0),
  maxBudget: z.number().positive(),
  costModel: z.enum(['cpc', 'cpm', 'cpa', 'cpv', 'flat']),
  baseRate: z.number().positive(),
  scalingFactor: z.number().positive().default(1.0),
  saturationPoint: z.number().positive().optional(),
  conversionRate: z.number().min(0).max(1),
  averageOrderValue: z.number().positive(),
  seasonality: z.array(z.number()).length(12).default(Array(12).fill(1.0)),
  competitiveIndex: z.number().min(0).max(10).default(5),
});

export type ChannelConfig = z.infer<typeof ChannelConfigSchema>;

// Optimization Objective
export const OptimizationObjectiveSchema = z.object({
  primary: z.enum(['maximize_revenue', 'maximize_conversions', 'minimize_cpa', 'maximize_roas']),
  secondary: z.enum(['maximize_reach', 'minimize_cost', 'maximize_engagement']).optional(),
  weights: z.object({
    primary: z.number().min(0).max(1).default(0.8),
    secondary: z.number().min(0).max(1).default(0.2),
  }),
  timeHorizon: z.enum(['daily', 'weekly', 'monthly', 'quarterly']).default('monthly'),
});

export type OptimizationObjective = z.infer<typeof OptimizationObjectiveSchema>;

// Budget Allocation Result
export const BudgetAllocationSchema = z.object({
  channelId: z.string(),
  allocatedBudget: z.number().min(0),
  expectedConversions: z.number().min(0),
  expectedRevenue: z.number().min(0),
  expectedCPA: z.number().positive(),
  expectedROAS: z.number().positive(),
  confidence: z.number().min(0).max(1),
  riskScore: z.number().min(0).max(10),
});

export type BudgetAllocation = z.infer<typeof BudgetAllocationSchema>;

// Optimization Request
export const OptimizationRequestSchema = z.object({
  id: z.string(),
  totalBudget: z.number().positive(),
  channels: z.array(ChannelConfigSchema),
  constraints: z.array(BudgetConstraintSchema),
  objective: OptimizationObjectiveSchema,
  timeframe: z.object({
    start: z.date(),
    end: z.date(),
  }),
  historicalData: z.object({
    channelPerformance: z.record(z.string(), z.array(z.object({
      date: z.date(),
      spend: z.number().min(0),
      conversions: z.number().min(0),
      revenue: z.number().min(0),
    }))),
    marketConditions: z.array(z.object({
      date: z.date(),
      competitiveIndex: z.number().min(0).max(10),
      seasonalityFactor: z.number().positive(),
      economicIndicator: z.number(),
    })),
  }).optional(),
  preferences: z.object({
    useQuantumOptimization: z.boolean().default(true),
    quantumFallbackThreshold: z.number().min(0).max(1).default(0.95),
    maxIterations: z.number().positive().default(1000),
    convergenceThreshold: z.number().positive().default(0.001),
    riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).default('moderate'),
  }).default({}),
});

export type OptimizationRequest = z.infer<typeof OptimizationRequestSchema>;

// Optimization Result
export const OptimizationResultSchema = z.object({
  id: z.string(),
  requestId: z.string(),
  status: z.enum(['success', 'partial', 'failed']),
  method: z.enum(['quantum', 'classical', 'hybrid']),
  allocations: z.array(BudgetAllocationSchema),
  totalAllocated: z.number().min(0),
  expectedTotalRevenue: z.number().min(0),
  expectedTotalConversions: z.number().min(0),
  overallROAS: z.number().positive(),
  overallCPA: z.number().positive(),
  confidence: z.number().min(0).max(1),
  riskScore: z.number().min(0).max(10),
  optimizationMetrics: z.object({
    iterations: z.number().positive(),
    convergenceTime: z.number().positive(),
    objectiveValue: z.number(),
    constraintViolations: z.array(z.object({
      constraintId: z.string(),
      violation: z.number(),
      severity: z.enum(['low', 'medium', 'high']),
    })),
  }),
  quantumMetrics: z.object({
    quantumAdvantage: z.number().min(0).max(1).optional(),
    coherenceTime: z.number().positive().optional(),
    gateCount: z.number().positive().optional(),
    errorRate: z.number().min(0).max(1).optional(),
    willowChipUtilization: z.number().min(0).max(1).optional(),
  }).optional(),
  recommendations: z.array(z.object({
    type: z.enum(['budget_increase', 'channel_rebalance', 'constraint_relaxation', 'timing_adjustment']),
    description: z.string(),
    impact: z.object({
      revenueChange: z.number(),
      costChange: z.number(),
      riskChange: z.number(),
    }),
    priority: z.enum(['low', 'medium', 'high']),
  })),
  createdAt: z.date(),
  computeTime: z.number().positive(),
});

export type OptimizationResult = z.infer<typeof OptimizationResultSchema>;

// Quantum Circuit Configuration
export const QuantumCircuitConfigSchema = z.object({
  qubits: z.number().positive().max(105), // Willow chip limit
  depth: z.number().positive(),
  gateSet: z.array(z.enum(['H', 'X', 'Y', 'Z', 'CNOT', 'CZ', 'RX', 'RY', 'RZ', 'SWAP'])),
  errorCorrection: z.boolean().default(true),
  coherenceTime: z.number().positive().default(100), // microseconds
  fidelity: z.number().min(0).max(1).default(0.999),
});

export type QuantumCircuitConfig = z.infer<typeof QuantumCircuitConfigSchema>;

// Willow API Configuration
export const WillowConfigSchema = z.object({
  endpoint: z.string().url(),
  apiKey: z.string(),
  projectId: z.string(),
  region: z.enum(['us-central1', 'europe-west1', 'asia-east1']).default('us-central1'),
  timeout: z.number().positive().default(30000),
  retries: z.number().min(0).max(5).default(3),
  circuitConfig: QuantumCircuitConfigSchema,
});

export type WillowConfig = z.infer<typeof WillowConfigSchema>;

// Error Types
export class QuantumOptimizationError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'QuantumOptimizationError';
  }
}

export class WillowAPIError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: any
  ) {
    super(message);
    this.name = 'WillowAPIError';
  }
}

export class ClassicalOptimizationError extends Error {
  constructor(
    message: string,
    public algorithm: string,
    public details?: Record<string, any>
  ) {
    super(message);
    this.name = 'ClassicalOptimizationError';
  }
}

// Utility Types
export type Matrix = number[][];
export type Vector = number[];

export interface OptimizationMetrics {
  objectiveValue: number;
  constraintViolations: number;
  feasibility: boolean;
  optimality: number;
  convergenceRate: number;
}

export interface PerformanceMetrics {
  computeTime: number;
  memoryUsage: number;
  iterations: number;
  accuracy: number;
  stability: number;
}

// Export all schemas for validation
export const Schemas = {
  BudgetConstraint: BudgetConstraintSchema,
  ChannelConfig: ChannelConfigSchema,
  OptimizationObjective: OptimizationObjectiveSchema,
  BudgetAllocation: BudgetAllocationSchema,
  OptimizationRequest: OptimizationRequestSchema,
  OptimizationResult: OptimizationResultSchema,
  QuantumCircuitConfig: QuantumCircuitConfigSchema,
  WillowConfig: WillowConfigSchema,
} as const;
