"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+auth-js@2.70.0";
exports.ids = ["vendor-chunks/@supabase+auth-js@2.70.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n\nconst AuthAdminApi = _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthAdminApi);\n//# sourceMappingURL=AuthAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvQXV0aEFkbWluQXBpLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThDO0FBQzlDLHFCQUFxQix1REFBYztBQUNuQyxpRUFBZSxZQUFZLEVBQUM7QUFDNUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcQXV0aEFkbWluQXBpLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBHb1RydWVBZG1pbkFwaSBmcm9tICcuL0dvVHJ1ZUFkbWluQXBpJztcbmNvbnN0IEF1dGhBZG1pbkFwaSA9IEdvVHJ1ZUFkbWluQXBpO1xuZXhwb3J0IGRlZmF1bHQgQXV0aEFkbWluQXBpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9QXV0aEFkbWluQXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n\nconst AuthClient = _GoTrueClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AuthClient);\n//# sourceMappingURL=AuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvQXV0aENsaWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwQztBQUMxQyxtQkFBbUIscURBQVk7QUFDL0IsaUVBQWUsVUFBVSxFQUFDO0FBQzFCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlZHBhbmRhXFxEZXNrdG9wXFxNZXRhbW9ycGhpYyBmbHV4XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjcwLjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXEF1dGhDbGllbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IEdvVHJ1ZUNsaWVudCBmcm9tICcuL0dvVHJ1ZUNsaWVudCc7XG5jb25zdCBBdXRoQ2xpZW50ID0gR29UcnVlQ2xpZW50O1xuZXhwb3J0IGRlZmF1bHQgQXV0aENsaWVudDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPUF1dGhDbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueAdminApi)\n/* harmony export */ });\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\n\nclass GoTrueAdminApi {\n    constructor({ url = '', headers = {}, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(fetch);\n        this.mfa = {\n            listFactors: this._listFactors.bind(this),\n            deleteFactor: this._deleteFactor.bind(this),\n        };\n    }\n    /**\n     * Removes a logged-in session.\n     * @param jwt A valid, logged-in JWT.\n     * @param scope The logout sope.\n     */\n    async signOut(jwt, scope = _lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES[0]) {\n        if (_lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES.indexOf(scope) < 0) {\n            throw new Error(`@supabase/auth-js: Parameter scope must be one of ${_lib_types__WEBPACK_IMPORTED_MODULE_2__.SIGN_OUT_SCOPES.join(', ')}`);\n        }\n        try {\n            await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/logout?scope=${scope}`, {\n                headers: this.headers,\n                jwt,\n                noResolveJson: true,\n            });\n            return { data: null, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends an invite link to an email address.\n     * @param email The email address of the user.\n     * @param options Additional options to be included when inviting.\n     */\n    async inviteUserByEmail(email, options = {}) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/invite`, {\n                body: { email, data: options.data },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates email links and OTPs to be sent via a custom email provider.\n     * @param email The user's email.\n     * @param options.password User password. For signup only.\n     * @param options.data Optional user metadata. For signup only.\n     * @param options.redirectTo The redirect url which should be appended to the generated link\n     */\n    async generateLink(params) {\n        try {\n            const { options } = params, rest = __rest(params, [\"options\"]);\n            const body = Object.assign(Object.assign({}, rest), options);\n            if ('newEmail' in rest) {\n                // replace newEmail with new_email in request body\n                body.new_email = rest === null || rest === void 0 ? void 0 : rest.newEmail;\n                delete body['newEmail'];\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/generate_link`, {\n                body: body,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._generateLinkResponse,\n                redirectTo: options === null || options === void 0 ? void 0 : options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return {\n                    data: {\n                        properties: null,\n                        user: null,\n                    },\n                    error,\n                };\n            }\n            throw error;\n        }\n    }\n    // User Admin API\n    /**\n     * Creates a new user.\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async createUser(attributes) {\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'POST', `${this.url}/admin/users`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get a list of users.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     * @param params An object which supports `page` and `perPage` as numbers, to alter the paginated results.\n     */\n    async listUsers(params) {\n        var _a, _b, _c, _d, _e, _f, _g;\n        try {\n            const pagination = { nextPage: null, lastPage: 0, total: 0 };\n            const response = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users`, {\n                headers: this.headers,\n                noResolveJson: true,\n                query: {\n                    page: (_b = (_a = params === null || params === void 0 ? void 0 : params.page) === null || _a === void 0 ? void 0 : _a.toString()) !== null && _b !== void 0 ? _b : '',\n                    per_page: (_d = (_c = params === null || params === void 0 ? void 0 : params.perPage) === null || _c === void 0 ? void 0 : _c.toString()) !== null && _d !== void 0 ? _d : '',\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._noResolveJsonResponse,\n            });\n            if (response.error)\n                throw response.error;\n            const users = await response.json();\n            const total = (_e = response.headers.get('x-total-count')) !== null && _e !== void 0 ? _e : 0;\n            const links = (_g = (_f = response.headers.get('link')) === null || _f === void 0 ? void 0 : _f.split(',')) !== null && _g !== void 0 ? _g : [];\n            if (links.length > 0) {\n                links.forEach((link) => {\n                    const page = parseInt(link.split(';')[0].split('=')[1].substring(0, 1));\n                    const rel = JSON.parse(link.split(';')[1].split('=')[1]);\n                    pagination[`${rel}Page`] = page;\n                });\n                pagination.total = parseInt(total);\n            }\n            return { data: Object.assign(Object.assign({}, users), pagination), error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { users: [] }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Get user by id.\n     *\n     * @param uid The user's unique identifier\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async getUserById(uid) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(uid);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${uid}`, {\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates the user data.\n     *\n     * @param attributes The data you want to update.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async updateUserById(uid, attributes) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(uid);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'PUT', `${this.url}/admin/users/${uid}`, {\n                body: attributes,\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Delete a user. Requires a `service_role` key.\n     *\n     * @param id The user id you want to remove.\n     * @param shouldSoftDelete If true, then the user will be soft-deleted from the auth schema. Soft deletion allows user identification from the hashed user ID but is not reversible.\n     * Defaults to false for backward compatibility.\n     *\n     * This function should only be called on a server. Never expose your `service_role` key in the browser.\n     */\n    async deleteUser(id, shouldSoftDelete = false) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(id);\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${id}`, {\n                headers: this.headers,\n                body: {\n                    should_soft_delete: shouldSoftDelete,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_0__._userResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _listFactors(params) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.userId);\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'GET', `${this.url}/admin/users/${params.userId}/factors`, {\n                headers: this.headers,\n                xform: (factors) => {\n                    return { data: { factors }, error: null };\n                },\n            });\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _deleteFactor(params) {\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.userId);\n        (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_1__.validateUUID)(params.id);\n        try {\n            const data = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_0__._request)(this.fetch, 'DELETE', `${this.url}/admin/users/${params.userId}/factors/${params.id}`, {\n                headers: this.headers,\n            });\n            return { data, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_3__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=GoTrueAdminApi.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvR29UcnVlQWRtaW5BcGkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQSxjQUFjLFNBQUksSUFBSSxTQUFJO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGNBQWM7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNzRztBQUMzQztBQUNaO0FBQ0o7QUFDNUI7QUFDZixrQkFBa0Isc0JBQXNCLFVBQVU7QUFDbEQ7QUFDQTtBQUNBLHFCQUFxQiwwREFBWTtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix1REFBZTtBQUM5QyxZQUFZLHVEQUFlO0FBQzNCLGlGQUFpRix1REFBZSxZQUFZO0FBQzVHO0FBQ0E7QUFDQSxrQkFBa0Isb0RBQVEsd0JBQXdCLFNBQVMsZ0JBQWdCLE1BQU07QUFDakY7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EsZ0JBQWdCLHdEQUFXO0FBQzNCLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0M7QUFDL0M7QUFDQSx5QkFBeUIsb0RBQVEsd0JBQXdCLFNBQVM7QUFDbEUsd0JBQXdCLDJCQUEyQjtBQUNuRDtBQUNBO0FBQ0EsdUJBQXVCLHFEQUFhO0FBQ3BDLGFBQWE7QUFDYjtBQUNBO0FBQ0EsZ0JBQWdCLHdEQUFXO0FBQzNCLHlCQUF5QixRQUFRLFlBQVk7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsVUFBVTtBQUM5Qix1REFBdUQ7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixvREFBUSx3QkFBd0IsU0FBUztBQUNsRTtBQUNBO0FBQ0EsdUJBQXVCLDZEQUFxQjtBQUM1QztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0EsZ0JBQWdCLHdEQUFXO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLG9EQUFRLHdCQUF3QixTQUFTO0FBQ2xFO0FBQ0E7QUFDQSx1QkFBdUIscURBQWE7QUFDcEMsYUFBYTtBQUNiO0FBQ0E7QUFDQSxnQkFBZ0Isd0RBQVc7QUFDM0IseUJBQXlCLFFBQVEsWUFBWTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQyxtQ0FBbUMsb0RBQVEsdUJBQXVCLFNBQVM7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQix1QkFBdUIsOERBQXNCO0FBQzdDLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RDtBQUN2RCx3REFBd0Q7QUFDeEQsa0NBQWtDLElBQUk7QUFDdEMsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQSxxQkFBcUIsb0NBQW9DO0FBQ3pEO0FBQ0E7QUFDQSxnQkFBZ0Isd0RBQVc7QUFDM0IseUJBQXlCLFFBQVEsV0FBVztBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDBEQUFZO0FBQ3BCO0FBQ0EseUJBQXlCLG9EQUFRLHVCQUF1QixTQUFTLGVBQWUsSUFBSTtBQUNwRjtBQUNBLHVCQUF1QixxREFBYTtBQUNwQyxhQUFhO0FBQ2I7QUFDQTtBQUNBLGdCQUFnQix3REFBVztBQUMzQix5QkFBeUIsUUFBUSxZQUFZO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMERBQVk7QUFDcEI7QUFDQSx5QkFBeUIsb0RBQVEsdUJBQXVCLFNBQVMsZUFBZSxJQUFJO0FBQ3BGO0FBQ0E7QUFDQSx1QkFBdUIscURBQWE7QUFDcEMsYUFBYTtBQUNiO0FBQ0E7QUFDQSxnQkFBZ0Isd0RBQVc7QUFDM0IseUJBQXlCLFFBQVEsWUFBWTtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSwwREFBWTtBQUNwQjtBQUNBLHlCQUF5QixvREFBUSwwQkFBMEIsU0FBUyxlQUFlLEdBQUc7QUFDdEY7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLHVCQUF1QixxREFBYTtBQUNwQyxhQUFhO0FBQ2I7QUFDQTtBQUNBLGdCQUFnQix3REFBVztBQUMzQix5QkFBeUIsUUFBUSxZQUFZO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDBEQUFZO0FBQ3BCO0FBQ0Esb0JBQW9CLGNBQWMsUUFBUSxvREFBUSx1QkFBdUIsU0FBUyxlQUFlLGNBQWM7QUFDL0c7QUFDQTtBQUNBLDZCQUE2QixRQUFRLFNBQVM7QUFDOUMsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYixxQkFBcUI7QUFDckI7QUFDQTtBQUNBLGdCQUFnQix3REFBVztBQUMzQix5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsMERBQVk7QUFDcEIsUUFBUSwwREFBWTtBQUNwQjtBQUNBLCtCQUErQixvREFBUSwwQkFBMEIsU0FBUyxlQUFlLGNBQWMsV0FBVyxVQUFVO0FBQzVIO0FBQ0EsYUFBYTtBQUNiLHFCQUFxQjtBQUNyQjtBQUNBO0FBQ0EsZ0JBQWdCLHdEQUFXO0FBQzNCLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcR29UcnVlQWRtaW5BcGkuanMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF9fcmVzdCA9ICh0aGlzICYmIHRoaXMuX19yZXN0KSB8fCBmdW5jdGlvbiAocywgZSkge1xuICAgIHZhciB0ID0ge307XG4gICAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApICYmIGUuaW5kZXhPZihwKSA8IDApXG4gICAgICAgIHRbcF0gPSBzW3BdO1xuICAgIGlmIChzICE9IG51bGwgJiYgdHlwZW9mIE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMgPT09IFwiZnVuY3Rpb25cIilcbiAgICAgICAgZm9yICh2YXIgaSA9IDAsIHAgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHMpOyBpIDwgcC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgaWYgKGUuaW5kZXhPZihwW2ldKSA8IDAgJiYgT2JqZWN0LnByb3RvdHlwZS5wcm9wZXJ0eUlzRW51bWVyYWJsZS5jYWxsKHMsIHBbaV0pKVxuICAgICAgICAgICAgICAgIHRbcFtpXV0gPSBzW3BbaV1dO1xuICAgICAgICB9XG4gICAgcmV0dXJuIHQ7XG59O1xuaW1wb3J0IHsgX2dlbmVyYXRlTGlua1Jlc3BvbnNlLCBfbm9SZXNvbHZlSnNvblJlc3BvbnNlLCBfcmVxdWVzdCwgX3VzZXJSZXNwb25zZSwgfSBmcm9tICcuL2xpYi9mZXRjaCc7XG5pbXBvcnQgeyByZXNvbHZlRmV0Y2gsIHZhbGlkYXRlVVVJRCB9IGZyb20gJy4vbGliL2hlbHBlcnMnO1xuaW1wb3J0IHsgU0lHTl9PVVRfU0NPUEVTLCB9IGZyb20gJy4vbGliL3R5cGVzJztcbmltcG9ydCB7IGlzQXV0aEVycm9yIH0gZnJvbSAnLi9saWIvZXJyb3JzJztcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIEdvVHJ1ZUFkbWluQXBpIHtcbiAgICBjb25zdHJ1Y3Rvcih7IHVybCA9ICcnLCBoZWFkZXJzID0ge30sIGZldGNoLCB9KSB7XG4gICAgICAgIHRoaXMudXJsID0gdXJsO1xuICAgICAgICB0aGlzLmhlYWRlcnMgPSBoZWFkZXJzO1xuICAgICAgICB0aGlzLmZldGNoID0gcmVzb2x2ZUZldGNoKGZldGNoKTtcbiAgICAgICAgdGhpcy5tZmEgPSB7XG4gICAgICAgICAgICBsaXN0RmFjdG9yczogdGhpcy5fbGlzdEZhY3RvcnMuYmluZCh0aGlzKSxcbiAgICAgICAgICAgIGRlbGV0ZUZhY3RvcjogdGhpcy5fZGVsZXRlRmFjdG9yLmJpbmQodGhpcyksXG4gICAgICAgIH07XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJlbW92ZXMgYSBsb2dnZWQtaW4gc2Vzc2lvbi5cbiAgICAgKiBAcGFyYW0gand0IEEgdmFsaWQsIGxvZ2dlZC1pbiBKV1QuXG4gICAgICogQHBhcmFtIHNjb3BlIFRoZSBsb2dvdXQgc29wZS5cbiAgICAgKi9cbiAgICBhc3luYyBzaWduT3V0KGp3dCwgc2NvcGUgPSBTSUdOX09VVF9TQ09QRVNbMF0pIHtcbiAgICAgICAgaWYgKFNJR05fT1VUX1NDT1BFUy5pbmRleE9mKHNjb3BlKSA8IDApIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQHN1cGFiYXNlL2F1dGgtanM6IFBhcmFtZXRlciBzY29wZSBtdXN0IGJlIG9uZSBvZiAke1NJR05fT1VUX1NDT1BFUy5qb2luKCcsICcpfWApO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBhd2FpdCBfcmVxdWVzdCh0aGlzLmZldGNoLCAnUE9TVCcsIGAke3RoaXMudXJsfS9sb2dvdXQ/c2NvcGU9JHtzY29wZX1gLCB7XG4gICAgICAgICAgICAgICAgaGVhZGVyczogdGhpcy5oZWFkZXJzLFxuICAgICAgICAgICAgICAgIGp3dCxcbiAgICAgICAgICAgICAgICBub1Jlc29sdmVKc29uOiB0cnVlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4geyBkYXRhOiBudWxsLCBlcnJvcjogbnVsbCB9O1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgaWYgKGlzQXV0aEVycm9yKGVycm9yKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7IGRhdGE6IG51bGwsIGVycm9yIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBTZW5kcyBhbiBpbnZpdGUgbGluayB0byBhbiBlbWFpbCBhZGRyZXNzLlxuICAgICAqIEBwYXJhbSBlbWFpbCBUaGUgZW1haWwgYWRkcmVzcyBvZiB0aGUgdXNlci5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucyBBZGRpdGlvbmFsIG9wdGlvbnMgdG8gYmUgaW5jbHVkZWQgd2hlbiBpbnZpdGluZy5cbiAgICAgKi9cbiAgICBhc3luYyBpbnZpdGVVc2VyQnlFbWFpbChlbWFpbCwgb3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgX3JlcXVlc3QodGhpcy5mZXRjaCwgJ1BPU1QnLCBgJHt0aGlzLnVybH0vaW52aXRlYCwge1xuICAgICAgICAgICAgICAgIGJvZHk6IHsgZW1haWwsIGRhdGE6IG9wdGlvbnMuZGF0YSB9LFxuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMuaGVhZGVycyxcbiAgICAgICAgICAgICAgICByZWRpcmVjdFRvOiBvcHRpb25zLnJlZGlyZWN0VG8sXG4gICAgICAgICAgICAgICAgeGZvcm06IF91c2VyUmVzcG9uc2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChpc0F1dGhFcnJvcihlcnJvcikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4geyBkYXRhOiB7IHVzZXI6IG51bGwgfSwgZXJyb3IgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdlbmVyYXRlcyBlbWFpbCBsaW5rcyBhbmQgT1RQcyB0byBiZSBzZW50IHZpYSBhIGN1c3RvbSBlbWFpbCBwcm92aWRlci5cbiAgICAgKiBAcGFyYW0gZW1haWwgVGhlIHVzZXIncyBlbWFpbC5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5wYXNzd29yZCBVc2VyIHBhc3N3b3JkLiBGb3Igc2lnbnVwIG9ubHkuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZGF0YSBPcHRpb25hbCB1c2VyIG1ldGFkYXRhLiBGb3Igc2lnbnVwIG9ubHkuXG4gICAgICogQHBhcmFtIG9wdGlvbnMucmVkaXJlY3RUbyBUaGUgcmVkaXJlY3QgdXJsIHdoaWNoIHNob3VsZCBiZSBhcHBlbmRlZCB0byB0aGUgZ2VuZXJhdGVkIGxpbmtcbiAgICAgKi9cbiAgICBhc3luYyBnZW5lcmF0ZUxpbmsocGFyYW1zKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCB7IG9wdGlvbnMgfSA9IHBhcmFtcywgcmVzdCA9IF9fcmVzdChwYXJhbXMsIFtcIm9wdGlvbnNcIl0pO1xuICAgICAgICAgICAgY29uc3QgYm9keSA9IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcmVzdCksIG9wdGlvbnMpO1xuICAgICAgICAgICAgaWYgKCduZXdFbWFpbCcgaW4gcmVzdCkge1xuICAgICAgICAgICAgICAgIC8vIHJlcGxhY2UgbmV3RW1haWwgd2l0aCBuZXdfZW1haWwgaW4gcmVxdWVzdCBib2R5XG4gICAgICAgICAgICAgICAgYm9keS5uZXdfZW1haWwgPSByZXN0ID09PSBudWxsIHx8IHJlc3QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHJlc3QubmV3RW1haWw7XG4gICAgICAgICAgICAgICAgZGVsZXRlIGJvZHlbJ25ld0VtYWlsJ107XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgX3JlcXVlc3QodGhpcy5mZXRjaCwgJ1BPU1QnLCBgJHt0aGlzLnVybH0vYWRtaW4vZ2VuZXJhdGVfbGlua2AsIHtcbiAgICAgICAgICAgICAgICBib2R5OiBib2R5LFxuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMuaGVhZGVycyxcbiAgICAgICAgICAgICAgICB4Zm9ybTogX2dlbmVyYXRlTGlua1Jlc3BvbnNlLFxuICAgICAgICAgICAgICAgIHJlZGlyZWN0VG86IG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5yZWRpcmVjdFRvLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBpZiAoaXNBdXRoRXJyb3IoZXJyb3IpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgICAgICAgICAgcHJvcGVydGllczogbnVsbCxcbiAgICAgICAgICAgICAgICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvLyBVc2VyIEFkbWluIEFQSVxuICAgIC8qKlxuICAgICAqIENyZWF0ZXMgYSBuZXcgdXNlci5cbiAgICAgKiBUaGlzIGZ1bmN0aW9uIHNob3VsZCBvbmx5IGJlIGNhbGxlZCBvbiBhIHNlcnZlci4gTmV2ZXIgZXhwb3NlIHlvdXIgYHNlcnZpY2Vfcm9sZWAga2V5IGluIHRoZSBicm93c2VyLlxuICAgICAqL1xuICAgIGFzeW5jIGNyZWF0ZVVzZXIoYXR0cmlidXRlcykge1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IF9yZXF1ZXN0KHRoaXMuZmV0Y2gsICdQT1NUJywgYCR7dGhpcy51cmx9L2FkbWluL3VzZXJzYCwge1xuICAgICAgICAgICAgICAgIGJvZHk6IGF0dHJpYnV0ZXMsXG4gICAgICAgICAgICAgICAgaGVhZGVyczogdGhpcy5oZWFkZXJzLFxuICAgICAgICAgICAgICAgIHhmb3JtOiBfdXNlclJlc3BvbnNlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBpZiAoaXNBdXRoRXJyb3IoZXJyb3IpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHsgZGF0YTogeyB1c2VyOiBudWxsIH0sIGVycm9yIH07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgYSBsaXN0IG9mIHVzZXJzLlxuICAgICAqXG4gICAgICogVGhpcyBmdW5jdGlvbiBzaG91bGQgb25seSBiZSBjYWxsZWQgb24gYSBzZXJ2ZXIuIE5ldmVyIGV4cG9zZSB5b3VyIGBzZXJ2aWNlX3JvbGVgIGtleSBpbiB0aGUgYnJvd3Nlci5cbiAgICAgKiBAcGFyYW0gcGFyYW1zIEFuIG9iamVjdCB3aGljaCBzdXBwb3J0cyBgcGFnZWAgYW5kIGBwZXJQYWdlYCBhcyBudW1iZXJzLCB0byBhbHRlciB0aGUgcGFnaW5hdGVkIHJlc3VsdHMuXG4gICAgICovXG4gICAgYXN5bmMgbGlzdFVzZXJzKHBhcmFtcykge1xuICAgICAgICB2YXIgX2EsIF9iLCBfYywgX2QsIF9lLCBfZiwgX2c7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBwYWdpbmF0aW9uID0geyBuZXh0UGFnZTogbnVsbCwgbGFzdFBhZ2U6IDAsIHRvdGFsOiAwIH07XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IF9yZXF1ZXN0KHRoaXMuZmV0Y2gsICdHRVQnLCBgJHt0aGlzLnVybH0vYWRtaW4vdXNlcnNgLCB7XG4gICAgICAgICAgICAgICAgaGVhZGVyczogdGhpcy5oZWFkZXJzLFxuICAgICAgICAgICAgICAgIG5vUmVzb2x2ZUpzb246IHRydWUsXG4gICAgICAgICAgICAgICAgcXVlcnk6IHtcbiAgICAgICAgICAgICAgICAgICAgcGFnZTogKF9iID0gKF9hID0gcGFyYW1zID09PSBudWxsIHx8IHBhcmFtcyA9PT0gdm9pZCAwID8gdm9pZCAwIDogcGFyYW1zLnBhZ2UpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS50b1N0cmluZygpKSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAnJyxcbiAgICAgICAgICAgICAgICAgICAgcGVyX3BhZ2U6IChfZCA9IChfYyA9IHBhcmFtcyA9PT0gbnVsbCB8fCBwYXJhbXMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHBhcmFtcy5wZXJQYWdlKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MudG9TdHJpbmcoKSkgIT09IG51bGwgJiYgX2QgIT09IHZvaWQgMCA/IF9kIDogJycsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB4Zm9ybTogX25vUmVzb2x2ZUpzb25SZXNwb25zZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmVycm9yKVxuICAgICAgICAgICAgICAgIHRocm93IHJlc3BvbnNlLmVycm9yO1xuICAgICAgICAgICAgY29uc3QgdXNlcnMgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgICBjb25zdCB0b3RhbCA9IChfZSA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCd4LXRvdGFsLWNvdW50JykpICE9PSBudWxsICYmIF9lICE9PSB2b2lkIDAgPyBfZSA6IDA7XG4gICAgICAgICAgICBjb25zdCBsaW5rcyA9IChfZyA9IChfZiA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KCdsaW5rJykpID09PSBudWxsIHx8IF9mID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZi5zcGxpdCgnLCcpKSAhPT0gbnVsbCAmJiBfZyAhPT0gdm9pZCAwID8gX2cgOiBbXTtcbiAgICAgICAgICAgIGlmIChsaW5rcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgbGlua3MuZm9yRWFjaCgobGluaykgPT4ge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwYWdlID0gcGFyc2VJbnQobGluay5zcGxpdCgnOycpWzBdLnNwbGl0KCc9JylbMV0uc3Vic3RyaW5nKDAsIDEpKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVsID0gSlNPTi5wYXJzZShsaW5rLnNwbGl0KCc7JylbMV0uc3BsaXQoJz0nKVsxXSk7XG4gICAgICAgICAgICAgICAgICAgIHBhZ2luYXRpb25bYCR7cmVsfVBhZ2VgXSA9IHBhZ2U7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcGFnaW5hdGlvbi50b3RhbCA9IHBhcnNlSW50KHRvdGFsKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiB7IGRhdGE6IE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgdXNlcnMpLCBwYWdpbmF0aW9uKSwgZXJyb3I6IG51bGwgfTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChpc0F1dGhFcnJvcihlcnJvcikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4geyBkYXRhOiB7IHVzZXJzOiBbXSB9LCBlcnJvciB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHVzZXIgYnkgaWQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gdWlkIFRoZSB1c2VyJ3MgdW5pcXVlIGlkZW50aWZpZXJcbiAgICAgKlxuICAgICAqIFRoaXMgZnVuY3Rpb24gc2hvdWxkIG9ubHkgYmUgY2FsbGVkIG9uIGEgc2VydmVyLiBOZXZlciBleHBvc2UgeW91ciBgc2VydmljZV9yb2xlYCBrZXkgaW4gdGhlIGJyb3dzZXIuXG4gICAgICovXG4gICAgYXN5bmMgZ2V0VXNlckJ5SWQodWlkKSB7XG4gICAgICAgIHZhbGlkYXRlVVVJRCh1aWQpO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIGF3YWl0IF9yZXF1ZXN0KHRoaXMuZmV0Y2gsICdHRVQnLCBgJHt0aGlzLnVybH0vYWRtaW4vdXNlcnMvJHt1aWR9YCwge1xuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMuaGVhZGVycyxcbiAgICAgICAgICAgICAgICB4Zm9ybTogX3VzZXJSZXNwb25zZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgaWYgKGlzQXV0aEVycm9yKGVycm9yKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7IGRhdGE6IHsgdXNlcjogbnVsbCB9LCBlcnJvciB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogVXBkYXRlcyB0aGUgdXNlciBkYXRhLlxuICAgICAqXG4gICAgICogQHBhcmFtIGF0dHJpYnV0ZXMgVGhlIGRhdGEgeW91IHdhbnQgdG8gdXBkYXRlLlxuICAgICAqXG4gICAgICogVGhpcyBmdW5jdGlvbiBzaG91bGQgb25seSBiZSBjYWxsZWQgb24gYSBzZXJ2ZXIuIE5ldmVyIGV4cG9zZSB5b3VyIGBzZXJ2aWNlX3JvbGVgIGtleSBpbiB0aGUgYnJvd3Nlci5cbiAgICAgKi9cbiAgICBhc3luYyB1cGRhdGVVc2VyQnlJZCh1aWQsIGF0dHJpYnV0ZXMpIHtcbiAgICAgICAgdmFsaWRhdGVVVUlEKHVpZCk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgX3JlcXVlc3QodGhpcy5mZXRjaCwgJ1BVVCcsIGAke3RoaXMudXJsfS9hZG1pbi91c2Vycy8ke3VpZH1gLCB7XG4gICAgICAgICAgICAgICAgYm9keTogYXR0cmlidXRlcyxcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB0aGlzLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgeGZvcm06IF91c2VyUmVzcG9uc2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChpc0F1dGhFcnJvcihlcnJvcikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4geyBkYXRhOiB7IHVzZXI6IG51bGwgfSwgZXJyb3IgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBhIHVzZXIuIFJlcXVpcmVzIGEgYHNlcnZpY2Vfcm9sZWAga2V5LlxuICAgICAqXG4gICAgICogQHBhcmFtIGlkIFRoZSB1c2VyIGlkIHlvdSB3YW50IHRvIHJlbW92ZS5cbiAgICAgKiBAcGFyYW0gc2hvdWxkU29mdERlbGV0ZSBJZiB0cnVlLCB0aGVuIHRoZSB1c2VyIHdpbGwgYmUgc29mdC1kZWxldGVkIGZyb20gdGhlIGF1dGggc2NoZW1hLiBTb2Z0IGRlbGV0aW9uIGFsbG93cyB1c2VyIGlkZW50aWZpY2F0aW9uIGZyb20gdGhlIGhhc2hlZCB1c2VyIElEIGJ1dCBpcyBub3QgcmV2ZXJzaWJsZS5cbiAgICAgKiBEZWZhdWx0cyB0byBmYWxzZSBmb3IgYmFja3dhcmQgY29tcGF0aWJpbGl0eS5cbiAgICAgKlxuICAgICAqIFRoaXMgZnVuY3Rpb24gc2hvdWxkIG9ubHkgYmUgY2FsbGVkIG9uIGEgc2VydmVyLiBOZXZlciBleHBvc2UgeW91ciBgc2VydmljZV9yb2xlYCBrZXkgaW4gdGhlIGJyb3dzZXIuXG4gICAgICovXG4gICAgYXN5bmMgZGVsZXRlVXNlcihpZCwgc2hvdWxkU29mdERlbGV0ZSA9IGZhbHNlKSB7XG4gICAgICAgIHZhbGlkYXRlVVVJRChpZCk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgX3JlcXVlc3QodGhpcy5mZXRjaCwgJ0RFTEVURScsIGAke3RoaXMudXJsfS9hZG1pbi91c2Vycy8ke2lkfWAsIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB0aGlzLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgYm9keToge1xuICAgICAgICAgICAgICAgICAgICBzaG91bGRfc29mdF9kZWxldGU6IHNob3VsZFNvZnREZWxldGUsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICB4Zm9ybTogX3VzZXJSZXNwb25zZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgaWYgKGlzQXV0aEVycm9yKGVycm9yKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiB7IGRhdGE6IHsgdXNlcjogbnVsbCB9LCBlcnJvciB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9XG4gICAgYXN5bmMgX2xpc3RGYWN0b3JzKHBhcmFtcykge1xuICAgICAgICB2YWxpZGF0ZVVVSUQocGFyYW1zLnVzZXJJZCk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBfcmVxdWVzdCh0aGlzLmZldGNoLCAnR0VUJywgYCR7dGhpcy51cmx9L2FkbWluL3VzZXJzLyR7cGFyYW1zLnVzZXJJZH0vZmFjdG9yc2AsIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB0aGlzLmhlYWRlcnMsXG4gICAgICAgICAgICAgICAgeGZvcm06IChmYWN0b3JzKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiB7IGRhdGE6IHsgZmFjdG9ycyB9LCBlcnJvcjogbnVsbCB9O1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybiB7IGRhdGEsIGVycm9yIH07XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBpZiAoaXNBdXRoRXJyb3IoZXJyb3IpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3IgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuICAgIGFzeW5jIF9kZWxldGVGYWN0b3IocGFyYW1zKSB7XG4gICAgICAgIHZhbGlkYXRlVVVJRChwYXJhbXMudXNlcklkKTtcbiAgICAgICAgdmFsaWRhdGVVVUlEKHBhcmFtcy5pZCk7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgX3JlcXVlc3QodGhpcy5mZXRjaCwgJ0RFTEVURScsIGAke3RoaXMudXJsfS9hZG1pbi91c2Vycy8ke3BhcmFtcy51c2VySWR9L2ZhY3RvcnMvJHtwYXJhbXMuaWR9YCwge1xuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHRoaXMuaGVhZGVycyxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIHsgZGF0YSwgZXJyb3I6IG51bGwgfTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGlmIChpc0F1dGhFcnJvcihlcnJvcikpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4geyBkYXRhOiBudWxsLCBlcnJvciB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Hb1RydWVBZG1pbkFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoTrueClient)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/local-storage */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\");\n/* harmony import */ var _lib_polyfills__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/polyfills */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\");\n/* harmony import */ var _lib_version__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/version */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n/* harmony import */ var _lib_base64url__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/base64url */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\n\n\n\n\n\n\n\n(0,_lib_polyfills__WEBPACK_IMPORTED_MODULE_6__.polyfillGlobalThis)(); // Make \"globalThis\" available\nconst DEFAULT_OPTIONS = {\n    url: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.GOTRUE_URL,\n    storageKey: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.STORAGE_KEY,\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    headers: _lib_constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_HEADERS,\n    flowType: 'implicit',\n    debug: false,\n    hasCustomAuthorizationHeader: false,\n};\nasync function lockNoOp(name, acquireTimeout, fn) {\n    return await fn();\n}\nclass GoTrueClient {\n    /**\n     * Create a new client for use in the browser.\n     */\n    constructor(options) {\n        var _a, _b;\n        this.memoryStorage = null;\n        this.stateChangeEmitters = new Map();\n        this.autoRefreshTicker = null;\n        this.visibilityChangedCallback = null;\n        this.refreshingDeferred = null;\n        /**\n         * Keeps track of the async client initialization.\n         * When null or not yet resolved the auth state is `unknown`\n         * Once resolved the the auth state is known and it's save to call any further client methods.\n         * Keep extra care to never reject or throw uncaught errors\n         */\n        this.initializePromise = null;\n        this.detectSessionInUrl = true;\n        this.hasCustomAuthorizationHeader = false;\n        this.suppressGetSessionWarning = false;\n        this.lockAcquired = false;\n        this.pendingInLock = [];\n        /**\n         * Used to broadcast state change events to other tabs listening.\n         */\n        this.broadcastChannel = null;\n        this.logger = console.log;\n        this.instanceID = GoTrueClient.nextInstanceID;\n        GoTrueClient.nextInstanceID += 1;\n        if (this.instanceID > 0 && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n            console.warn('Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.');\n        }\n        const settings = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n        this.logDebugMessages = !!settings.debug;\n        if (typeof settings.debug === 'function') {\n            this.logger = settings.debug;\n        }\n        this.persistSession = settings.persistSession;\n        this.storageKey = settings.storageKey;\n        this.autoRefreshToken = settings.autoRefreshToken;\n        this.admin = new _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            url: settings.url,\n            headers: settings.headers,\n            fetch: settings.fetch,\n        });\n        this.url = settings.url;\n        this.headers = settings.headers;\n        this.fetch = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.resolveFetch)(settings.fetch);\n        this.lock = settings.lock || lockNoOp;\n        this.detectSessionInUrl = settings.detectSessionInUrl;\n        this.flowType = settings.flowType;\n        this.hasCustomAuthorizationHeader = settings.hasCustomAuthorizationHeader;\n        if (settings.lock) {\n            this.lock = settings.lock;\n        }\n        else if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && ((_a = globalThis === null || globalThis === void 0 ? void 0 : globalThis.navigator) === null || _a === void 0 ? void 0 : _a.locks)) {\n            this.lock = _lib_locks__WEBPACK_IMPORTED_MODULE_8__.navigatorLock;\n        }\n        else {\n            this.lock = lockNoOp;\n        }\n        this.jwks = { keys: [] };\n        this.jwks_cached_at = Number.MIN_SAFE_INTEGER;\n        this.mfa = {\n            verify: this._verify.bind(this),\n            enroll: this._enroll.bind(this),\n            unenroll: this._unenroll.bind(this),\n            challenge: this._challenge.bind(this),\n            listFactors: this._listFactors.bind(this),\n            challengeAndVerify: this._challengeAndVerify.bind(this),\n            getAuthenticatorAssuranceLevel: this._getAuthenticatorAssuranceLevel.bind(this),\n        };\n        if (this.persistSession) {\n            if (settings.storage) {\n                this.storage = settings.storage;\n            }\n            else {\n                if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.supportsLocalStorage)()) {\n                    this.storage = _lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.localStorageAdapter;\n                }\n                else {\n                    this.memoryStorage = {};\n                    this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n                }\n            }\n        }\n        else {\n            this.memoryStorage = {};\n            this.storage = (0,_lib_local_storage__WEBPACK_IMPORTED_MODULE_5__.memoryLocalStorageAdapter)(this.memoryStorage);\n        }\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && globalThis.BroadcastChannel && this.persistSession && this.storageKey) {\n            try {\n                this.broadcastChannel = new globalThis.BroadcastChannel(this.storageKey);\n            }\n            catch (e) {\n                console.error('Failed to create a new BroadcastChannel, multi-tab state changes will not be available', e);\n            }\n            (_b = this.broadcastChannel) === null || _b === void 0 ? void 0 : _b.addEventListener('message', async (event) => {\n                this._debug('received broadcast notification from other tab or client', event);\n                await this._notifyAllSubscribers(event.data.event, event.data.session, false); // broadcast = false so we don't get an endless loop of messages\n            });\n        }\n        this.initialize();\n    }\n    _debug(...args) {\n        if (this.logDebugMessages) {\n            this.logger(`GoTrueClient@${this.instanceID} (${_lib_version__WEBPACK_IMPORTED_MODULE_7__.version}) ${new Date().toISOString()}`, ...args);\n        }\n        return this;\n    }\n    /**\n     * Initializes the client session either from the url or from storage.\n     * This method is automatically called when instantiating the client, but should also be called\n     * manually when checking for an error from an auth redirect (oauth, magiclink, password recovery, etc).\n     */\n    async initialize() {\n        if (this.initializePromise) {\n            return await this.initializePromise;\n        }\n        this.initializePromise = (async () => {\n            return await this._acquireLock(-1, async () => {\n                return await this._initialize();\n            });\n        })();\n        return await this.initializePromise;\n    }\n    /**\n     * IMPORTANT:\n     * 1. Never throw in this method, as it is called from the constructor\n     * 2. Never return a session from this method as it would be cached over\n     *    the whole lifetime of the client\n     */\n    async _initialize() {\n        var _a;\n        try {\n            const params = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.parseParametersFromURL)(window.location.href);\n            let callbackUrlType = 'none';\n            if (this._isImplicitGrantCallback(params)) {\n                callbackUrlType = 'implicit';\n            }\n            else if (await this._isPKCECallback(params)) {\n                callbackUrlType = 'pkce';\n            }\n            /**\n             * Attempt to get the session from the URL only if these conditions are fulfilled\n             *\n             * Note: If the URL isn't one of the callback url types (implicit or pkce),\n             * then there could be an existing session so we don't want to prematurely remove it\n             */\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && this.detectSessionInUrl && callbackUrlType !== 'none') {\n                const { data, error } = await this._getSessionFromURL(params, callbackUrlType);\n                if (error) {\n                    this._debug('#_initialize()', 'error detecting session from URL', error);\n                    if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthImplicitGrantRedirectError)(error)) {\n                        const errorCode = (_a = error.details) === null || _a === void 0 ? void 0 : _a.code;\n                        if (errorCode === 'identity_already_exists' ||\n                            errorCode === 'identity_not_found' ||\n                            errorCode === 'single_identity_not_deletable') {\n                            return { error };\n                        }\n                    }\n                    // failed login attempt via url,\n                    // remove old session as in verifyOtp, signUp and signInWith*\n                    await this._removeSession();\n                    return { error };\n                }\n                const { session, redirectType } = data;\n                this._debug('#_initialize()', 'detected session in URL', session, 'redirect type', redirectType);\n                await this._saveSession(session);\n                setTimeout(async () => {\n                    if (redirectType === 'recovery') {\n                        await this._notifyAllSubscribers('PASSWORD_RECOVERY', session);\n                    }\n                    else {\n                        await this._notifyAllSubscribers('SIGNED_IN', session);\n                    }\n                }, 0);\n                return { error: null };\n            }\n            // no login attempt via callback url try to recover session from storage\n            await this._recoverAndRefresh();\n            return { error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { error };\n            }\n            return {\n                error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError('Unexpected error during initialization', error),\n            };\n        }\n        finally {\n            await this._handleVisibilityChange();\n            this._debug('#_initialize()', 'end');\n        }\n    }\n    /**\n     * Creates a new anonymous user.\n     *\n     * @returns A session where the is_anonymous claim in the access token JWT set to true\n     */\n    async signInAnonymously(credentials) {\n        var _a, _b, _c;\n        try {\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                headers: this.headers,\n                body: {\n                    data: (_b = (_a = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _a === void 0 ? void 0 : _a.data) !== null && _b !== void 0 ? _b : {},\n                    gotrue_meta_security: { captcha_token: (_c = credentials === null || credentials === void 0 ? void 0 : credentials.options) === null || _c === void 0 ? void 0 : _c.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Creates a new user.\n     *\n     * Be aware that if a user account exists in the system you may get back an\n     * error message that attempts to hide this information from the user.\n     * This method has support for PKCE via email signups. The PKCE flow cannot be used when autoconfirm is enabled.\n     *\n     * @returns A logged-in session if the server has \"autoconfirm\" ON\n     * @returns A user if the server has \"autoconfirm\" OFF\n     */\n    async signUp(credentials) {\n        var _a, _b, _c;\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: {\n                        email,\n                        password,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/signup`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        data: (_b = options === null || options === void 0 ? void 0 : options.data) !== null && _b !== void 0 ? _b : {},\n                        channel: (_c = options === null || options === void 0 ? void 0 : options.channel) !== null && _c !== void 0 ? _c : 'sms',\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error || !data) {\n                return { data: { user: null, session: null }, error: error };\n            }\n            const session = data.session;\n            const user = data.user;\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user with an email and password or phone and password.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or that the\n     * email/phone and password combination is wrong or that the account can only\n     * be accessed via social login.\n     */\n    async signInWithPassword(credentials) {\n        try {\n            let res;\n            if ('email' in credentials) {\n                const { email, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else if ('phone' in credentials) {\n                const { phone, password, options } = credentials;\n                res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=password`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        password,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponsePassword,\n                });\n            }\n            else {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a password');\n            }\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return { data: { user: null, session: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError() };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return {\n                data: Object.assign({ user: data.user, session: data.session }, (data.weak_password ? { weakPassword: data.weak_password } : null)),\n                error,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in an existing user via a third-party provider.\n     * This method supports the PKCE flow.\n     */\n    async signInWithOAuth(credentials) {\n        var _a, _b, _c, _d;\n        return await this._handleProviderSignIn(credentials.provider, {\n            redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n            scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n            queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n            skipBrowserRedirect: (_d = credentials.options) === null || _d === void 0 ? void 0 : _d.skipBrowserRedirect,\n        });\n    }\n    /**\n     * Log in an existing user by exchanging an Auth Code issued during the PKCE flow.\n     */\n    async exchangeCodeForSession(authCode) {\n        await this.initializePromise;\n        return this._acquireLock(-1, async () => {\n            return this._exchangeCodeForSession(authCode);\n        });\n    }\n    /**\n     * Signs in a user by verifying a message signed by the user's private key.\n     * Only Solana supported at this time, using the Sign in with Solana standard.\n     */\n    async signInWithWeb3(credentials) {\n        const { chain } = credentials;\n        if (chain === 'solana') {\n            return await this.signInWithSolana(credentials);\n        }\n        throw new Error(`@supabase/auth-js: Unsupported chain \"${chain}\"`);\n    }\n    async signInWithSolana(credentials) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n        let message;\n        let signature;\n        if ('message' in credentials) {\n            message = credentials.message;\n            signature = credentials.signature;\n        }\n        else {\n            const { chain, wallet, statement, options } = credentials;\n            let resolvedWallet;\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)()) {\n                if (typeof wallet !== 'object' || !(options === null || options === void 0 ? void 0 : options.url)) {\n                    throw new Error('@supabase/auth-js: Both wallet and url must be specified in non-browser environments.');\n                }\n                resolvedWallet = wallet;\n            }\n            else if (typeof wallet === 'object') {\n                resolvedWallet = wallet;\n            }\n            else {\n                const windowAny = window;\n                if ('solana' in windowAny &&\n                    typeof windowAny.solana === 'object' &&\n                    (('signIn' in windowAny.solana && typeof windowAny.solana.signIn === 'function') ||\n                        ('signMessage' in windowAny.solana &&\n                            typeof windowAny.solana.signMessage === 'function'))) {\n                    resolvedWallet = windowAny.solana;\n                }\n                else {\n                    throw new Error(`@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.`);\n                }\n            }\n            const url = new URL((_a = options === null || options === void 0 ? void 0 : options.url) !== null && _a !== void 0 ? _a : window.location.href);\n            if ('signIn' in resolvedWallet && resolvedWallet.signIn) {\n                const output = await resolvedWallet.signIn(Object.assign(Object.assign(Object.assign({ issuedAt: new Date().toISOString() }, options === null || options === void 0 ? void 0 : options.signInWithSolana), { \n                    // non-overridable properties\n                    version: '1', domain: url.host, uri: url.href }), (statement ? { statement } : null)));\n                let outputToProcess;\n                if (Array.isArray(output) && output[0] && typeof output[0] === 'object') {\n                    outputToProcess = output[0];\n                }\n                else if (output &&\n                    typeof output === 'object' &&\n                    'signedMessage' in output &&\n                    'signature' in output) {\n                    outputToProcess = output;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() returned unrecognized value');\n                }\n                if ('signedMessage' in outputToProcess &&\n                    'signature' in outputToProcess &&\n                    (typeof outputToProcess.signedMessage === 'string' ||\n                        outputToProcess.signedMessage instanceof Uint8Array) &&\n                    outputToProcess.signature instanceof Uint8Array) {\n                    message =\n                        typeof outputToProcess.signedMessage === 'string'\n                            ? outputToProcess.signedMessage\n                            : new TextDecoder().decode(outputToProcess.signedMessage);\n                    signature = outputToProcess.signature;\n                }\n                else {\n                    throw new Error('@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields');\n                }\n            }\n            else {\n                if (!('signMessage' in resolvedWallet) ||\n                    typeof resolvedWallet.signMessage !== 'function' ||\n                    !('publicKey' in resolvedWallet) ||\n                    typeof resolvedWallet !== 'object' ||\n                    !resolvedWallet.publicKey ||\n                    !('toBase58' in resolvedWallet.publicKey) ||\n                    typeof resolvedWallet.publicKey.toBase58 !== 'function') {\n                    throw new Error('@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API');\n                }\n                message = [\n                    `${url.host} wants you to sign in with your Solana account:`,\n                    resolvedWallet.publicKey.toBase58(),\n                    ...(statement ? ['', statement, ''] : ['']),\n                    'Version: 1',\n                    `URI: ${url.href}`,\n                    `Issued At: ${(_c = (_b = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _b === void 0 ? void 0 : _b.issuedAt) !== null && _c !== void 0 ? _c : new Date().toISOString()}`,\n                    ...(((_d = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _d === void 0 ? void 0 : _d.notBefore)\n                        ? [`Not Before: ${options.signInWithSolana.notBefore}`]\n                        : []),\n                    ...(((_e = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _e === void 0 ? void 0 : _e.expirationTime)\n                        ? [`Expiration Time: ${options.signInWithSolana.expirationTime}`]\n                        : []),\n                    ...(((_f = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _f === void 0 ? void 0 : _f.chainId)\n                        ? [`Chain ID: ${options.signInWithSolana.chainId}`]\n                        : []),\n                    ...(((_g = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _g === void 0 ? void 0 : _g.nonce) ? [`Nonce: ${options.signInWithSolana.nonce}`] : []),\n                    ...(((_h = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _h === void 0 ? void 0 : _h.requestId)\n                        ? [`Request ID: ${options.signInWithSolana.requestId}`]\n                        : []),\n                    ...(((_k = (_j = options === null || options === void 0 ? void 0 : options.signInWithSolana) === null || _j === void 0 ? void 0 : _j.resources) === null || _k === void 0 ? void 0 : _k.length)\n                        ? [\n                            'Resources',\n                            ...options.signInWithSolana.resources.map((resource) => `- ${resource}`),\n                        ]\n                        : []),\n                ].join('\\n');\n                const maybeSignature = await resolvedWallet.signMessage(new TextEncoder().encode(message), 'utf8');\n                if (!maybeSignature || !(maybeSignature instanceof Uint8Array)) {\n                    throw new Error('@supabase/auth-js: Wallet signMessage() API returned an recognized value');\n                }\n                signature = maybeSignature;\n            }\n        }\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=web3`, {\n                headers: this.headers,\n                body: Object.assign({ chain: 'solana', message, signature: (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.bytesToBase64URL)(signature) }, (((_l = credentials.options) === null || _l === void 0 ? void 0 : _l.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: (_m = credentials.options) === null || _m === void 0 ? void 0 : _m.captchaToken } }\n                    : null)),\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign({}, data), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    async _exchangeCodeForSession(authCode) {\n        const storageItem = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        const [codeVerifier, redirectType] = (storageItem !== null && storageItem !== void 0 ? storageItem : '').split('/');\n        try {\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=pkce`, {\n                headers: this.headers,\n                body: {\n                    auth_code: authCode,\n                    code_verifier: codeVerifier,\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            if (error) {\n                throw error;\n            }\n            if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null, redirectType: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data: Object.assign(Object.assign({}, data), { redirectType: redirectType !== null && redirectType !== void 0 ? redirectType : null }), error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Allows signing in with an OIDC ID token. The authentication provider used\n     * should be enabled and configured.\n     */\n    async signInWithIdToken(credentials) {\n        try {\n            const { options, provider, token, access_token, nonce } = credentials;\n            const res = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=id_token`, {\n                headers: this.headers,\n                body: {\n                    provider,\n                    id_token: token,\n                    access_token,\n                    nonce,\n                    gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                },\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            const { data, error } = res;\n            if (error) {\n                return { data: { user: null, session: null }, error };\n            }\n            else if (!data || !data.session || !data.user) {\n                return {\n                    data: { user: null, session: null },\n                    error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidTokenResponseError(),\n                };\n            }\n            if (data.session) {\n                await this._saveSession(data.session);\n                await this._notifyAllSubscribers('SIGNED_IN', data.session);\n            }\n            return { data, error };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user using magiclink or a one-time password (OTP).\n     *\n     * If the `{{ .ConfirmationURL }}` variable is specified in the email template, a magiclink will be sent.\n     * If the `{{ .Token }}` variable is specified in the email template, an OTP will be sent.\n     * If you're using phone sign-ins, only an OTP will be sent. You won't be able to send a magiclink for phone sign-ins.\n     *\n     * Be aware that you may get back an error message that will not distinguish\n     * between the cases where the account does not exist or, that the account\n     * can only be accessed via social login.\n     *\n     * Do note that you will need to configure a Whatsapp sender on Twilio\n     * if you are using phone sign in with the 'whatsapp' channel. The whatsapp\n     * channel is not supported on other providers\n     * at this time.\n     * This method supports PKCE when an email is passed.\n     */\n    async signInWithOtp(credentials) {\n        var _a, _b, _c, _d, _e;\n        try {\n            if ('email' in credentials) {\n                const { email, options } = credentials;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce') {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        data: (_a = options === null || options === void 0 ? void 0 : options.data) !== null && _a !== void 0 ? _a : {},\n                        create_user: (_b = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _b !== void 0 ? _b : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        code_challenge: codeChallenge,\n                        code_challenge_method: codeChallengeMethod,\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            if ('phone' in credentials) {\n                const { phone, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/otp`, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        data: (_c = options === null || options === void 0 ? void 0 : options.data) !== null && _c !== void 0 ? _c : {},\n                        create_user: (_d = options === null || options === void 0 ? void 0 : options.shouldCreateUser) !== null && _d !== void 0 ? _d : true,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                        channel: (_e = options === null || options === void 0 ? void 0 : options.channel) !== null && _e !== void 0 ? _e : 'sms',\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number.');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Log in a user given a User supplied OTP or TokenHash received through mobile or email.\n     */\n    async verifyOtp(params) {\n        var _a, _b;\n        try {\n            let redirectTo = undefined;\n            let captchaToken = undefined;\n            if ('options' in params) {\n                redirectTo = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo;\n                captchaToken = (_b = params.options) === null || _b === void 0 ? void 0 : _b.captchaToken;\n            }\n            const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/verify`, {\n                headers: this.headers,\n                body: Object.assign(Object.assign({}, params), { gotrue_meta_security: { captcha_token: captchaToken } }),\n                redirectTo,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n            });\n            if (error) {\n                throw error;\n            }\n            if (!data) {\n                throw new Error('An error occurred on token verification.');\n            }\n            const session = data.session;\n            const user = data.user;\n            if (session === null || session === void 0 ? void 0 : session.access_token) {\n                await this._saveSession(session);\n                await this._notifyAllSubscribers(params.type == 'recovery' ? 'PASSWORD_RECOVERY' : 'SIGNED_IN', session);\n            }\n            return { data: { user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Attempts a single-sign on using an enterprise Identity Provider. A\n     * successful SSO attempt will redirect the current page to the identity\n     * provider authorization page. The redirect URL is implementation and SSO\n     * protocol specific.\n     *\n     * You can use it by providing a SSO domain. Typically you can extract this\n     * domain by asking users for their email address. If this domain is\n     * registered on the Auth instance the redirect will use that organization's\n     * currently active SSO Identity Provider for the login.\n     *\n     * If you have built an organization-specific login page, you can use the\n     * organization's SSO Identity Provider UUID directly instead.\n     */\n    async signInWithSSO(params) {\n        var _a, _b, _c;\n        try {\n            let codeChallenge = null;\n            let codeChallengeMethod = null;\n            if (this.flowType === 'pkce') {\n                ;\n                [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            }\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/sso`, {\n                body: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, ('providerId' in params ? { provider_id: params.providerId } : null)), ('domain' in params ? { domain: params.domain } : null)), { redirect_to: (_b = (_a = params.options) === null || _a === void 0 ? void 0 : _a.redirectTo) !== null && _b !== void 0 ? _b : undefined }), (((_c = params === null || params === void 0 ? void 0 : params.options) === null || _c === void 0 ? void 0 : _c.captchaToken)\n                    ? { gotrue_meta_security: { captcha_token: params.options.captchaToken } }\n                    : null)), { skip_http_redirect: true, code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                headers: this.headers,\n                xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._ssoResponse,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sends a reauthentication OTP to the user's email or phone number.\n     * Requires the user to be signed-in.\n     */\n    async reauthenticate() {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._reauthenticate();\n        });\n    }\n    async _reauthenticate() {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError)\n                    throw sessionError;\n                if (!session)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/reauthenticate`, {\n                    headers: this.headers,\n                    jwt: session.access_token,\n                });\n                return { data: { user: null, session: null }, error };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Resends an existing signup confirmation email, email change email, SMS OTP or phone change OTP.\n     */\n    async resend(credentials) {\n        try {\n            const endpoint = `${this.url}/resend`;\n            if ('email' in credentials) {\n                const { email, type, options } = credentials;\n                const { error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        email,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                });\n                return { data: { user: null, session: null }, error };\n            }\n            else if ('phone' in credentials) {\n                const { phone, type, options } = credentials;\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', endpoint, {\n                    headers: this.headers,\n                    body: {\n                        phone,\n                        type,\n                        gotrue_meta_security: { captcha_token: options === null || options === void 0 ? void 0 : options.captchaToken },\n                    },\n                });\n                return { data: { user: null, session: null, messageId: data === null || data === void 0 ? void 0 : data.message_id }, error };\n            }\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidCredentialsError('You must provide either an email or phone number and a type');\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns the session, refreshing it if necessary.\n     *\n     * The session returned can be null if the session is not detected which can happen in the event a user is not signed-in or has logged out.\n     *\n     * **IMPORTANT:** This method loads values directly from the storage attached\n     * to the client. If that storage is based on request cookies for example,\n     * the values in it may not be authentic and therefore it's strongly advised\n     * against using this method and its results in such circumstances. A warning\n     * will be emitted if this is detected. Use {@link #getUser()} instead.\n     */\n    async getSession() {\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return this._useSession(async (result) => {\n                return result;\n            });\n        });\n        return result;\n    }\n    /**\n     * Acquires a global lock based on the storage key.\n     */\n    async _acquireLock(acquireTimeout, fn) {\n        this._debug('#_acquireLock', 'begin', acquireTimeout);\n        try {\n            if (this.lockAcquired) {\n                const last = this.pendingInLock.length\n                    ? this.pendingInLock[this.pendingInLock.length - 1]\n                    : Promise.resolve();\n                const result = (async () => {\n                    await last;\n                    return await fn();\n                })();\n                this.pendingInLock.push((async () => {\n                    try {\n                        await result;\n                    }\n                    catch (e) {\n                        // we just care if it finished\n                    }\n                })());\n                return result;\n            }\n            return await this.lock(`lock:${this.storageKey}`, acquireTimeout, async () => {\n                this._debug('#_acquireLock', 'lock acquired for storage key', this.storageKey);\n                try {\n                    this.lockAcquired = true;\n                    const result = fn();\n                    this.pendingInLock.push((async () => {\n                        try {\n                            await result;\n                        }\n                        catch (e) {\n                            // we just care if it finished\n                        }\n                    })());\n                    await result;\n                    // keep draining the queue until there's nothing to wait on\n                    while (this.pendingInLock.length) {\n                        const waitOn = [...this.pendingInLock];\n                        await Promise.all(waitOn);\n                        this.pendingInLock.splice(0, waitOn.length);\n                    }\n                    return await result;\n                }\n                finally {\n                    this._debug('#_acquireLock', 'lock released for storage key', this.storageKey);\n                    this.lockAcquired = false;\n                }\n            });\n        }\n        finally {\n            this._debug('#_acquireLock', 'end');\n        }\n    }\n    /**\n     * Use instead of {@link #getSession} inside the library. It is\n     * semantically usually what you want, as getting a session involves some\n     * processing afterwards that requires only one client operating on the\n     * session at once across multiple tabs or processes.\n     */\n    async _useSession(fn) {\n        this._debug('#_useSession', 'begin');\n        try {\n            // the use of __loadSession here is the only correct use of the function!\n            const result = await this.__loadSession();\n            return await fn(result);\n        }\n        finally {\n            this._debug('#_useSession', 'end');\n        }\n    }\n    /**\n     * NEVER USE DIRECTLY!\n     *\n     * Always use {@link #_useSession}.\n     */\n    async __loadSession() {\n        this._debug('#__loadSession()', 'begin');\n        if (!this.lockAcquired) {\n            this._debug('#__loadSession()', 'used outside of an acquired lock!', new Error().stack);\n        }\n        try {\n            let currentSession = null;\n            const maybeSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug('#getSession()', 'session from storage', maybeSession);\n            if (maybeSession !== null) {\n                if (this._isValidSession(maybeSession)) {\n                    currentSession = maybeSession;\n                }\n                else {\n                    this._debug('#getSession()', 'session from storage is not valid');\n                    await this._removeSession();\n                }\n            }\n            if (!currentSession) {\n                return { data: { session: null }, error: null };\n            }\n            // A session is considered expired before the access token _actually_\n            // expires. When the autoRefreshToken option is off (or when the tab is\n            // in the background), very eager users of getSession() -- like\n            // realtime-js -- might send a valid JWT which will expire by the time it\n            // reaches the server.\n            const hasExpired = currentSession.expires_at\n                ? currentSession.expires_at * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS\n                : false;\n            this._debug('#__loadSession()', `session has${hasExpired ? '' : ' not'} expired`, 'expires_at', currentSession.expires_at);\n            if (!hasExpired) {\n                if (this.storage.isServer) {\n                    let suppressWarning = this.suppressGetSessionWarning;\n                    const proxySession = new Proxy(currentSession, {\n                        get: (target, prop, receiver) => {\n                            if (!suppressWarning && prop === 'user') {\n                                // only show warning when the user object is being accessed from the server\n                                console.warn('Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server.');\n                                suppressWarning = true; // keeps this proxy instance from logging additional warnings\n                                this.suppressGetSessionWarning = true; // keeps this client's future proxy instances from warning\n                            }\n                            return Reflect.get(target, prop, receiver);\n                        },\n                    });\n                    currentSession = proxySession;\n                }\n                return { data: { session: currentSession }, error: null };\n            }\n            const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n            if (error) {\n                return { data: { session: null }, error };\n            }\n            return { data: { session }, error: null };\n        }\n        finally {\n            this._debug('#__loadSession()', 'end');\n        }\n    }\n    /**\n     * Gets the current user details if there is an existing session. This method\n     * performs a network request to the Supabase Auth server, so the returned\n     * value is authentic and can be used to base authorization rules on.\n     *\n     * @param jwt Takes in an optional access token JWT. If no JWT is provided, the JWT from the current session is used.\n     */\n    async getUser(jwt) {\n        if (jwt) {\n            return await this._getUser(jwt);\n        }\n        await this.initializePromise;\n        const result = await this._acquireLock(-1, async () => {\n            return await this._getUser();\n        });\n        return result;\n    }\n    async _getUser(jwt) {\n        try {\n            if (jwt) {\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: jwt,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            }\n            return await this._useSession(async (result) => {\n                var _a, _b, _c;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                // returns an error if there is no access_token or custom authorization header\n                if (!((_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) && !this.hasCustomAuthorizationHeader) {\n                    return { data: { user: null }, error: new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError() };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/user`, {\n                    headers: this.headers,\n                    jwt: (_c = (_b = data.session) === null || _b === void 0 ? void 0 : _b.access_token) !== null && _c !== void 0 ? _c : undefined,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthSessionMissingError)(error)) {\n                    // JWT contains a `session_id` which does not correspond to an active\n                    // session in the database, indicating the user is signed out.\n                    await this._removeSession();\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n                }\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Updates user data for a logged in user.\n     */\n    async updateUser(attributes, options = {}) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._updateUser(attributes, options);\n        });\n    }\n    async _updateUser(attributes, options = {}) {\n        try {\n            return await this._useSession(async (result) => {\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    throw sessionError;\n                }\n                if (!sessionData.session) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const session = sessionData.session;\n                let codeChallenge = null;\n                let codeChallengeMethod = null;\n                if (this.flowType === 'pkce' && attributes.email != null) {\n                    ;\n                    [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n                }\n                const { data, error: userError } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'PUT', `${this.url}/user`, {\n                    headers: this.headers,\n                    redirectTo: options === null || options === void 0 ? void 0 : options.emailRedirectTo,\n                    body: Object.assign(Object.assign({}, attributes), { code_challenge: codeChallenge, code_challenge_method: codeChallengeMethod }),\n                    jwt: session.access_token,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._userResponse,\n                });\n                if (userError)\n                    throw userError;\n                session.user = data.user;\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('USER_UPDATED', session);\n                return { data: { user: session.user }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Sets the session data from the current session. If the current session is expired, setSession will take care of refreshing it to obtain a new session.\n     * If the refresh token or access token in the current session is invalid, an error will be thrown.\n     * @param currentSession The current session that minimally contains an access token and refresh token.\n     */\n    async setSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._setSession(currentSession);\n        });\n    }\n    async _setSession(currentSession) {\n        try {\n            if (!currentSession.access_token || !currentSession.refresh_token) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            }\n            const timeNow = Date.now() / 1000;\n            let expiresAt = timeNow;\n            let hasExpired = true;\n            let session = null;\n            const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(currentSession.access_token);\n            if (payload.exp) {\n                expiresAt = payload.exp;\n                hasExpired = expiresAt <= timeNow;\n            }\n            if (hasExpired) {\n                const { session: refreshedSession, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!refreshedSession) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                session = refreshedSession;\n            }\n            else {\n                const { data, error } = await this._getUser(currentSession.access_token);\n                if (error) {\n                    throw error;\n                }\n                session = {\n                    access_token: currentSession.access_token,\n                    refresh_token: currentSession.refresh_token,\n                    user: data.user,\n                    token_type: 'bearer',\n                    expires_in: expiresAt - timeNow,\n                    expires_at: expiresAt,\n                };\n                await this._saveSession(session);\n                await this._notifyAllSubscribers('SIGNED_IN', session);\n            }\n            return { data: { user: session.user, session }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Returns a new session, regardless of expiry status.\n     * Takes in an optional current session. If not passed in, then refreshSession() will attempt to retrieve it from getSession().\n     * If the current session's refresh token is invalid, an error will be thrown.\n     * @param currentSession The current session. If passed in, it must contain a refresh token.\n     */\n    async refreshSession(currentSession) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._refreshSession(currentSession);\n        });\n    }\n    async _refreshSession(currentSession) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                if (!currentSession) {\n                    const { data, error } = result;\n                    if (error) {\n                        throw error;\n                    }\n                    currentSession = (_a = data.session) !== null && _a !== void 0 ? _a : undefined;\n                }\n                if (!(currentSession === null || currentSession === void 0 ? void 0 : currentSession.refresh_token)) {\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n                }\n                const { session, error } = await this._callRefreshToken(currentSession.refresh_token);\n                if (error) {\n                    return { data: { user: null, session: null }, error: error };\n                }\n                if (!session) {\n                    return { data: { user: null, session: null }, error: null };\n                }\n                return { data: { user: session.user, session }, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { user: null, session: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets the session data from a URL string\n     */\n    async _getSessionFromURL(params, callbackUrlType) {\n        try {\n            if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)())\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No browser detected.');\n            // If there's an error in the URL, it doesn't matter what flow it is, we just return the error.\n            if (params.error || params.error_description || params.error_code) {\n                // The error class returned implies that the redirect is from an implicit grant flow\n                // but it could also be from a redirect error from a PKCE flow.\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError(params.error_description || 'Error in URL with unspecified error_description', {\n                    error: params.error || 'unspecified_error',\n                    code: params.error_code || 'unspecified_code',\n                });\n            }\n            // Checks for mismatches between the flowType initialised in the client and the URL parameters\n            switch (callbackUrlType) {\n                case 'implicit':\n                    if (this.flowType === 'pkce') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('Not a valid PKCE flow url.');\n                    }\n                    break;\n                case 'pkce':\n                    if (this.flowType === 'implicit') {\n                        throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('Not a valid implicit grant flow url.');\n                    }\n                    break;\n                default:\n                // there's no mismatch so we continue\n            }\n            // Since this is a redirect for PKCE, we attempt to retrieve the code from the URL for the code exchange\n            if (callbackUrlType === 'pkce') {\n                this._debug('#_initialize()', 'begin', 'is PKCE flow', true);\n                if (!params.code)\n                    throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthPKCEGrantCodeExchangeError('No code detected.');\n                const { data, error } = await this._exchangeCodeForSession(params.code);\n                if (error)\n                    throw error;\n                const url = new URL(window.location.href);\n                url.searchParams.delete('code');\n                window.history.replaceState(window.history.state, '', url.toString());\n                return { data: { session: data.session, redirectType: null }, error: null };\n            }\n            const { provider_token, provider_refresh_token, access_token, refresh_token, expires_in, expires_at, token_type, } = params;\n            if (!access_token || !expires_in || !refresh_token || !token_type) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthImplicitGrantRedirectError('No session defined in URL');\n            }\n            const timeNow = Math.round(Date.now() / 1000);\n            const expiresIn = parseInt(expires_in);\n            let expiresAt = timeNow + expiresIn;\n            if (expires_at) {\n                expiresAt = parseInt(expires_at);\n            }\n            const actuallyExpiresIn = expiresAt - timeNow;\n            if (actuallyExpiresIn * 1000 <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS) {\n                console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${actuallyExpiresIn}s, should have been closer to ${expiresIn}s`);\n            }\n            const issuedAt = expiresAt - expiresIn;\n            if (timeNow - issuedAt >= 120) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale', issuedAt, expiresAt, timeNow);\n            }\n            else if (timeNow - issuedAt < 0) {\n                console.warn('@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew', issuedAt, expiresAt, timeNow);\n            }\n            const { data, error } = await this._getUser(access_token);\n            if (error)\n                throw error;\n            const session = {\n                provider_token,\n                provider_refresh_token,\n                access_token,\n                expires_in: expiresIn,\n                expires_at: expiresAt,\n                refresh_token,\n                token_type,\n                user: data.user,\n            };\n            // Remove tokens from URL\n            window.location.hash = '';\n            this._debug('#_getSessionFromURL()', 'clearing window.location.hash');\n            return { data: { session, redirectType: params.type }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, redirectType: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Checks if the current URL contains parameters given by an implicit oauth grant flow (https://www.rfc-editor.org/rfc/rfc6749.html#section-4.2)\n     */\n    _isImplicitGrantCallback(params) {\n        return Boolean(params.access_token || params.error_description);\n    }\n    /**\n     * Checks if the current URL and backing storage contain parameters given by a PKCE flow\n     */\n    async _isPKCECallback(params) {\n        const currentStorageContent = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n        return !!(params.code && currentStorageContent);\n    }\n    /**\n     * Inside a browser context, `signOut()` will remove the logged in user from the browser session and log them out - removing all items from localstorage and then trigger a `\"SIGNED_OUT\"` event.\n     *\n     * For server-side management, you can revoke all refresh tokens for a user by passing a user's JWT through to `auth.api.signOut(JWT: string)`.\n     * There is no way to revoke a user's access token jwt until it expires. It is recommended to set a shorter expiry on the jwt for this reason.\n     *\n     * If using `others` scope, no `SIGNED_OUT` event is fired!\n     */\n    async signOut(options = { scope: 'global' }) {\n        await this.initializePromise;\n        return await this._acquireLock(-1, async () => {\n            return await this._signOut(options);\n        });\n    }\n    async _signOut({ scope } = { scope: 'global' }) {\n        return await this._useSession(async (result) => {\n            var _a;\n            const { data, error: sessionError } = result;\n            if (sessionError) {\n                return { error: sessionError };\n            }\n            const accessToken = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token;\n            if (accessToken) {\n                const { error } = await this.admin.signOut(accessToken, scope);\n                if (error) {\n                    // ignore 404s since user might not exist anymore\n                    // ignore 401s since an invalid or expired JWT should sign out the current session\n                    if (!((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthApiError)(error) &&\n                        (error.status === 404 || error.status === 401 || error.status === 403))) {\n                        return { error };\n                    }\n                }\n            }\n            if (scope !== 'others') {\n                await this._removeSession();\n                await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, `${this.storageKey}-code-verifier`);\n            }\n            return { error: null };\n        });\n    }\n    /**\n     * Receive a notification every time an auth event happens.\n     * @param callback A callback function to be invoked when an auth event happens.\n     */\n    onAuthStateChange(callback) {\n        const id = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.uuid)();\n        const subscription = {\n            id,\n            callback,\n            unsubscribe: () => {\n                this._debug('#unsubscribe()', 'state change callback with id removed', id);\n                this.stateChangeEmitters.delete(id);\n            },\n        };\n        this._debug('#onAuthStateChange()', 'registered callback with id', id);\n        this.stateChangeEmitters.set(id, subscription);\n        (async () => {\n            await this.initializePromise;\n            await this._acquireLock(-1, async () => {\n                this._emitInitialSession(id);\n            });\n        })();\n        return { data: { subscription } };\n    }\n    async _emitInitialSession(id) {\n        return await this._useSession(async (result) => {\n            var _a, _b;\n            try {\n                const { data: { session }, error, } = result;\n                if (error)\n                    throw error;\n                await ((_a = this.stateChangeEmitters.get(id)) === null || _a === void 0 ? void 0 : _a.callback('INITIAL_SESSION', session));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'session', session);\n            }\n            catch (err) {\n                await ((_b = this.stateChangeEmitters.get(id)) === null || _b === void 0 ? void 0 : _b.callback('INITIAL_SESSION', null));\n                this._debug('INITIAL_SESSION', 'callback id', id, 'error', err);\n                console.error(err);\n            }\n        });\n    }\n    /**\n     * Sends a password reset request to an email address. This method supports the PKCE flow.\n     *\n     * @param email The email address of the user.\n     * @param options.redirectTo The URL to send the user to after they click the password reset link.\n     * @param options.captchaToken Verification token received when the user completes the captcha on the site.\n     */\n    async resetPasswordForEmail(email, options = {}) {\n        let codeChallenge = null;\n        let codeChallengeMethod = null;\n        if (this.flowType === 'pkce') {\n            ;\n            [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey, true // isPasswordRecovery\n            );\n        }\n        try {\n            return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/recover`, {\n                body: {\n                    email,\n                    code_challenge: codeChallenge,\n                    code_challenge_method: codeChallengeMethod,\n                    gotrue_meta_security: { captcha_token: options.captchaToken },\n                },\n                headers: this.headers,\n                redirectTo: options.redirectTo,\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Gets all the identities linked to a user.\n     */\n    async getUserIdentities() {\n        var _a;\n        try {\n            const { data, error } = await this.getUser();\n            if (error)\n                throw error;\n            return { data: { identities: (_a = data.user.identities) !== null && _a !== void 0 ? _a : [] }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Links an oauth identity to an existing user.\n     * This method supports the PKCE flow.\n     */\n    async linkIdentity(credentials) {\n        var _a;\n        try {\n            const { data, error } = await this._useSession(async (result) => {\n                var _a, _b, _c, _d, _e;\n                const { data, error } = result;\n                if (error)\n                    throw error;\n                const url = await this._getUrlForProvider(`${this.url}/user/identities/authorize`, credentials.provider, {\n                    redirectTo: (_a = credentials.options) === null || _a === void 0 ? void 0 : _a.redirectTo,\n                    scopes: (_b = credentials.options) === null || _b === void 0 ? void 0 : _b.scopes,\n                    queryParams: (_c = credentials.options) === null || _c === void 0 ? void 0 : _c.queryParams,\n                    skipBrowserRedirect: true,\n                });\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', url, {\n                    headers: this.headers,\n                    jwt: (_e = (_d = data.session) === null || _d === void 0 ? void 0 : _d.access_token) !== null && _e !== void 0 ? _e : undefined,\n                });\n            });\n            if (error)\n                throw error;\n            if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !((_a = credentials.options) === null || _a === void 0 ? void 0 : _a.skipBrowserRedirect)) {\n                window.location.assign(data === null || data === void 0 ? void 0 : data.url);\n            }\n            return { data: { provider: credentials.provider, url: data === null || data === void 0 ? void 0 : data.url }, error: null };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { provider: credentials.provider, url: null }, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Unlinks an identity from a user by deleting it. The user will no longer be able to sign in with that identity once it's unlinked.\n     */\n    async unlinkIdentity(identity) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data, error } = result;\n                if (error) {\n                    throw error;\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/user/identities/${identity.identity_id}`, {\n                    headers: this.headers,\n                    jwt: (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : undefined,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * Generates a new JWT.\n     * @param refreshToken A valid refresh token that was returned on login.\n     */\n    async _refreshAccessToken(refreshToken) {\n        const debugName = `#_refreshAccessToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            const startedAt = Date.now();\n            // will attempt to refresh the token with exponential backoff\n            return await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.retryable)(async (attempt) => {\n                if (attempt > 0) {\n                    await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.sleep)(200 * Math.pow(2, attempt - 1)); // 200, 400, 800, ...\n                }\n                this._debug(debugName, 'refreshing attempt', attempt);\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/token?grant_type=refresh_token`, {\n                    body: { refresh_token: refreshToken },\n                    headers: this.headers,\n                    xform: _lib_fetch__WEBPACK_IMPORTED_MODULE_3__._sessionResponse,\n                });\n            }, (attempt, error) => {\n                const nextBackOffInterval = 200 * Math.pow(2, attempt);\n                return (error &&\n                    (0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error) &&\n                    // retryable only if the request can be sent before the backoff overflows the tick duration\n                    Date.now() + nextBackOffInterval - startedAt < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n            });\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: { session: null, user: null }, error };\n            }\n            throw error;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    _isValidSession(maybeSession) {\n        const isValidSession = typeof maybeSession === 'object' &&\n            maybeSession !== null &&\n            'access_token' in maybeSession &&\n            'refresh_token' in maybeSession &&\n            'expires_at' in maybeSession;\n        return isValidSession;\n    }\n    async _handleProviderSignIn(provider, options) {\n        const url = await this._getUrlForProvider(`${this.url}/authorize`, provider, {\n            redirectTo: options.redirectTo,\n            scopes: options.scopes,\n            queryParams: options.queryParams,\n        });\n        this._debug('#_handleProviderSignIn()', 'provider', provider, 'options', options, 'url', url);\n        // try to open on the browser\n        if ((0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && !options.skipBrowserRedirect) {\n            window.location.assign(url);\n        }\n        return { data: { provider, url }, error: null };\n    }\n    /**\n     * Recovers the session from LocalStorage and refreshes the token\n     * Note: this method is async to accommodate for AsyncStorage e.g. in React native.\n     */\n    async _recoverAndRefresh() {\n        var _a;\n        const debugName = '#_recoverAndRefresh()';\n        this._debug(debugName, 'begin');\n        try {\n            const currentSession = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getItemAsync)(this.storage, this.storageKey);\n            this._debug(debugName, 'session from storage', currentSession);\n            if (!this._isValidSession(currentSession)) {\n                this._debug(debugName, 'session is not valid');\n                if (currentSession !== null) {\n                    await this._removeSession();\n                }\n                return;\n            }\n            const expiresWithMargin = ((_a = currentSession.expires_at) !== null && _a !== void 0 ? _a : Infinity) * 1000 - Date.now() < _lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS;\n            this._debug(debugName, `session has${expiresWithMargin ? '' : ' not'} expired with margin of ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.EXPIRY_MARGIN_MS}s`);\n            if (expiresWithMargin) {\n                if (this.autoRefreshToken && currentSession.refresh_token) {\n                    const { error } = await this._callRefreshToken(currentSession.refresh_token);\n                    if (error) {\n                        console.error(error);\n                        if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                            this._debug(debugName, 'refresh failed with a non-retryable error, removing the session', error);\n                            await this._removeSession();\n                        }\n                    }\n                }\n            }\n            else {\n                // no need to persist currentSession again, as we just loaded it from\n                // local storage; persisting it again may overwrite a value saved by\n                // another client with access to the same local storage\n                await this._notifyAllSubscribers('SIGNED_IN', currentSession);\n            }\n        }\n        catch (err) {\n            this._debug(debugName, 'error', err);\n            console.error(err);\n            return;\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    async _callRefreshToken(refreshToken) {\n        var _a, _b;\n        if (!refreshToken) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n        }\n        // refreshing is already in progress\n        if (this.refreshingDeferred) {\n            return this.refreshingDeferred.promise;\n        }\n        const debugName = `#_callRefreshToken(${refreshToken.substring(0, 5)}...)`;\n        this._debug(debugName, 'begin');\n        try {\n            this.refreshingDeferred = new _lib_helpers__WEBPACK_IMPORTED_MODULE_4__.Deferred();\n            const { data, error } = await this._refreshAccessToken(refreshToken);\n            if (error)\n                throw error;\n            if (!data.session)\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n            await this._saveSession(data.session);\n            await this._notifyAllSubscribers('TOKEN_REFRESHED', data.session);\n            const result = { session: data.session, error: null };\n            this.refreshingDeferred.resolve(result);\n            return result;\n        }\n        catch (error) {\n            this._debug(debugName, 'error', error);\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                const result = { session: null, error };\n                if (!(0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthRetryableFetchError)(error)) {\n                    await this._removeSession();\n                }\n                (_a = this.refreshingDeferred) === null || _a === void 0 ? void 0 : _a.resolve(result);\n                return result;\n            }\n            (_b = this.refreshingDeferred) === null || _b === void 0 ? void 0 : _b.reject(error);\n            throw error;\n        }\n        finally {\n            this.refreshingDeferred = null;\n            this._debug(debugName, 'end');\n        }\n    }\n    async _notifyAllSubscribers(event, session, broadcast = true) {\n        const debugName = `#_notifyAllSubscribers(${event})`;\n        this._debug(debugName, 'begin', session, `broadcast = ${broadcast}`);\n        try {\n            if (this.broadcastChannel && broadcast) {\n                this.broadcastChannel.postMessage({ event, session });\n            }\n            const errors = [];\n            const promises = Array.from(this.stateChangeEmitters.values()).map(async (x) => {\n                try {\n                    await x.callback(event, session);\n                }\n                catch (e) {\n                    errors.push(e);\n                }\n            });\n            await Promise.all(promises);\n            if (errors.length > 0) {\n                for (let i = 0; i < errors.length; i += 1) {\n                    console.error(errors[i]);\n                }\n                throw errors[0];\n            }\n        }\n        finally {\n            this._debug(debugName, 'end');\n        }\n    }\n    /**\n     * set currentSession and currentUser\n     * process to _startAutoRefreshToken if possible\n     */\n    async _saveSession(session) {\n        this._debug('#_saveSession()', session);\n        // _saveSession is always called whenever a new session has been acquired\n        // so we can safely suppress the warning returned by future getSession calls\n        this.suppressGetSessionWarning = true;\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.setItemAsync)(this.storage, this.storageKey, session);\n    }\n    async _removeSession() {\n        this._debug('#_removeSession()');\n        await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.removeItemAsync)(this.storage, this.storageKey);\n        await this._notifyAllSubscribers('SIGNED_OUT', null);\n    }\n    /**\n     * Removes any registered visibilitychange callback.\n     *\n     * {@see #startAutoRefresh}\n     * {@see #stopAutoRefresh}\n     */\n    _removeVisibilityChangedCallback() {\n        this._debug('#_removeVisibilityChangedCallback()');\n        const callback = this.visibilityChangedCallback;\n        this.visibilityChangedCallback = null;\n        try {\n            if (callback && (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() && (window === null || window === void 0 ? void 0 : window.removeEventListener)) {\n                window.removeEventListener('visibilitychange', callback);\n            }\n        }\n        catch (e) {\n            console.error('removing visibilitychange callback failed', e);\n        }\n    }\n    /**\n     * This is the private implementation of {@link #startAutoRefresh}. Use this\n     * within the library.\n     */\n    async _startAutoRefresh() {\n        await this._stopAutoRefresh();\n        this._debug('#_startAutoRefresh()');\n        const ticker = setInterval(() => this._autoRefreshTokenTick(), _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n        this.autoRefreshTicker = ticker;\n        if (ticker && typeof ticker === 'object' && typeof ticker.unref === 'function') {\n            // ticker is a NodeJS Timeout object that has an `unref` method\n            // https://nodejs.org/api/timers.html#timeoutunref\n            // When auto refresh is used in NodeJS (like for testing) the\n            // `setInterval` is preventing the process from being marked as\n            // finished and tests run endlessly. This can be prevented by calling\n            // `unref()` on the returned object.\n            ticker.unref();\n            // @ts-expect-error TS has no context of Deno\n        }\n        else if (typeof Deno !== 'undefined' && typeof Deno.unrefTimer === 'function') {\n            // similar like for NodeJS, but with the Deno API\n            // https://deno.land/api@latest?unstable&s=Deno.unrefTimer\n            // @ts-expect-error TS has no context of Deno\n            Deno.unrefTimer(ticker);\n        }\n        // run the tick immediately, but in the next pass of the event loop so that\n        // #_initialize can be allowed to complete without recursively waiting on\n        // itself\n        setTimeout(async () => {\n            await this.initializePromise;\n            await this._autoRefreshTokenTick();\n        }, 0);\n    }\n    /**\n     * This is the private implementation of {@link #stopAutoRefresh}. Use this\n     * within the library.\n     */\n    async _stopAutoRefresh() {\n        this._debug('#_stopAutoRefresh()');\n        const ticker = this.autoRefreshTicker;\n        this.autoRefreshTicker = null;\n        if (ticker) {\n            clearInterval(ticker);\n        }\n    }\n    /**\n     * Starts an auto-refresh process in the background. The session is checked\n     * every few seconds. Close to the time of expiration a process is started to\n     * refresh the session. If refreshing fails it will be retried for as long as\n     * necessary.\n     *\n     * If you set the {@link GoTrueClientOptions#autoRefreshToken} you don't need\n     * to call this function, it will be called for you.\n     *\n     * On browsers the refresh process works only when the tab/window is in the\n     * foreground to conserve resources as well as prevent race conditions and\n     * flooding auth with requests. If you call this method any managed\n     * visibility change callback will be removed and you must manage visibility\n     * changes on your own.\n     *\n     * On non-browser platforms the refresh process works *continuously* in the\n     * background, which may not be desirable. You should hook into your\n     * platform's foreground indication mechanism and call these methods\n     * appropriately to conserve resources.\n     *\n     * {@see #stopAutoRefresh}\n     */\n    async startAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._startAutoRefresh();\n    }\n    /**\n     * Stops an active auto refresh process running in the background (if any).\n     *\n     * If you call this method any managed visibility change callback will be\n     * removed and you must manage visibility changes on your own.\n     *\n     * See {@link #startAutoRefresh} for more details.\n     */\n    async stopAutoRefresh() {\n        this._removeVisibilityChangedCallback();\n        await this._stopAutoRefresh();\n    }\n    /**\n     * Runs the auto refresh token tick.\n     */\n    async _autoRefreshTokenTick() {\n        this._debug('#_autoRefreshTokenTick()', 'begin');\n        try {\n            await this._acquireLock(0, async () => {\n                try {\n                    const now = Date.now();\n                    try {\n                        return await this._useSession(async (result) => {\n                            const { data: { session }, } = result;\n                            if (!session || !session.refresh_token || !session.expires_at) {\n                                this._debug('#_autoRefreshTokenTick()', 'no session');\n                                return;\n                            }\n                            // session will expire in this many ticks (or has already expired if <= 0)\n                            const expiresInTicks = Math.floor((session.expires_at * 1000 - now) / _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS);\n                            this._debug('#_autoRefreshTokenTick()', `access token expires in ${expiresInTicks} ticks, a tick lasts ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_DURATION_MS}ms, refresh threshold is ${_lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD} ticks`);\n                            if (expiresInTicks <= _lib_constants__WEBPACK_IMPORTED_MODULE_1__.AUTO_REFRESH_TICK_THRESHOLD) {\n                                await this._callRefreshToken(session.refresh_token);\n                            }\n                        });\n                    }\n                    catch (e) {\n                        console.error('Auto refresh tick failed with error. This is likely a transient error.', e);\n                    }\n                }\n                finally {\n                    this._debug('#_autoRefreshTokenTick()', 'end');\n                }\n            });\n        }\n        catch (e) {\n            if (e.isAcquireTimeout || e instanceof _lib_locks__WEBPACK_IMPORTED_MODULE_8__.LockAcquireTimeoutError) {\n                this._debug('auto refresh token tick lock not available');\n            }\n            else {\n                throw e;\n            }\n        }\n    }\n    /**\n     * Registers callbacks on the browser / platform, which in-turn run\n     * algorithms when the browser window/tab are in foreground. On non-browser\n     * platforms it assumes always foreground.\n     */\n    async _handleVisibilityChange() {\n        this._debug('#_handleVisibilityChange()');\n        if (!(0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.isBrowser)() || !(window === null || window === void 0 ? void 0 : window.addEventListener)) {\n            if (this.autoRefreshToken) {\n                // in non-browser environments the refresh token ticker runs always\n                this.startAutoRefresh();\n            }\n            return false;\n        }\n        try {\n            this.visibilityChangedCallback = async () => await this._onVisibilityChanged(false);\n            window === null || window === void 0 ? void 0 : window.addEventListener('visibilitychange', this.visibilityChangedCallback);\n            // now immediately call the visbility changed callback to setup with the\n            // current visbility state\n            await this._onVisibilityChanged(true); // initial call\n        }\n        catch (error) {\n            console.error('_handleVisibilityChange', error);\n        }\n    }\n    /**\n     * Callback registered with `window.addEventListener('visibilitychange')`.\n     */\n    async _onVisibilityChanged(calledFromInitialize) {\n        const methodName = `#_onVisibilityChanged(${calledFromInitialize})`;\n        this._debug(methodName, 'visibilityState', document.visibilityState);\n        if (document.visibilityState === 'visible') {\n            if (this.autoRefreshToken) {\n                // in browser environments the refresh token ticker runs only on focused tabs\n                // which prevents race conditions\n                this._startAutoRefresh();\n            }\n            if (!calledFromInitialize) {\n                // called when the visibility has changed, i.e. the browser\n                // transitioned from hidden -> visible so we need to see if the session\n                // should be recovered immediately... but to do that we need to acquire\n                // the lock first asynchronously\n                await this.initializePromise;\n                await this._acquireLock(-1, async () => {\n                    if (document.visibilityState !== 'visible') {\n                        this._debug(methodName, 'acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting');\n                        // visibility has changed while waiting for the lock, abort\n                        return;\n                    }\n                    // recover the session\n                    await this._recoverAndRefresh();\n                });\n            }\n        }\n        else if (document.visibilityState === 'hidden') {\n            if (this.autoRefreshToken) {\n                this._stopAutoRefresh();\n            }\n        }\n    }\n    /**\n     * Generates the relevant login URL for a third-party provider.\n     * @param options.redirectTo A URL or mobile address to send the user to after they are confirmed.\n     * @param options.scopes A space-separated list of scopes granted to the OAuth application.\n     * @param options.queryParams An object of key-value pairs containing query parameters granted to the OAuth application.\n     */\n    async _getUrlForProvider(url, provider, options) {\n        const urlParams = [`provider=${encodeURIComponent(provider)}`];\n        if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n            urlParams.push(`redirect_to=${encodeURIComponent(options.redirectTo)}`);\n        }\n        if (options === null || options === void 0 ? void 0 : options.scopes) {\n            urlParams.push(`scopes=${encodeURIComponent(options.scopes)}`);\n        }\n        if (this.flowType === 'pkce') {\n            const [codeChallenge, codeChallengeMethod] = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getCodeChallengeAndMethod)(this.storage, this.storageKey);\n            const flowParams = new URLSearchParams({\n                code_challenge: `${encodeURIComponent(codeChallenge)}`,\n                code_challenge_method: `${encodeURIComponent(codeChallengeMethod)}`,\n            });\n            urlParams.push(flowParams.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.queryParams) {\n            const query = new URLSearchParams(options.queryParams);\n            urlParams.push(query.toString());\n        }\n        if (options === null || options === void 0 ? void 0 : options.skipBrowserRedirect) {\n            urlParams.push(`skip_http_redirect=${options.skipBrowserRedirect}`);\n        }\n        return `${url}?${urlParams.join('&')}`;\n    }\n    async _unenroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'DELETE', `${this.url}/factors/${params.factorId}`, {\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    async _enroll(params) {\n        try {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: sessionData, error: sessionError } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                const body = Object.assign({ friendly_name: params.friendlyName, factor_type: params.factorType }, (params.factorType === 'phone' ? { phone: params.phone } : { issuer: params.issuer }));\n                const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors`, {\n                    body,\n                    headers: this.headers,\n                    jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                });\n                if (error) {\n                    return { data: null, error };\n                }\n                if (params.factorType === 'totp' && ((_b = data === null || data === void 0 ? void 0 : data.totp) === null || _b === void 0 ? void 0 : _b.qr_code)) {\n                    data.totp.qr_code = `data:image/svg+xml;utf-8,${data.totp.qr_code}`;\n                }\n                return { data, error: null };\n            });\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n    /**\n     * {@see GoTrueMFAApi#verify}\n     */\n    async _verify(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/verify`, {\n                        body: { code: params.code, challenge_id: params.challengeId },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                    if (error) {\n                        return { data: null, error };\n                    }\n                    await this._saveSession(Object.assign({ expires_at: Math.round(Date.now() / 1000) + data.expires_in }, data));\n                    await this._notifyAllSubscribers('MFA_CHALLENGE_VERIFIED', data);\n                    return { data, error };\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challenge}\n     */\n    async _challenge(params) {\n        return this._acquireLock(-1, async () => {\n            try {\n                return await this._useSession(async (result) => {\n                    var _a;\n                    const { data: sessionData, error: sessionError } = result;\n                    if (sessionError) {\n                        return { data: null, error: sessionError };\n                    }\n                    return await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'POST', `${this.url}/factors/${params.factorId}/challenge`, {\n                        body: { channel: params.channel },\n                        headers: this.headers,\n                        jwt: (_a = sessionData === null || sessionData === void 0 ? void 0 : sessionData.session) === null || _a === void 0 ? void 0 : _a.access_token,\n                    });\n                });\n            }\n            catch (error) {\n                if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                    return { data: null, error };\n                }\n                throw error;\n            }\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#challengeAndVerify}\n     */\n    async _challengeAndVerify(params) {\n        // both _challenge and _verify independently acquire the lock, so no need\n        // to acquire it here\n        const { data: challengeData, error: challengeError } = await this._challenge({\n            factorId: params.factorId,\n        });\n        if (challengeError) {\n            return { data: null, error: challengeError };\n        }\n        return await this._verify({\n            factorId: params.factorId,\n            challengeId: challengeData.id,\n            code: params.code,\n        });\n    }\n    /**\n     * {@see GoTrueMFAApi#listFactors}\n     */\n    async _listFactors() {\n        // use #getUser instead of #_getUser as the former acquires a lock\n        const { data: { user }, error: userError, } = await this.getUser();\n        if (userError) {\n            return { data: null, error: userError };\n        }\n        const factors = (user === null || user === void 0 ? void 0 : user.factors) || [];\n        const totp = factors.filter((factor) => factor.factor_type === 'totp' && factor.status === 'verified');\n        const phone = factors.filter((factor) => factor.factor_type === 'phone' && factor.status === 'verified');\n        return {\n            data: {\n                all: factors,\n                totp,\n                phone,\n            },\n            error: null,\n        };\n    }\n    /**\n     * {@see GoTrueMFAApi#getAuthenticatorAssuranceLevel}\n     */\n    async _getAuthenticatorAssuranceLevel() {\n        return this._acquireLock(-1, async () => {\n            return await this._useSession(async (result) => {\n                var _a, _b;\n                const { data: { session }, error: sessionError, } = result;\n                if (sessionError) {\n                    return { data: null, error: sessionError };\n                }\n                if (!session) {\n                    return {\n                        data: { currentLevel: null, nextLevel: null, currentAuthenticationMethods: [] },\n                        error: null,\n                    };\n                }\n                const { payload } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(session.access_token);\n                let currentLevel = null;\n                if (payload.aal) {\n                    currentLevel = payload.aal;\n                }\n                let nextLevel = currentLevel;\n                const verifiedFactors = (_b = (_a = session.user.factors) === null || _a === void 0 ? void 0 : _a.filter((factor) => factor.status === 'verified')) !== null && _b !== void 0 ? _b : [];\n                if (verifiedFactors.length > 0) {\n                    nextLevel = 'aal2';\n                }\n                const currentAuthenticationMethods = payload.amr || [];\n                return { data: { currentLevel, nextLevel, currentAuthenticationMethods }, error: null };\n            });\n        });\n    }\n    async fetchJwk(kid, jwks = { keys: [] }) {\n        // try fetching from the supplied jwks\n        let jwk = jwks.keys.find((key) => key.kid === kid);\n        if (jwk) {\n            return jwk;\n        }\n        // try fetching from cache\n        jwk = this.jwks.keys.find((key) => key.kid === kid);\n        // jwk exists and jwks isn't stale\n        if (jwk && this.jwks_cached_at + _lib_constants__WEBPACK_IMPORTED_MODULE_1__.JWKS_TTL > Date.now()) {\n            return jwk;\n        }\n        // jwk isn't cached in memory so we need to fetch it from the well-known endpoint\n        const { data, error } = await (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_3__._request)(this.fetch, 'GET', `${this.url}/.well-known/jwks.json`, {\n            headers: this.headers,\n        });\n        if (error) {\n            throw error;\n        }\n        if (!data.keys || data.keys.length === 0) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('JWKS is empty');\n        }\n        this.jwks = data;\n        this.jwks_cached_at = Date.now();\n        // Find the signing key\n        jwk = data.keys.find((key) => key.kid === kid);\n        if (!jwk) {\n            throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('No matching signing key found in JWKS');\n        }\n        return jwk;\n    }\n    /**\n     * @experimental This method may change in future versions.\n     * @description Gets the claims from a JWT. If the JWT is symmetric JWTs, it will call getUser() to verify against the server. If the JWT is asymmetric, it will be verified against the JWKS using the WebCrypto API.\n     */\n    async getClaims(jwt, jwks = { keys: [] }) {\n        try {\n            let token = jwt;\n            if (!token) {\n                const { data, error } = await this.getSession();\n                if (error || !data.session) {\n                    return { data: null, error };\n                }\n                token = data.session.access_token;\n            }\n            const { header, payload, signature, raw: { header: rawHeader, payload: rawPayload }, } = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.decodeJWT)(token);\n            // Reject expired JWTs\n            (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.validateExp)(payload.exp);\n            // If symmetric algorithm or WebCrypto API is unavailable, fallback to getUser()\n            if (!header.kid ||\n                header.alg === 'HS256' ||\n                !('crypto' in globalThis && 'subtle' in globalThis.crypto)) {\n                const { error } = await this.getUser(token);\n                if (error) {\n                    throw error;\n                }\n                // getUser succeeds so the claims in the JWT can be trusted\n                return {\n                    data: {\n                        claims: payload,\n                        header,\n                        signature,\n                    },\n                    error: null,\n                };\n            }\n            const algorithm = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_4__.getAlgorithm)(header.alg);\n            const signingKey = await this.fetchJwk(header.kid, jwks);\n            // Convert JWK to CryptoKey\n            const publicKey = await crypto.subtle.importKey('jwk', signingKey, algorithm, true, [\n                'verify',\n            ]);\n            // Verify the signature\n            const isValid = await crypto.subtle.verify(algorithm, publicKey, signature, (0,_lib_base64url__WEBPACK_IMPORTED_MODULE_9__.stringToUint8Array)(`${rawHeader}.${rawPayload}`));\n            if (!isValid) {\n                throw new _lib_errors__WEBPACK_IMPORTED_MODULE_2__.AuthInvalidJwtError('Invalid JWT signature');\n            }\n            // If verification succeeds, decode and return claims\n            return {\n                data: {\n                    claims: payload,\n                    header,\n                    signature,\n                },\n                error: null,\n            };\n        }\n        catch (error) {\n            if ((0,_lib_errors__WEBPACK_IMPORTED_MODULE_2__.isAuthError)(error)) {\n                return { data: null, error };\n            }\n            throw error;\n        }\n    }\n}\nGoTrueClient.nextInstanceID = 0;\n//# sourceMappingURL=GoTrueClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _AuthClient__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   AuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.CustomAuthError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _lib_types__WEBPACK_IMPORTED_MODULE_4__.SIGN_OUT_SCOPES),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _lib_errors__WEBPACK_IMPORTED_MODULE_5__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.internals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _lib_locks__WEBPACK_IMPORTED_MODULE_6__.processLock)\n/* harmony export */ });\n/* harmony import */ var _GoTrueAdminApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GoTrueAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js\");\n/* harmony import */ var _GoTrueClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GoTrueClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/GoTrueClient.js\");\n/* harmony import */ var _AuthAdminApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthAdminApi */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js\");\n/* harmony import */ var _AuthClient__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/AuthClient.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/types */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js\");\n/* harmony import */ var _lib_errors__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _lib_locks__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/locks */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js\");\n\n\n\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNKO0FBQ0E7QUFDSjtBQUM0QjtBQUN0QztBQUNDO0FBQzJGO0FBQ3hIIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlZHBhbmRhXFxEZXNrdG9wXFxNZXRhbW9ycGhpYyBmbHV4XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjcwLjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBHb1RydWVBZG1pbkFwaSBmcm9tICcuL0dvVHJ1ZUFkbWluQXBpJztcbmltcG9ydCBHb1RydWVDbGllbnQgZnJvbSAnLi9Hb1RydWVDbGllbnQnO1xuaW1wb3J0IEF1dGhBZG1pbkFwaSBmcm9tICcuL0F1dGhBZG1pbkFwaSc7XG5pbXBvcnQgQXV0aENsaWVudCBmcm9tICcuL0F1dGhDbGllbnQnO1xuZXhwb3J0IHsgR29UcnVlQWRtaW5BcGksIEdvVHJ1ZUNsaWVudCwgQXV0aEFkbWluQXBpLCBBdXRoQ2xpZW50IH07XG5leHBvcnQgKiBmcm9tICcuL2xpYi90eXBlcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9lcnJvcnMnO1xuZXhwb3J0IHsgbmF2aWdhdG9yTG9jaywgTmF2aWdhdG9yTG9ja0FjcXVpcmVUaW1lb3V0RXJyb3IsIGludGVybmFscyBhcyBsb2NrSW50ZXJuYWxzLCBwcm9jZXNzTG9jaywgfSBmcm9tICcuL2xpYi9sb2Nrcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   base64UrlToUint8Array: () => (/* binding */ base64UrlToUint8Array),\n/* harmony export */   byteFromBase64URL: () => (/* binding */ byteFromBase64URL),\n/* harmony export */   byteToBase64URL: () => (/* binding */ byteToBase64URL),\n/* harmony export */   bytesToBase64URL: () => (/* binding */ bytesToBase64URL),\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8),\n/* harmony export */   stringToUint8Array: () => (/* binding */ stringToUint8Array)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_'.split('');\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = ' \\t\\n\\r='.split('');\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a byte to a Base64-URL string.\n *\n * @param byte The byte to convert, or null to flush at the end of the byte sequence.\n * @param state The Base64 conversion state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next Base64 character when ready.\n */\nfunction byteToBase64URL(byte, state, emit) {\n    if (byte !== null) {\n        state.queue = (state.queue << 8) | byte;\n        state.queuedBits += 8;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n    else if (state.queuedBits > 0) {\n        state.queue = state.queue << (6 - state.queuedBits);\n        state.queuedBits = 6;\n        while (state.queuedBits >= 6) {\n            const pos = (state.queue >> (state.queuedBits - 6)) & 63;\n            emit(TO_BASE64URL[pos]);\n            state.queuedBits -= 6;\n        }\n    }\n}\n/**\n * Converts a String char code (extracted using `string.charCodeAt(position)`) to a sequence of Base64-URL characters.\n *\n * @param charCode The char code of the JavaScript string.\n * @param state The Base64 state. Pass an initial value of `{ queue: 0, queuedBits: 0 }`.\n * @param emit A function called with the next byte.\n */\nfunction byteFromBase64URL(charCode, state, emit) {\n    const bits = FROM_BASE64URL[charCode];\n    if (bits > -1) {\n        // valid Base64-URL character\n        state.queue = (state.queue << 6) | bits;\n        state.queuedBits += 6;\n        while (state.queuedBits >= 8) {\n            emit((state.queue >> (state.queuedBits - 8)) & 0xff);\n            state.queuedBits -= 8;\n        }\n    }\n    else if (bits === -2) {\n        // ignore spaces, tabs, newlines, =\n        return;\n    }\n    else {\n        throw new Error(`Invalid Base64-URL character \"${String.fromCharCode(charCode)}\"`);\n    }\n}\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    const emitter = (char) => {\n        base64.push(char);\n    };\n    const state = { queue: 0, queuedBits: 0 };\n    stringToUTF8(str, (byte) => {\n        byteToBase64URL(byte, state, emitter);\n    });\n    byteToBase64URL(null, state, emitter);\n    return base64.join('');\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const utf8Emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const utf8State = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    const b64State = { queue: 0, queuedBits: 0 };\n    const byteEmit = (byte) => {\n        stringFromUTF8(byte, utf8State, utf8Emit);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), b64State, byteEmit);\n    }\n    return conv.join('');\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error('Invalid UTF-8 sequence');\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n/**\n * Helper functions to convert different types of strings to Uint8Array\n */\nfunction base64UrlToUint8Array(str) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onByte = (byte) => {\n        result.push(byte);\n    };\n    for (let i = 0; i < str.length; i += 1) {\n        byteFromBase64URL(str.charCodeAt(i), state, onByte);\n    }\n    return new Uint8Array(result);\n}\nfunction stringToUint8Array(str) {\n    const result = [];\n    stringToUTF8(str, (byte) => result.push(byte));\n    return new Uint8Array(result);\n}\nfunction bytesToBase64URL(bytes) {\n    const result = [];\n    const state = { queue: 0, queuedBits: 0 };\n    const onChar = (char) => {\n        result.push(char);\n    };\n    bytes.forEach((byte) => byteToBase64URL(byte, state, onChar));\n    // always call with `null` after processing all bytes\n    byteToBase64URL(null, state, onChar);\n    return result.join('');\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_VERSIONS: () => (/* binding */ API_VERSIONS),\n/* harmony export */   API_VERSION_HEADER_NAME: () => (/* binding */ API_VERSION_HEADER_NAME),\n/* harmony export */   AUDIENCE: () => (/* binding */ AUDIENCE),\n/* harmony export */   AUTO_REFRESH_TICK_DURATION_MS: () => (/* binding */ AUTO_REFRESH_TICK_DURATION_MS),\n/* harmony export */   AUTO_REFRESH_TICK_THRESHOLD: () => (/* binding */ AUTO_REFRESH_TICK_THRESHOLD),\n/* harmony export */   BASE64URL_REGEX: () => (/* binding */ BASE64URL_REGEX),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   EXPIRY_MARGIN_MS: () => (/* binding */ EXPIRY_MARGIN_MS),\n/* harmony export */   GOTRUE_URL: () => (/* binding */ GOTRUE_URL),\n/* harmony export */   JWKS_TTL: () => (/* binding */ JWKS_TTL),\n/* harmony export */   NETWORK_FAILURE: () => (/* binding */ NETWORK_FAILURE),\n/* harmony export */   STORAGE_KEY: () => (/* binding */ STORAGE_KEY)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js\");\n\n/** Current session will be checked for refresh at this interval. */\nconst AUTO_REFRESH_TICK_DURATION_MS = 30 * 1000;\n/**\n * A token refresh will be attempted this many ticks before the current session expires. */\nconst AUTO_REFRESH_TICK_THRESHOLD = 3;\n/*\n * Earliest time before an access token expires that the session should be refreshed.\n */\nconst EXPIRY_MARGIN_MS = AUTO_REFRESH_TICK_THRESHOLD * AUTO_REFRESH_TICK_DURATION_MS;\nconst GOTRUE_URL = 'http://localhost:9999';\nconst STORAGE_KEY = 'supabase.auth.token';\nconst AUDIENCE = '';\nconst DEFAULT_HEADERS = { 'X-Client-Info': `gotrue-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst NETWORK_FAILURE = {\n    MAX_RETRIES: 10,\n    RETRY_INTERVAL: 2, // in deciseconds\n};\nconst API_VERSION_HEADER_NAME = 'X-Supabase-Api-Version';\nconst API_VERSIONS = {\n    '2024-01-01': {\n        timestamp: Date.parse('2024-01-01T00:00:00.0Z'),\n        name: '2024-01-01',\n    },\n};\nconst BASE64URL_REGEX = /^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;\nconst JWKS_TTL = 600000; // 10 minutes\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthApiError: () => (/* binding */ AuthApiError),\n/* harmony export */   AuthError: () => (/* binding */ AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* binding */ AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* binding */ AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* binding */ AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* binding */ AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* binding */ AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* binding */ AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* binding */ AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* binding */ AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* binding */ AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* binding */ CustomAuthError),\n/* harmony export */   isAuthApiError: () => (/* binding */ isAuthApiError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* binding */ isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* binding */ isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* binding */ isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* binding */ isAuthWeakPasswordError)\n/* harmony export */ });\nclass AuthError extends Error {\n    constructor(message, status, code) {\n        super(message);\n        this.__isAuthError = true;\n        this.name = 'AuthError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthError(error) {\n    return typeof error === 'object' && error !== null && '__isAuthError' in error;\n}\nclass AuthApiError extends AuthError {\n    constructor(message, status, code) {\n        super(message, status, code);\n        this.name = 'AuthApiError';\n        this.status = status;\n        this.code = code;\n    }\n}\nfunction isAuthApiError(error) {\n    return isAuthError(error) && error.name === 'AuthApiError';\n}\nclass AuthUnknownError extends AuthError {\n    constructor(message, originalError) {\n        super(message);\n        this.name = 'AuthUnknownError';\n        this.originalError = originalError;\n    }\n}\nclass CustomAuthError extends AuthError {\n    constructor(message, name, status, code) {\n        super(message, status, code);\n        this.name = name;\n        this.status = status;\n    }\n}\nclass AuthSessionMissingError extends CustomAuthError {\n    constructor() {\n        super('Auth session missing!', 'AuthSessionMissingError', 400, undefined);\n    }\n}\nfunction isAuthSessionMissingError(error) {\n    return isAuthError(error) && error.name === 'AuthSessionMissingError';\n}\nclass AuthInvalidTokenResponseError extends CustomAuthError {\n    constructor() {\n        super('Auth session or user missing', 'AuthInvalidTokenResponseError', 500, undefined);\n    }\n}\nclass AuthInvalidCredentialsError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidCredentialsError', 400, undefined);\n    }\n}\nclass AuthImplicitGrantRedirectError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthImplicitGrantRedirectError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nfunction isAuthImplicitGrantRedirectError(error) {\n    return isAuthError(error) && error.name === 'AuthImplicitGrantRedirectError';\n}\nclass AuthPKCEGrantCodeExchangeError extends CustomAuthError {\n    constructor(message, details = null) {\n        super(message, 'AuthPKCEGrantCodeExchangeError', 500, undefined);\n        this.details = null;\n        this.details = details;\n    }\n    toJSON() {\n        return {\n            name: this.name,\n            message: this.message,\n            status: this.status,\n            details: this.details,\n        };\n    }\n}\nclass AuthRetryableFetchError extends CustomAuthError {\n    constructor(message, status) {\n        super(message, 'AuthRetryableFetchError', status, undefined);\n    }\n}\nfunction isAuthRetryableFetchError(error) {\n    return isAuthError(error) && error.name === 'AuthRetryableFetchError';\n}\n/**\n * This error is thrown on certain methods when the password used is deemed\n * weak. Inspect the reasons to identify what password strength rules are\n * inadequate.\n */\nclass AuthWeakPasswordError extends CustomAuthError {\n    constructor(message, status, reasons) {\n        super(message, 'AuthWeakPasswordError', status, 'weak_password');\n        this.reasons = reasons;\n    }\n}\nfunction isAuthWeakPasswordError(error) {\n    return isAuthError(error) && error.name === 'AuthWeakPasswordError';\n}\nclass AuthInvalidJwtError extends CustomAuthError {\n    constructor(message) {\n        super(message, 'AuthInvalidJwtError', 400, 'invalid_jwt');\n    }\n}\n//# sourceMappingURL=errors.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   _generateLinkResponse: () => (/* binding */ _generateLinkResponse),\n/* harmony export */   _noResolveJsonResponse: () => (/* binding */ _noResolveJsonResponse),\n/* harmony export */   _request: () => (/* binding */ _request),\n/* harmony export */   _sessionResponse: () => (/* binding */ _sessionResponse),\n/* harmony export */   _sessionResponsePassword: () => (/* binding */ _sessionResponsePassword),\n/* harmony export */   _ssoResponse: () => (/* binding */ _ssoResponse),\n/* harmony export */   _userResponse: () => (/* binding */ _userResponse),\n/* harmony export */   handleError: () => (/* binding */ handleError)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n\n\n\nconst _getErrorMessage = (err) => err.msg || err.message || err.error_description || err.error || JSON.stringify(err);\nconst NETWORK_ERROR_CODES = [502, 503, 504];\nasync function handleError(error) {\n    var _a;\n    if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_1__.looksLikeFetchResponse)(error)) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), 0);\n    }\n    if (NETWORK_ERROR_CODES.includes(error.status)) {\n        // status in 500...599 range - server had an error, request might be retryed.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(error), error.status);\n    }\n    let data;\n    try {\n        data = await error.json();\n    }\n    catch (e) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthUnknownError(_getErrorMessage(e), e);\n    }\n    let errorCode = undefined;\n    const responseAPIVersion = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.parseResponseAPIVersion)(error);\n    if (responseAPIVersion &&\n        responseAPIVersion.getTime() >= _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].timestamp &&\n        typeof data === 'object' &&\n        data &&\n        typeof data.code === 'string') {\n        errorCode = data.code;\n    }\n    else if (typeof data === 'object' && data && typeof data.error_code === 'string') {\n        errorCode = data.error_code;\n    }\n    if (!errorCode) {\n        // Legacy support for weak password errors, when there were no error codes\n        if (typeof data === 'object' &&\n            data &&\n            typeof data.weak_password === 'object' &&\n            data.weak_password &&\n            Array.isArray(data.weak_password.reasons) &&\n            data.weak_password.reasons.length &&\n            data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, data.weak_password.reasons);\n        }\n    }\n    else if (errorCode === 'weak_password') {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthWeakPasswordError(_getErrorMessage(data), error.status, ((_a = data.weak_password) === null || _a === void 0 ? void 0 : _a.reasons) || []);\n    }\n    else if (errorCode === 'session_not_found') {\n        // The `session_id` inside the JWT does not correspond to a row in the\n        // `sessions` table. This usually means the user has signed out, has been\n        // deleted, or their session has somehow been terminated.\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthSessionMissingError();\n    }\n    throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthApiError(_getErrorMessage(data), error.status || 500, errorCode);\n}\nconst _getRequestParams = (method, options, parameters, body) => {\n    const params = { method, headers: (options === null || options === void 0 ? void 0 : options.headers) || {} };\n    if (method === 'GET') {\n        return params;\n    }\n    params.headers = Object.assign({ 'Content-Type': 'application/json;charset=UTF-8' }, options === null || options === void 0 ? void 0 : options.headers);\n    params.body = JSON.stringify(body);\n    return Object.assign(Object.assign({}, params), parameters);\n};\nasync function _request(fetcher, method, url, options) {\n    var _a;\n    const headers = Object.assign({}, options === null || options === void 0 ? void 0 : options.headers);\n    if (!headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME]) {\n        headers[_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME] = _constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSIONS['2024-01-01'].name;\n    }\n    if (options === null || options === void 0 ? void 0 : options.jwt) {\n        headers['Authorization'] = `Bearer ${options.jwt}`;\n    }\n    const qs = (_a = options === null || options === void 0 ? void 0 : options.query) !== null && _a !== void 0 ? _a : {};\n    if (options === null || options === void 0 ? void 0 : options.redirectTo) {\n        qs['redirect_to'] = options.redirectTo;\n    }\n    const queryString = Object.keys(qs).length ? '?' + new URLSearchParams(qs).toString() : '';\n    const data = await _handleRequest(fetcher, method, url + queryString, {\n        headers,\n        noResolveJson: options === null || options === void 0 ? void 0 : options.noResolveJson,\n    }, {}, options === null || options === void 0 ? void 0 : options.body);\n    return (options === null || options === void 0 ? void 0 : options.xform) ? options === null || options === void 0 ? void 0 : options.xform(data) : { data: Object.assign({}, data), error: null };\n}\nasync function _handleRequest(fetcher, method, url, options, parameters, body) {\n    const requestParams = _getRequestParams(method, options, parameters, body);\n    let result;\n    try {\n        result = await fetcher(url, Object.assign({}, requestParams));\n    }\n    catch (e) {\n        console.error(e);\n        // fetch failed, likely due to a network or CORS error\n        throw new _errors__WEBPACK_IMPORTED_MODULE_2__.AuthRetryableFetchError(_getErrorMessage(e), 0);\n    }\n    if (!result.ok) {\n        await handleError(result);\n    }\n    if (options === null || options === void 0 ? void 0 : options.noResolveJson) {\n        return result;\n    }\n    try {\n        return await result.json();\n    }\n    catch (e) {\n        await handleError(e);\n    }\n}\nfunction _sessionResponse(data) {\n    var _a;\n    let session = null;\n    if (hasSession(data)) {\n        session = Object.assign({}, data);\n        if (!data.expires_at) {\n            session.expires_at = (0,_helpers__WEBPACK_IMPORTED_MODULE_1__.expiresAt)(data.expires_in);\n        }\n    }\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { session, user }, error: null };\n}\nfunction _sessionResponsePassword(data) {\n    const response = _sessionResponse(data);\n    if (!response.error &&\n        data.weak_password &&\n        typeof data.weak_password === 'object' &&\n        Array.isArray(data.weak_password.reasons) &&\n        data.weak_password.reasons.length &&\n        data.weak_password.message &&\n        typeof data.weak_password.message === 'string' &&\n        data.weak_password.reasons.reduce((a, i) => a && typeof i === 'string', true)) {\n        response.data.weak_password = data.weak_password;\n    }\n    return response;\n}\nfunction _userResponse(data) {\n    var _a;\n    const user = (_a = data.user) !== null && _a !== void 0 ? _a : data;\n    return { data: { user }, error: null };\n}\nfunction _ssoResponse(data) {\n    return { data, error: null };\n}\nfunction _generateLinkResponse(data) {\n    const { action_link, email_otp, hashed_token, redirect_to, verification_type } = data, rest = __rest(data, [\"action_link\", \"email_otp\", \"hashed_token\", \"redirect_to\", \"verification_type\"]);\n    const properties = {\n        action_link,\n        email_otp,\n        hashed_token,\n        redirect_to,\n        verification_type,\n    };\n    const user = Object.assign({}, rest);\n    return {\n        data: {\n            properties,\n            user,\n        },\n        error: null,\n    };\n}\nfunction _noResolveJsonResponse(data) {\n    return data;\n}\n/**\n * hasSession checks if the response object contains a valid session\n * @param data A response object\n * @returns true if a session is in the response\n */\nfunction hasSession(data) {\n    return data.access_token && data.refresh_token && data.expires_in;\n}\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Deferred: () => (/* binding */ Deferred),\n/* harmony export */   decodeJWT: () => (/* binding */ decodeJWT),\n/* harmony export */   expiresAt: () => (/* binding */ expiresAt),\n/* harmony export */   generatePKCEChallenge: () => (/* binding */ generatePKCEChallenge),\n/* harmony export */   generatePKCEVerifier: () => (/* binding */ generatePKCEVerifier),\n/* harmony export */   getAlgorithm: () => (/* binding */ getAlgorithm),\n/* harmony export */   getCodeChallengeAndMethod: () => (/* binding */ getCodeChallengeAndMethod),\n/* harmony export */   getItemAsync: () => (/* binding */ getItemAsync),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   looksLikeFetchResponse: () => (/* binding */ looksLikeFetchResponse),\n/* harmony export */   parseParametersFromURL: () => (/* binding */ parseParametersFromURL),\n/* harmony export */   parseResponseAPIVersion: () => (/* binding */ parseResponseAPIVersion),\n/* harmony export */   removeItemAsync: () => (/* binding */ removeItemAsync),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   retryable: () => (/* binding */ retryable),\n/* harmony export */   setItemAsync: () => (/* binding */ setItemAsync),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   supportsLocalStorage: () => (/* binding */ supportsLocalStorage),\n/* harmony export */   uuid: () => (/* binding */ uuid),\n/* harmony export */   validateExp: () => (/* binding */ validateExp),\n/* harmony export */   validateUUID: () => (/* binding */ validateUUID)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/constants.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./errors */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/errors.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./base64url */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/base64url.js\");\n\n\n\nfunction expiresAt(expiresIn) {\n    const timeNow = Math.round(Date.now() / 1000);\n    return timeNow + expiresIn;\n}\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        const r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nconst isBrowser = () => typeof window !== 'undefined' && typeof document !== 'undefined';\nconst localStorageWriteTests = {\n    tested: false,\n    writable: false,\n};\n/**\n * Checks whether localStorage is supported on this browser.\n */\nconst supportsLocalStorage = () => {\n    if (!isBrowser()) {\n        return false;\n    }\n    try {\n        if (typeof globalThis.localStorage !== 'object') {\n            return false;\n        }\n    }\n    catch (e) {\n        // DOM exception when accessing `localStorage`\n        return false;\n    }\n    if (localStorageWriteTests.tested) {\n        return localStorageWriteTests.writable;\n    }\n    const randomKey = `lswt-${Math.random()}${Math.random()}`;\n    try {\n        globalThis.localStorage.setItem(randomKey, randomKey);\n        globalThis.localStorage.removeItem(randomKey);\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = true;\n    }\n    catch (e) {\n        // localStorage can't be written to\n        // https://www.chromium.org/for-testers/bug-reporting-guidelines/uncaught-securityerror-failed-to-read-the-localstorage-property-from-window-access-is-denied-for-this-document\n        localStorageWriteTests.tested = true;\n        localStorageWriteTests.writable = false;\n    }\n    return localStorageWriteTests.writable;\n};\n/**\n * Extracts parameters encoded in the URL both in the query and fragment.\n */\nfunction parseParametersFromURL(href) {\n    const result = {};\n    const url = new URL(href);\n    if (url.hash && url.hash[0] === '#') {\n        try {\n            const hashSearchParams = new URLSearchParams(url.hash.substring(1));\n            hashSearchParams.forEach((value, key) => {\n                result[key] = value;\n            });\n        }\n        catch (e) {\n            // hash is not a query string\n        }\n    }\n    // search parameters take precedence over hash parameters\n    url.searchParams.forEach((value, key) => {\n        result[key] = value;\n    });\n    return result;\n}\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst looksLikeFetchResponse = (maybeResponse) => {\n    return (typeof maybeResponse === 'object' &&\n        maybeResponse !== null &&\n        'status' in maybeResponse &&\n        'ok' in maybeResponse &&\n        'json' in maybeResponse &&\n        typeof maybeResponse.json === 'function');\n};\n// Storage helpers\nconst setItemAsync = async (storage, key, data) => {\n    await storage.setItem(key, JSON.stringify(data));\n};\nconst getItemAsync = async (storage, key) => {\n    const value = await storage.getItem(key);\n    if (!value) {\n        return null;\n    }\n    try {\n        return JSON.parse(value);\n    }\n    catch (_a) {\n        return value;\n    }\n};\nconst removeItemAsync = async (storage, key) => {\n    await storage.removeItem(key);\n};\n/**\n * A deferred represents some asynchronous work that is not yet finished, which\n * may or may not culminate in a value.\n * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts\n */\nclass Deferred {\n    constructor() {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        this.promise = new Deferred.promiseConstructor((res, rej) => {\n            // eslint-disable-next-line @typescript-eslint/no-extra-semi\n            ;\n            this.resolve = res;\n            this.reject = rej;\n        });\n    }\n}\nDeferred.promiseConstructor = Promise;\nfunction decodeJWT(token) {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n        throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('Invalid JWT structure');\n    }\n    // Regex checks for base64url format\n    for (let i = 0; i < parts.length; i++) {\n        if (!_constants__WEBPACK_IMPORTED_MODULE_0__.BASE64URL_REGEX.test(parts[i])) {\n            throw new _errors__WEBPACK_IMPORTED_MODULE_1__.AuthInvalidJwtError('JWT not in base64url format');\n        }\n    }\n    const data = {\n        // using base64url lib\n        header: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[0])),\n        payload: JSON.parse((0,_base64url__WEBPACK_IMPORTED_MODULE_2__.stringFromBase64URL)(parts[1])),\n        signature: (0,_base64url__WEBPACK_IMPORTED_MODULE_2__.base64UrlToUint8Array)(parts[2]),\n        raw: {\n            header: parts[0],\n            payload: parts[1],\n        },\n    };\n    return data;\n}\n/**\n * Creates a promise that resolves to null after some time.\n */\nasync function sleep(time) {\n    return await new Promise((accept) => {\n        setTimeout(() => accept(null), time);\n    });\n}\n/**\n * Converts the provided async function into a retryable function. Each result\n * or thrown error is sent to the isRetryable function which should return true\n * if the function should run again.\n */\nfunction retryable(fn, isRetryable) {\n    const promise = new Promise((accept, reject) => {\n        // eslint-disable-next-line @typescript-eslint/no-extra-semi\n        ;\n        (async () => {\n            for (let attempt = 0; attempt < Infinity; attempt++) {\n                try {\n                    const result = await fn(attempt);\n                    if (!isRetryable(attempt, null, result)) {\n                        accept(result);\n                        return;\n                    }\n                }\n                catch (e) {\n                    if (!isRetryable(attempt, e)) {\n                        reject(e);\n                        return;\n                    }\n                }\n            }\n        })();\n    });\n    return promise;\n}\nfunction dec2hex(dec) {\n    return ('0' + dec.toString(16)).substr(-2);\n}\n// Functions below taken from: https://stackoverflow.com/questions/63309409/creating-a-code-verifier-and-challenge-for-pkce-auth-on-spotify-api-in-reactjs\nfunction generatePKCEVerifier() {\n    const verifierLength = 56;\n    const array = new Uint32Array(verifierLength);\n    if (typeof crypto === 'undefined') {\n        const charSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';\n        const charSetLen = charSet.length;\n        let verifier = '';\n        for (let i = 0; i < verifierLength; i++) {\n            verifier += charSet.charAt(Math.floor(Math.random() * charSetLen));\n        }\n        return verifier;\n    }\n    crypto.getRandomValues(array);\n    return Array.from(array, dec2hex).join('');\n}\nasync function sha256(randomString) {\n    const encoder = new TextEncoder();\n    const encodedData = encoder.encode(randomString);\n    const hash = await crypto.subtle.digest('SHA-256', encodedData);\n    const bytes = new Uint8Array(hash);\n    return Array.from(bytes)\n        .map((c) => String.fromCharCode(c))\n        .join('');\n}\nasync function generatePKCEChallenge(verifier) {\n    const hasCryptoSupport = typeof crypto !== 'undefined' &&\n        typeof crypto.subtle !== 'undefined' &&\n        typeof TextEncoder !== 'undefined';\n    if (!hasCryptoSupport) {\n        console.warn('WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256.');\n        return verifier;\n    }\n    const hashed = await sha256(verifier);\n    return btoa(hashed).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/=+$/, '');\n}\nasync function getCodeChallengeAndMethod(storage, storageKey, isPasswordRecovery = false) {\n    const codeVerifier = generatePKCEVerifier();\n    let storedCodeVerifier = codeVerifier;\n    if (isPasswordRecovery) {\n        storedCodeVerifier += '/PASSWORD_RECOVERY';\n    }\n    await setItemAsync(storage, `${storageKey}-code-verifier`, storedCodeVerifier);\n    const codeChallenge = await generatePKCEChallenge(codeVerifier);\n    const codeChallengeMethod = codeVerifier === codeChallenge ? 'plain' : 's256';\n    return [codeChallenge, codeChallengeMethod];\n}\n/** Parses the API version which is 2YYY-MM-DD. */\nconst API_VERSION_REGEX = /^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;\nfunction parseResponseAPIVersion(response) {\n    const apiVersion = response.headers.get(_constants__WEBPACK_IMPORTED_MODULE_0__.API_VERSION_HEADER_NAME);\n    if (!apiVersion) {\n        return null;\n    }\n    if (!apiVersion.match(API_VERSION_REGEX)) {\n        return null;\n    }\n    try {\n        const date = new Date(`${apiVersion}T00:00:00.0Z`);\n        return date;\n    }\n    catch (e) {\n        return null;\n    }\n}\nfunction validateExp(exp) {\n    if (!exp) {\n        throw new Error('Missing exp claim');\n    }\n    const timeNow = Math.floor(Date.now() / 1000);\n    if (exp <= timeNow) {\n        throw new Error('JWT has expired');\n    }\n}\nfunction getAlgorithm(alg) {\n    switch (alg) {\n        case 'RS256':\n            return {\n                name: 'RSASSA-PKCS1-v1_5',\n                hash: { name: 'SHA-256' },\n            };\n        case 'ES256':\n            return {\n                name: 'ECDSA',\n                namedCurve: 'P-256',\n                hash: { name: 'SHA-256' },\n            };\n        default:\n            throw new Error('Invalid alg claim');\n    }\n}\nconst UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;\nfunction validateUUID(str) {\n    if (!UUID_REGEX.test(str)) {\n        throw new Error('@supabase/auth-js: Expected parameter to be UUID but is not');\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js":
/*!*************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localStorageAdapter: () => (/* binding */ localStorageAdapter),\n/* harmony export */   memoryLocalStorageAdapter: () => (/* binding */ memoryLocalStorageAdapter)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * Provides safe access to the globalThis.localStorage property.\n */\nconst localStorageAdapter = {\n    getItem: (key) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return null;\n        }\n        return globalThis.localStorage.getItem(key);\n    },\n    setItem: (key, value) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return;\n        }\n        globalThis.localStorage.setItem(key, value);\n    },\n    removeItem: (key) => {\n        if (!(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)()) {\n            return;\n        }\n        globalThis.localStorage.removeItem(key);\n    },\n};\n/**\n * Returns a localStorage-like object that stores the key-value pairs in\n * memory.\n */\nfunction memoryLocalStorageAdapter(store = {}) {\n    return {\n        getItem: (key) => {\n            return store[key] || null;\n        },\n        setItem: (key, value) => {\n            store[key] = value;\n        },\n        removeItem: (key) => {\n            delete store[key];\n        },\n    };\n}\n//# sourceMappingURL=local-storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/local-storage.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LockAcquireTimeoutError: () => (/* binding */ LockAcquireTimeoutError),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* binding */ NavigatorLockAcquireTimeoutError),\n/* harmony export */   ProcessLockAcquireTimeoutError: () => (/* binding */ ProcessLockAcquireTimeoutError),\n/* harmony export */   internals: () => (/* binding */ internals),\n/* harmony export */   navigatorLock: () => (/* binding */ navigatorLock),\n/* harmony export */   processLock: () => (/* binding */ processLock)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/helpers.js\");\n\n/**\n * @experimental\n */\nconst internals = {\n    /**\n     * @experimental\n     */\n    debug: !!(globalThis &&\n        (0,_helpers__WEBPACK_IMPORTED_MODULE_0__.supportsLocalStorage)() &&\n        globalThis.localStorage &&\n        globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'),\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nclass LockAcquireTimeoutError extends Error {\n    constructor(message) {\n        super(message);\n        this.isAcquireTimeout = true;\n    }\n}\nclass NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\nclass ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {\n}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with Safari being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function navigatorLock(name, acquireTimeout, fn) {\n    if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n    }\n    const abortController = new globalThis.AbortController();\n    if (acquireTimeout > 0) {\n        setTimeout(() => {\n            abortController.abort();\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n            }\n        }, acquireTimeout);\n    }\n    // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n    // Wrapping navigator.locks.request() with a plain Promise is done as some\n    // libraries like zone.js patch the Promise object to track the execution\n    // context. However, it appears that most browsers use an internal promise\n    // implementation when using the navigator.locks.request() API causing them\n    // to lose context and emit confusing log messages or break certain features.\n    // This wrapping is believed to help zone.js track the execution context\n    // better.\n    return await Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n        }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n        }, async (lock) => {\n        if (lock) {\n            if (internals.debug) {\n                console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n            }\n            try {\n                return await fn();\n            }\n            finally {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n                }\n            }\n        }\n        else {\n            if (acquireTimeout === 0) {\n                if (internals.debug) {\n                    console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n                }\n                throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n            }\n            else {\n                if (internals.debug) {\n                    try {\n                        const result = await globalThis.navigator.locks.query();\n                        console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n                    }\n                    catch (e) {\n                        console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n                    }\n                }\n                // Browser is not following the Navigator LockManager spec, it\n                // returned a null lock when we didn't use ifAvailable. So we can\n                // pretend the lock is acquired in the name of backward compatibility\n                // and user experience and just run the function.\n                console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n                return await fn();\n            }\n        }\n    }));\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nasync function processLock(name, acquireTimeout, fn) {\n    var _a;\n    const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n    const currentOperation = Promise.race([\n        previousOperation.catch(() => {\n            // ignore error of previous operation that we're waiting to finish\n            return null;\n        }),\n        acquireTimeout >= 0\n            ? new Promise((_, reject) => {\n                setTimeout(() => {\n                    reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n                }, acquireTimeout);\n            })\n            : null,\n    ].filter((x) => x))\n        .catch((e) => {\n        if (e && e.isAcquireTimeout) {\n            throw e;\n        }\n        return null;\n    })\n        .then(async () => {\n        // previous operations finished and we didn't get a race on the acquire\n        // timeout, so the current operation can finally start\n        return await fn();\n    });\n    PROCESS_LOCKS[name] = currentOperation.catch(async (e) => {\n        if (e && e.isAcquireTimeout) {\n            // if the current operation timed out, it doesn't mean that the previous\n            // operation finished, so we need contnue waiting for it to finish\n            await previousOperation;\n            return null;\n        }\n        throw e;\n    });\n    // finally wait for the current operation to finish successfully, with an\n    // error or with an acquire timeout error\n    return await currentOperation;\n}\n//# sourceMappingURL=locks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/locks.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js":
/*!*********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   polyfillGlobalThis: () => (/* binding */ polyfillGlobalThis)\n/* harmony export */ });\n/**\n * https://mathiasbynens.be/notes/globalthis\n */\nfunction polyfillGlobalThis() {\n    if (typeof globalThis === 'object')\n        return;\n    try {\n        Object.defineProperty(Object.prototype, '__magic__', {\n            get: function () {\n                return this;\n            },\n            configurable: true,\n        });\n        // @ts-expect-error 'Allow access to magic'\n        __magic__.globalThis = __magic__;\n        // @ts-expect-error 'Allow access to magic'\n        delete Object.prototype.__magic__;\n    }\n    catch (e) {\n        if (typeof self !== 'undefined') {\n            // @ts-expect-error 'Allow access to globals'\n            self.globalThis = self;\n        }\n    }\n}\n//# sourceMappingURL=polyfills.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL3BvbHlmaWxscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcbGliXFxwb2x5ZmlsbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBodHRwczovL21hdGhpYXNieW5lbnMuYmUvbm90ZXMvZ2xvYmFsdGhpc1xuICovXG5leHBvcnQgZnVuY3Rpb24gcG9seWZpbGxHbG9iYWxUaGlzKCkge1xuICAgIGlmICh0eXBlb2YgZ2xvYmFsVGhpcyA9PT0gJ29iamVjdCcpXG4gICAgICAgIHJldHVybjtcbiAgICB0cnkge1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoT2JqZWN0LnByb3RvdHlwZSwgJ19fbWFnaWNfXycsIHtcbiAgICAgICAgICAgIGdldDogZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICAgICAgfSk7XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgJ0FsbG93IGFjY2VzcyB0byBtYWdpYydcbiAgICAgICAgX19tYWdpY19fLmdsb2JhbFRoaXMgPSBfX21hZ2ljX187XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgJ0FsbG93IGFjY2VzcyB0byBtYWdpYydcbiAgICAgICAgZGVsZXRlIE9iamVjdC5wcm90b3R5cGUuX19tYWdpY19fO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICBpZiAodHlwZW9mIHNlbGYgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yICdBbGxvdyBhY2Nlc3MgdG8gZ2xvYmFscydcbiAgICAgICAgICAgIHNlbGYuZ2xvYmFsVGhpcyA9IHNlbGY7XG4gICAgICAgIH1cbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wb2x5ZmlsbHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/polyfills.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js":
/*!*****************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* binding */ SIGN_OUT_SCOPES)\n/* harmony export */ });\nconst SIGN_OUT_SCOPES = ['global', 'local', 'others'];\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlZHBhbmRhXFxEZXNrdG9wXFxNZXRhbW9ycGhpYyBmbHV4XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrYXV0aC1qc0AyLjcwLjBcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxhdXRoLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcdHlwZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFNJR05fT1VUX1NDT1BFUyA9IFsnZ2xvYmFsJywgJ2xvY2FsJywgJ290aGVycyddO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHlwZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/types.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.70.0';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZSthdXRoLWpzQDIuNzAuMC9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL2F1dGgtanMvZGlzdC9tb2R1bGUvbGliL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZSthdXRoLWpzQDIuNzAuMFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXGF1dGgtanNcXGRpc3RcXG1vZHVsZVxcbGliXFx2ZXJzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB2ZXJzaW9uID0gJzIuNzAuMCc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/lib/version.js\n");

/***/ })

};
;