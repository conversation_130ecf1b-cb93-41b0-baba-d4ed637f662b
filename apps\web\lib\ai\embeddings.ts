import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);

export interface EmbeddingResult {
  embedding: number[];
  text: string;
  metadata?: Record<string, any>;
}

export interface SimilarityResult {
  id: string;
  similarity: number;
  text: string;
  metadata?: Record<string, any>;
}

/**
 * Generate embeddings using Gemini Flash
 */
export async function generateEmbedding(text: string): Promise<number[]> {
  try {
    // Use Gemini's embedding model
    const model = genAI.getGenerativeModel({ model: 'embedding-001' });
    
    const result = await model.embedContent(text);
    
    if (!result.embedding?.values) {
      throw new Error('No embedding values returned from Gemini');
    }
    
    return result.embedding.values;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw new Error(`Failed to generate embedding: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate embeddings for multiple texts
 */
export async function generateEmbeddings(texts: string[]): Promise<EmbeddingResult[]> {
  try {
    const embeddings = await Promise.all(
      texts.map(async (text) => {
        const embedding = await generateEmbedding(text);
        return { embedding, text };
      })
    );
    
    return embeddings;
  } catch (error) {
    console.error('Error generating embeddings:', error);
    throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Calculate cosine similarity between two vectors
 */
export function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length');
  }
  
  let dotProduct = 0;
  let normA = 0;
  let normB = 0;
  
  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i];
    normA += a[i] * a[i];
    normB += b[i] * b[i];
  }
  
  if (normA === 0 || normB === 0) {
    return 0;
  }
  
  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

/**
 * Generate persona embedding from demographic and psychographic data
 */
export async function generatePersonaEmbedding(persona: {
  name: string;
  description?: string;
  demographics?: Record<string, any>;
  psychographics?: Record<string, any>;
  behaviors?: Record<string, any>;
  interests?: string[];
}): Promise<number[]> {
  // Create a comprehensive text representation of the persona
  const personaText = [
    `Persona: ${persona.name}`,
    persona.description ? `Description: ${persona.description}` : '',
    persona.demographics ? `Demographics: ${JSON.stringify(persona.demographics)}` : '',
    persona.psychographics ? `Psychographics: ${JSON.stringify(persona.psychographics)}` : '',
    persona.behaviors ? `Behaviors: ${JSON.stringify(persona.behaviors)}` : '',
    persona.interests ? `Interests: ${persona.interests.join(', ')}` : '',
  ]
    .filter(Boolean)
    .join('\n');
  
  return generateEmbedding(personaText);
}

/**
 * Generate creative embedding from content and metadata
 */
export async function generateCreativeEmbedding(creative: {
  name: string;
  type: string;
  copy_headline?: string;
  copy_description?: string;
  copy_text?: string;
  metadata?: Record<string, any>;
}): Promise<number[]> {
  // Create a comprehensive text representation of the creative
  const creativeText = [
    `Creative: ${creative.name}`,
    `Type: ${creative.type}`,
    creative.copy_headline ? `Headline: ${creative.copy_headline}` : '',
    creative.copy_description ? `Description: ${creative.copy_description}` : '',
    creative.copy_text ? `Copy: ${creative.copy_text}` : '',
    creative.metadata ? `Metadata: ${JSON.stringify(creative.metadata)}` : '',
  ]
    .filter(Boolean)
    .join('\n');
  
  return generateEmbedding(creativeText);
}

/**
 * Find similar personas using vector similarity
 */
export async function findSimilarPersonas(
  queryEmbedding: number[],
  personas: Array<{ id: string; embedding: number[]; name: string; description?: string }>,
  limit: number = 5
): Promise<SimilarityResult[]> {
  const similarities = personas
    .map((persona) => ({
      id: persona.id,
      similarity: cosineSimilarity(queryEmbedding, persona.embedding),
      text: persona.name,
      metadata: { description: persona.description },
    }))
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit);
  
  return similarities;
}

/**
 * Find similar creatives using vector similarity
 */
export async function findSimilarCreatives(
  queryEmbedding: number[],
  creatives: Array<{ 
    id: string; 
    embedding: number[]; 
    name: string; 
    copy_headline?: string;
    type: string;
  }>,
  limit: number = 5
): Promise<SimilarityResult[]> {
  const similarities = creatives
    .map((creative) => ({
      id: creative.id,
      similarity: cosineSimilarity(queryEmbedding, creative.embedding),
      text: creative.name,
      metadata: { 
        headline: creative.copy_headline,
        type: creative.type,
      },
    }))
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, limit);
  
  return similarities;
}

/**
 * Generate campaign insights using embeddings
 */
export async function generateCampaignInsights(
  campaignData: {
    name: string;
    description?: string;
    objectives?: Record<string, any>;
    personas: Array<{ name: string; embedding: number[] }>;
    creatives: Array<{ name: string; embedding: number[]; performance?: Record<string, any> }>;
  }
): Promise<{
  personaAlignment: number;
  creativeConsistency: number;
  recommendations: string[];
}> {
  try {
    // Calculate persona alignment (how well personas align with each other)
    let personaAlignment = 0;
    if (campaignData.personas.length > 1) {
      const similarities: number[] = [];
      for (let i = 0; i < campaignData.personas.length; i++) {
        for (let j = i + 1; j < campaignData.personas.length; j++) {
          similarities.push(
            cosineSimilarity(
              campaignData.personas[i].embedding,
              campaignData.personas[j].embedding
            )
          );
        }
      }
      personaAlignment = similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length;
    }
    
    // Calculate creative consistency (how well creatives align with personas)
    let creativeConsistency = 0;
    if (campaignData.personas.length > 0 && campaignData.creatives.length > 0) {
      const similarities: number[] = [];
      campaignData.personas.forEach((persona) => {
        campaignData.creatives.forEach((creative) => {
          similarities.push(cosineSimilarity(persona.embedding, creative.embedding));
        });
      });
      creativeConsistency = similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length;
    }
    
    // Generate recommendations based on analysis
    const recommendations: string[] = [];
    
    if (personaAlignment < 0.7) {
      recommendations.push('Consider refining your target personas for better alignment');
    }
    
    if (creativeConsistency < 0.6) {
      recommendations.push('Your creatives may not be well-aligned with your target personas');
    }
    
    if (campaignData.creatives.length < 3) {
      recommendations.push('Consider creating more creative variants for better A/B testing');
    }
    
    return {
      personaAlignment,
      creativeConsistency,
      recommendations,
    };
  } catch (error) {
    console.error('Error generating campaign insights:', error);
    throw new Error(`Failed to generate insights: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
