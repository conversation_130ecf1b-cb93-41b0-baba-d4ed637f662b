{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "module": "ESNext", "target": "ES2022", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/types/*": ["./src/types/*"], "@/agents/*": ["./src/agents/*"], "@/utils/*": ["./src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}