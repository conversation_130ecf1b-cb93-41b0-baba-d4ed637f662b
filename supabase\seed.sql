-- Seed data for development
-- Note: This will only run in development environment

-- Insert sample organization
INSERT INTO public.organizations (id, name, slug, description, website_url) VALUES
  ('550e8400-e29b-41d4-a716-446655440000', 'Metamorphic Flux Demo', 'metamorphic-flux-demo', 'Demo organization for testing Metamorphic Flux features', 'https://metamorphic-flux.com');

-- Insert sample personas with embeddings (using random vectors for demo)
INSERT INTO public.personas (id, organization_id, name, description, demographics, psychographics, behaviors, interests, embedding) VALUES
  (
    '550e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440000',
    'Tech-Savvy Millennials',
    'Young professionals interested in cutting-edge technology and digital innovation',
    '{"age_range": "25-35", "income": "50k-100k", "education": "college", "location": "urban"}',
    '{"values": ["innovation", "efficiency", "sustainability"], "personality": "early_adopter"}',
    '{"online_time": "high", "social_media_usage": "daily", "shopping_behavior": "research_heavy"}',
    ARRAY['technology', 'startups', 'AI', 'productivity', 'sustainability'],
    ARRAY(SELECT random() FROM generate_series(1, 1536))::vector
  ),
  (
    '550e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440000',
    'Creative Professionals',
    'Designers, marketers, and content creators looking for innovative tools',
    '{"age_range": "28-45", "income": "40k-120k", "education": "college", "location": "mixed"}',
    '{"values": ["creativity", "self_expression", "quality"], "personality": "creative_explorer"}',
    '{"online_time": "high", "social_media_usage": "professional", "shopping_behavior": "brand_conscious"}',
    ARRAY['design', 'marketing', 'creativity', 'tools', 'inspiration'],
    ARRAY(SELECT random() FROM generate_series(1, 1536))::vector
  ),
  (
    '550e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440000',
    'Enterprise Decision Makers',
    'C-level executives and senior managers evaluating marketing automation solutions',
    '{"age_range": "35-55", "income": "100k+", "education": "graduate", "location": "major_cities"}',
    '{"values": ["efficiency", "ROI", "scalability"], "personality": "analytical_leader"}',
    '{"online_time": "moderate", "social_media_usage": "professional", "shopping_behavior": "ROI_focused"}',
    ARRAY['business', 'automation', 'ROI', 'scalability', 'leadership'],
    ARRAY(SELECT random() FROM generate_series(1, 1536))::vector
  );

-- Insert sample campaign
INSERT INTO public.campaigns (id, organization_id, name, description, status, objectives, target_personas, budget_total, budget_daily, channels, start_date, end_date) VALUES
  (
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440000',
    'AI Marketing Platform Launch',
    'Launch campaign for Metamorphic Flux AI marketing platform targeting early adopters',
    'active',
    '{"primary": "brand_awareness", "secondary": "lead_generation", "kpis": ["CTR", "conversion_rate", "cost_per_lead"]}',
    ARRAY['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002'],
    50000.00,
    1000.00,
    ARRAY['facebook', 'google', 'linkedin']::channel_type[],
    NOW(),
    NOW() + INTERVAL '30 days'
  );

-- Insert sample creatives
INSERT INTO public.creatives (id, campaign_id, name, type, copy_headline, copy_description, copy_cta, metadata, embedding) VALUES
  (
    '550e8400-e29b-41d4-a716-446655440020',
    '550e8400-e29b-41d4-a716-446655440010',
    'Hero Image - AI Revolution',
    'image',
    'Transform Your Marketing with AI',
    'Discover how Metamorphic Flux uses cutting-edge AI to optimize your campaigns in real-time. Join the marketing revolution.',
    'Start Free Trial',
    '{"dimensions": {"width": 1200, "height": 628}, "format": "jpg", "color_scheme": "blue_gradient"}',
    ARRAY(SELECT random() FROM generate_series(1, 1536))::vector
  ),
  (
    '550e8400-e29b-41d4-a716-446655440021',
    '550e8400-e29b-41d4-a716-446655440010',
    'Video Demo - Platform Overview',
    'video',
    'See Metamorphic Flux in Action',
    'Watch how our AI agents work together to create, optimize, and scale your marketing campaigns automatically.',
    'Watch Demo',
    '{"duration": 60, "format": "mp4", "resolution": "1080p", "style": "animated"}',
    ARRAY(SELECT random() FROM generate_series(1, 1536))::vector
  ),
  (
    '550e8400-e29b-41d4-a716-446655440022',
    '550e8400-e29b-41d4-a716-446655440010',
    'Carousel - Feature Highlights',
    'carousel',
    'AI-Powered Marketing Features',
    'Explore real-time optimization, quantum budget allocation, and multi-agent creative generation.',
    'Learn More',
    '{"slides": 5, "format": "jpg", "dimensions": {"width": 1080, "height": 1080}}',
    ARRAY(SELECT random() FROM generate_series(1, 1536))::vector
  );

-- Insert sample campaign logs
INSERT INTO public.campaign_logs (campaign_id, creative_id, channel, event_type, event_data, metrics) VALUES
  (
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440020',
    'facebook',
    'impression',
    '{"placement": "feed", "audience_segment": "tech_millennials"}',
    '{"cost": 0.15, "reach": 1, "frequency": 1}'
  ),
  (
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440020',
    'facebook',
    'click',
    '{"placement": "feed", "audience_segment": "tech_millennials", "click_position": "cta_button"}',
    '{"cost": 1.25, "ctr": 0.08}'
  ),
  (
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440021',
    'google',
    'view',
    '{"ad_format": "video", "view_duration": 45}',
    '{"cost": 0.05, "view_rate": 0.75}'
  );

-- Insert sample optimization job
INSERT INTO public.optimization_jobs (id, campaign_id, type, status, input_data, output_data) VALUES
  (
    '550e8400-e29b-41d4-a716-446655440030',
    '550e8400-e29b-41d4-a716-446655440010',
    'budget',
    'completed',
    '{"current_allocation": {"facebook": 0.4, "google": 0.4, "linkedin": 0.2}, "performance_data": {"facebook": {"ctr": 0.08, "cpc": 1.25}, "google": {"ctr": 0.06, "cpc": 0.95}, "linkedin": {"ctr": 0.12, "cpc": 2.50}}}',
    '{"recommended_allocation": {"facebook": 0.35, "google": 0.45, "linkedin": 0.20}, "expected_improvement": 0.15, "confidence": 0.87}'
  );

-- Insert sample agent logs
INSERT INTO public.agent_logs (agent_type, campaign_id, creative_id, execution_id, status, input_data, output_data, duration_ms) VALUES
  (
    'copy',
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440020',
    'exec_001',
    'completed',
    '{"persona": "tech_millennials", "objective": "brand_awareness", "tone": "innovative"}',
    '{"headline": "Transform Your Marketing with AI", "description": "Discover how Metamorphic Flux uses cutting-edge AI...", "cta": "Start Free Trial", "confidence": 0.92}',
    1250
  ),
  (
    'design',
    '550e8400-e29b-41d4-a716-446655440010',
    '550e8400-e29b-41d4-a716-446655440020',
    'exec_002',
    'completed',
    '{"copy": "Transform Your Marketing with AI", "style": "modern", "color_scheme": "blue_gradient"}',
    '{"image_url": "/generated/hero_ai_revolution.jpg", "dimensions": {"width": 1200, "height": 628}, "confidence": 0.89}',
    3500
  ),
  (
    'analyst',
    '550e8400-e29b-41d4-a716-446655440010',
    NULL,
    'exec_003',
    'completed',
    '{"timeframe": "24h", "metrics": ["ctr", "cpc", "conversion_rate"]}',
    '{"insights": ["Facebook CTR above average", "Google CPC trending down", "LinkedIn showing high engagement"], "recommendations": ["Increase Google budget", "Test new creative variants"], "confidence": 0.85}',
    2100
  );
