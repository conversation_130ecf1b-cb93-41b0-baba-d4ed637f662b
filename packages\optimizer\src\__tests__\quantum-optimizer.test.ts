import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { QuantumOptimizer } from '../quantum/optimizer';
import { ClassicalSolver } from '../classical/solver';
import { WillowBridge } from '../quantum/willow-bridge';
import {
  OptimizationRequest,
  WillowConfig,
  ChannelConfig,
  BudgetConstraint,
  OptimizationObjective,
} from '../types';

// Mock WillowBridge
vi.mock('../quantum/willow-bridge');

describe('QuantumOptimizer', () => {
  let optimizer: QuantumOptimizer;
  let mockWillowBridge: any;
  let testConfig: WillowConfig;
  let testRequest: OptimizationRequest;

  beforeEach(() => {
    testConfig = {
      endpoint: 'https://test-quantum.googleapis.com',
      apiKey: 'test-api-key',
      projectId: 'test-project',
      region: 'us-central1',
      timeout: 30000,
      retries: 3,
      circuitConfig: {
        qubits: 20,
        depth: 100,
        gateSet: ['H', 'X', 'Y', 'Z', 'CNOT'],
        errorCorrection: true,
        coherenceTime: 100,
        fidelity: 0.999,
      },
    };

    // Mock WillowBridge methods
    mockWillowBridge = {
      getProcessorStatus: vi.fn().mockResolvedValue({
        available: true,
        qubits: 105,
        coherenceTime: 100,
        errorRate: 0.001,
        queueLength: 2,
        estimatedWaitTime: 5000,
      }),
      createOptimizationCircuit: vi.fn().mockResolvedValue('test-circuit-id'),
      executeOptimization: vi.fn().mockResolvedValue({
        result: [0.3, 0.4, 0.2, 0.1],
        confidence: 0.95,
        quantumAdvantage: 0.8,
        metrics: {
          gateCount: 150,
          depth: 50,
          errorRate: 0.001,
          coherenceTime: 100,
          executionTime: 2500,
        },
      }),
      cleanup: vi.fn().mockResolvedValue(undefined),
    };

    (WillowBridge as any).mockImplementation(() => mockWillowBridge);

    optimizer = new QuantumOptimizer(testConfig);

    // Test optimization request
    const channels: ChannelConfig[] = [
      {
        id: 'facebook',
        name: 'Facebook Ads',
        type: 'facebook',
        minBudget: 1000,
        maxBudget: 50000,
        costModel: 'cpc',
        baseRate: 2.5,
        scalingFactor: 1.0,
        conversionRate: 0.03,
        averageOrderValue: 75,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 6,
      },
      {
        id: 'google',
        name: 'Google Ads',
        type: 'google',
        minBudget: 2000,
        maxBudget: 60000,
        costModel: 'cpc',
        baseRate: 3.0,
        scalingFactor: 1.1,
        conversionRate: 0.04,
        averageOrderValue: 85,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 7,
      },
      {
        id: 'linkedin',
        name: 'LinkedIn Ads',
        type: 'linkedin',
        minBudget: 500,
        maxBudget: 20000,
        costModel: 'cpm',
        baseRate: 15.0,
        scalingFactor: 0.9,
        conversionRate: 0.02,
        averageOrderValue: 120,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 5,
      },
      {
        id: 'email',
        name: 'Email Marketing',
        type: 'email',
        minBudget: 100,
        maxBudget: 5000,
        costModel: 'flat',
        baseRate: 0.1,
        scalingFactor: 1.2,
        conversionRate: 0.05,
        averageOrderValue: 65,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 3,
      },
    ];

    const constraints: BudgetConstraint[] = [
      {
        id: 'total_budget',
        type: 'total_budget',
        value: 100000,
        operator: '<=',
        priority: 10,
        flexible: false,
        tolerance: 0,
      },
      {
        id: 'min_roas',
        type: 'roas_target',
        value: 3.0,
        operator: '>=',
        priority: 8,
        flexible: true,
        tolerance: 0.1,
      },
    ];

    const objective: OptimizationObjective = {
      primary: 'maximize_revenue',
      secondary: 'maximize_reach',
      weights: { primary: 0.8, secondary: 0.2 },
      timeHorizon: 'monthly',
    };

    testRequest = {
      id: 'test-request-1',
      totalBudget: 100000,
      channels,
      constraints,
      objective,
      timeframe: {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31'),
      },
      preferences: {
        useQuantumOptimization: true,
        quantumFallbackThreshold: 0.95,
        maxIterations: 1000,
        convergenceThreshold: 0.001,
        riskTolerance: 'moderate',
      },
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Quantum Optimization', () => {
    it('should successfully perform quantum optimization', async () => {
      const result = await optimizer.optimize(testRequest);

      expect(result).toBeDefined();
      expect(result.method).toBe('quantum');
      expect(result.status).toBe('success');
      expect(result.allocations).toHaveLength(4);
      expect(result.confidence).toBeGreaterThan(0.9);
      expect(result.quantumMetrics).toBeDefined();
      expect(result.quantumMetrics?.quantumAdvantage).toBe(0.8);

      // Verify Willow bridge was called correctly
      expect(mockWillowBridge.getProcessorStatus).toHaveBeenCalled();
      expect(mockWillowBridge.createOptimizationCircuit).toHaveBeenCalledWith(
        testRequest,
        testConfig.circuitConfig
      );
      expect(mockWillowBridge.executeOptimization).toHaveBeenCalled();
    });

    it('should handle quantum processor unavailability', async () => {
      mockWillowBridge.getProcessorStatus.mockResolvedValue({
        available: false,
        qubits: 0,
        coherenceTime: 0,
        errorRate: 1,
        queueLength: 100,
        estimatedWaitTime: 300000,
      });

      const result = await optimizer.optimize(testRequest);

      expect(result.method).toBe('classical');
      expect(result.status).toBe('partial');
    });

    it('should fall back to classical when quantum fails', async () => {
      mockWillowBridge.executeOptimization.mockRejectedValue(
        new Error('Quantum execution failed')
      );

      const result = await optimizer.optimize(testRequest);

      expect(result.method).toBe('classical');
      expect(result.recommendations).toContainEqual(
        expect.objectContaining({
          description: expect.stringContaining('Quantum optimization failed'),
        })
      );
    });

    it('should select quantum strategy for suitable problems', async () => {
      // Large problem with complex constraints should prefer quantum
      const largeRequest = {
        ...testRequest,
        channels: Array(25).fill(null).map((_, i) => ({
          ...testRequest.channels[0],
          id: `channel_${i}`,
        })),
        constraints: [
          ...testRequest.constraints,
          {
            id: 'complex_constraint',
            type: 'roas_target',
            value: 4.0,
            operator: '>=',
            priority: 9,
            flexible: true,
            tolerance: 0.05,
          },
        ],
      };

      const result = await optimizer.optimize(largeRequest);
      expect(result.method).toBe('quantum');
    });

    it('should handle quantum circuit creation errors', async () => {
      mockWillowBridge.createOptimizationCircuit.mockRejectedValue(
        new Error('Circuit creation failed')
      );

      const result = await optimizer.optimize(testRequest);
      expect(result.method).toBe('classical');
    });
  });

  describe('Hybrid Optimization', () => {
    it('should perform hybrid optimization when appropriate', async () => {
      // Modify request to trigger hybrid strategy
      const hybridRequest = {
        ...testRequest,
        channels: testRequest.channels.slice(0, 3), // Medium-sized problem
        preferences: {
          ...testRequest.preferences,
          riskTolerance: 'conservative' as const,
        },
      };

      // Mock quantum result that would trigger classical refinement
      mockWillowBridge.executeOptimization.mockResolvedValue({
        result: [0.4, 0.4, 0.2],
        confidence: 0.75, // Lower confidence triggers refinement
        quantumAdvantage: 0.6,
        metrics: {
          gateCount: 100,
          depth: 30,
          errorRate: 0.005,
          coherenceTime: 80,
          executionTime: 1500,
        },
      });

      const result = await optimizer.optimize(hybridRequest);
      expect(result.method).toBe('hybrid');
    });
  });

  describe('Algorithm Selection', () => {
    it('should select classical for small problems', async () => {
      const smallRequest = {
        ...testRequest,
        channels: testRequest.channels.slice(0, 2),
        constraints: testRequest.constraints.slice(0, 1),
      };

      const result = await optimizer.optimize(smallRequest);
      expect(result.method).toBe('classical');
    });

    it('should respect user preference to disable quantum', async () => {
      const classicalOnlyRequest = {
        ...testRequest,
        preferences: {
          ...testRequest.preferences,
          useQuantumOptimization: false,
        },
      };

      const result = await optimizer.optimize(classicalOnlyRequest);
      expect(result.method).toBe('classical');
    });
  });

  describe('Performance Monitoring', () => {
    it('should track performance history', async () => {
      // Run multiple optimizations
      await optimizer.optimize(testRequest);
      await optimizer.optimize(testRequest);
      await optimizer.optimize(testRequest);

      // Performance history should be updated (internal state)
      // This would be tested through behavior changes in algorithm selection
      expect(mockWillowBridge.executeOptimization).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      mockWillowBridge.getProcessorStatus.mockRejectedValue(
        new Error('Network timeout')
      );

      const result = await optimizer.optimize(testRequest);
      expect(result.method).toBe('classical');
      expect(result.status).toBe('partial');
    });

    it('should handle invalid quantum results', async () => {
      mockWillowBridge.executeOptimization.mockResolvedValue({
        result: [], // Invalid empty result
        confidence: 0.5,
        quantumAdvantage: 0,
        metrics: {
          gateCount: 0,
          depth: 0,
          errorRate: 1,
          coherenceTime: 0,
          executionTime: 0,
        },
      });

      const result = await optimizer.optimize(testRequest);
      expect(result.method).toBe('classical');
    });
  });

  describe('Budget Allocation Validation', () => {
    it('should ensure allocations respect channel constraints', async () => {
      const result = await optimizer.optimize(testRequest);

      result.allocations.forEach((allocation, i) => {
        const channel = testRequest.channels[i];
        expect(allocation.allocatedBudget).toBeGreaterThanOrEqual(channel.minBudget);
        expect(allocation.allocatedBudget).toBeLessThanOrEqual(channel.maxBudget);
      });
    });

    it('should ensure total allocation does not exceed budget', async () => {
      const result = await optimizer.optimize(testRequest);
      
      const totalAllocated = result.allocations.reduce(
        (sum, allocation) => sum + allocation.allocatedBudget,
        0
      );
      
      expect(totalAllocated).toBeLessThanOrEqual(testRequest.totalBudget * 1.01); // Small tolerance
    });

    it('should calculate realistic performance metrics', async () => {
      const result = await optimizer.optimize(testRequest);

      result.allocations.forEach(allocation => {
        expect(allocation.expectedConversions).toBeGreaterThan(0);
        expect(allocation.expectedRevenue).toBeGreaterThan(0);
        expect(allocation.expectedCPA).toBeGreaterThan(0);
        expect(allocation.expectedROAS).toBeGreaterThan(0);
        expect(allocation.confidence).toBeGreaterThan(0);
        expect(allocation.confidence).toBeLessThanOrEqual(1);
        expect(allocation.riskScore).toBeGreaterThanOrEqual(1);
        expect(allocation.riskScore).toBeLessThanOrEqual(10);
      });
    });
  });

  describe('Quantum Metrics', () => {
    it('should provide meaningful quantum metrics', async () => {
      const result = await optimizer.optimize(testRequest);

      if (result.method === 'quantum' && result.quantumMetrics) {
        expect(result.quantumMetrics.quantumAdvantage).toBeGreaterThan(0);
        expect(result.quantumMetrics.quantumAdvantage).toBeLessThanOrEqual(1);
        expect(result.quantumMetrics.coherenceTime).toBeGreaterThan(0);
        expect(result.quantumMetrics.gateCount).toBeGreaterThan(0);
        expect(result.quantumMetrics.errorRate).toBeGreaterThanOrEqual(0);
        expect(result.quantumMetrics.errorRate).toBeLessThan(1);
        expect(result.quantumMetrics.willowChipUtilization).toBeGreaterThan(0);
        expect(result.quantumMetrics.willowChipUtilization).toBeLessThanOrEqual(1);
      }
    });
  });

  describe('Recommendations', () => {
    it('should generate relevant recommendations', async () => {
      const result = await optimizer.optimize(testRequest);

      expect(result.recommendations).toBeDefined();
      expect(Array.isArray(result.recommendations)).toBe(true);

      if (result.recommendations.length > 0) {
        result.recommendations.forEach(rec => {
          expect(rec.type).toBeDefined();
          expect(rec.description).toBeDefined();
          expect(rec.impact).toBeDefined();
          expect(rec.priority).toBeDefined();
          expect(['low', 'medium', 'high']).toContain(rec.priority);
        });
      }
    });

    it('should generate quantum-specific recommendations for quantum results', async () => {
      const result = await optimizer.optimize(testRequest);

      if (result.method === 'quantum') {
        const quantumRecs = result.recommendations.filter(rec =>
          rec.description.toLowerCase().includes('quantum')
        );
        expect(quantumRecs.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources properly', async () => {
      await optimizer.cleanup();
      expect(mockWillowBridge.cleanup).toHaveBeenCalled();
    });
  });
});

// Additional integration tests
describe('QuantumOptimizer Integration', () => {
  it('should handle real-world optimization scenarios', async () => {
    const config: WillowConfig = {
      endpoint: 'https://test-quantum.googleapis.com',
      apiKey: 'test-key',
      projectId: 'test-project',
      region: 'us-central1',
      timeout: 30000,
      retries: 3,
      circuitConfig: {
        qubits: 50,
        depth: 200,
        gateSet: ['H', 'X', 'Y', 'Z', 'CNOT', 'CZ', 'RX', 'RY', 'RZ'],
        errorCorrection: true,
        coherenceTime: 100,
        fidelity: 0.999,
      },
    };

    // Mock successful quantum optimization
    const mockBridge = {
      getProcessorStatus: vi.fn().mockResolvedValue({
        available: true,
        qubits: 105,
        coherenceTime: 100,
        errorRate: 0.001,
        queueLength: 1,
        estimatedWaitTime: 2000,
      }),
      createOptimizationCircuit: vi.fn().mockResolvedValue('circuit-123'),
      executeOptimization: vi.fn().mockResolvedValue({
        result: [0.25, 0.35, 0.25, 0.15],
        confidence: 0.92,
        quantumAdvantage: 0.85,
        metrics: {
          gateCount: 250,
          depth: 75,
          errorRate: 0.0008,
          coherenceTime: 105,
          executionTime: 3200,
        },
      }),
      cleanup: vi.fn(),
    };

    (WillowBridge as any).mockImplementation(() => mockBridge);

    const optimizer = new QuantumOptimizer(config);

    // Real-world e-commerce scenario
    const channels: ChannelConfig[] = [
      {
        id: 'google_search',
        name: 'Google Search Ads',
        type: 'google',
        minBudget: 5000,
        maxBudget: 100000,
        costModel: 'cpc',
        baseRate: 2.8,
        scalingFactor: 1.1,
        saturationPoint: 75000,
        conversionRate: 0.045,
        averageOrderValue: 95,
        seasonality: [0.9, 0.9, 1.0, 1.1, 1.2, 1.1, 0.8, 0.8, 1.0, 1.1, 1.3, 1.4],
        competitiveIndex: 8,
      },
      {
        id: 'facebook_ads',
        name: 'Facebook & Instagram Ads',
        type: 'facebook',
        minBudget: 3000,
        maxBudget: 80000,
        costModel: 'cpm',
        baseRate: 12.5,
        scalingFactor: 1.0,
        saturationPoint: 60000,
        conversionRate: 0.035,
        averageOrderValue: 78,
        seasonality: [1.0, 1.0, 1.1, 1.1, 1.2, 1.0, 0.9, 0.9, 1.0, 1.1, 1.2, 1.3],
        competitiveIndex: 7,
      },
      {
        id: 'linkedin_ads',
        name: 'LinkedIn Sponsored Content',
        type: 'linkedin',
        minBudget: 2000,
        maxBudget: 40000,
        costModel: 'cpc',
        baseRate: 5.2,
        scalingFactor: 0.9,
        saturationPoint: 25000,
        conversionRate: 0.025,
        averageOrderValue: 145,
        seasonality: [1.1, 1.1, 1.0, 1.0, 0.9, 0.8, 0.7, 0.8, 1.0, 1.1, 1.0, 0.9],
        competitiveIndex: 6,
      },
      {
        id: 'email_marketing',
        name: 'Email Marketing Campaigns',
        type: 'email',
        minBudget: 500,
        maxBudget: 15000,
        costModel: 'flat',
        baseRate: 0.15,
        scalingFactor: 1.3,
        conversionRate: 0.065,
        averageOrderValue: 68,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 3,
      },
    ];

    const request: OptimizationRequest = {
      id: 'ecommerce-q1-2024',
      totalBudget: 200000,
      channels,
      constraints: [
        {
          id: 'total_budget',
          type: 'total_budget',
          value: 200000,
          operator: '<=',
          priority: 10,
          flexible: false,
          tolerance: 0,
        },
        {
          id: 'min_roas',
          type: 'roas_target',
          value: 4.0,
          operator: '>=',
          priority: 9,
          flexible: true,
          tolerance: 0.15,
        },
        {
          id: 'max_cpa',
          type: 'cpa_target',
          value: 25,
          operator: '<=',
          priority: 8,
          flexible: true,
          tolerance: 0.2,
        },
      ],
      objective: {
        primary: 'maximize_revenue',
        secondary: 'maximize_conversions',
        weights: { primary: 0.7, secondary: 0.3 },
        timeHorizon: 'quarterly',
      },
      timeframe: {
        start: new Date('2024-01-01'),
        end: new Date('2024-03-31'),
      },
      historicalData: {
        channelPerformance: {
          google_search: [
            { date: new Date('2023-12-01'), spend: 45000, conversions: 1800, revenue: 171000 },
            { date: new Date('2023-11-01'), spend: 42000, conversions: 1650, revenue: 156750 },
          ],
          facebook_ads: [
            { date: new Date('2023-12-01'), spend: 35000, conversions: 1225, revenue: 95550 },
            { date: new Date('2023-11-01'), spend: 38000, conversions: 1330, revenue: 103740 },
          ],
        },
        marketConditions: [
          { date: new Date('2023-12-01'), competitiveIndex: 7.5, seasonalityFactor: 1.3, economicIndicator: 0.95 },
          { date: new Date('2023-11-01'), competitiveIndex: 7.2, seasonalityFactor: 1.1, economicIndicator: 0.98 },
        ],
      },
      preferences: {
        useQuantumOptimization: true,
        quantumFallbackThreshold: 0.9,
        maxIterations: 1500,
        convergenceThreshold: 0.0005,
        riskTolerance: 'moderate',
      },
    };

    const result = await optimizer.optimize(request);

    // Validate comprehensive result
    expect(result.status).toBe('success');
    expect(result.method).toBe('quantum');
    expect(result.allocations).toHaveLength(4);
    expect(result.totalAllocated).toBeLessThanOrEqual(200000);
    expect(result.overallROAS).toBeGreaterThan(3.5); // Should meet constraint with tolerance
    expect(result.confidence).toBeGreaterThan(0.85);

    // Validate quantum-specific metrics
    expect(result.quantumMetrics?.quantumAdvantage).toBeGreaterThan(0.8);
    expect(result.quantumMetrics?.willowChipUtilization).toBeGreaterThan(0);

    // Validate recommendations
    expect(result.recommendations.length).toBeGreaterThan(0);

    await optimizer.cleanup();
  });
});
