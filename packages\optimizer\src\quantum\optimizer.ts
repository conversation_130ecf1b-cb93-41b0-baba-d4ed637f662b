import { WillowBridge } from './willow-bridge';
import { ClassicalSolver } from '../classical/solver';
import {
  OptimizationRequest,
  OptimizationResult,
  WillowConfig,
  QuantumOptimizationError,
  BudgetAllocation,
} from '../types';

/**
 * Quantum Budget Optimizer
 * 
 * Main orchestrator that attempts quantum optimization first,
 * then falls back to classical methods if needed.
 * 
 * Features:
 * - Automatic quantum/classical selection
 * - Hybrid optimization strategies
 * - Performance monitoring and comparison
 * - Adaptive algorithm selection
 */
export class QuantumOptimizer {
  private willowBridge: WillowBridge;
  private classicalSolver: ClassicalSolver;
  private config: WillowConfig;
  private performanceHistory: Map<string, number[]> = new Map();

  constructor(config: WillowConfig) {
    this.config = config;
    this.willowBridge = new WillowBridge(config);
    this.classicalSolver = new ClassicalSolver({
      maxIterations: 1000,
      convergenceThreshold: 1e-6,
      populationSize: 100,
    });
  }

  /**
   * Main optimization entry point
   */
  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {
    const startTime = Date.now();
    
    try {
      // Determine optimization strategy
      const strategy = await this.selectOptimizationStrategy(request);
      console.log(`[Quantum] Using ${strategy} optimization strategy`);

      let result: OptimizationResult;

      switch (strategy) {
        case 'quantum':
          result = await this.quantumOptimization(request);
          break;
        case 'classical':
          result = await this.classicalOptimization(request);
          break;
        case 'hybrid':
          result = await this.hybridOptimization(request);
          break;
        default:
          throw new QuantumOptimizationError(
            `Unknown optimization strategy: ${strategy}`,
            'INVALID_STRATEGY'
          );
      }

      // Update performance history
      this.updatePerformanceHistory(strategy, result.computeTime);

      // Add quantum-specific recommendations
      result.recommendations.push(...this.generateQuantumRecommendations(result));

      return result;
    } catch (error) {
      console.error('[Quantum] Optimization failed, falling back to classical:', error);
      
      // Emergency fallback to classical
      const fallbackResult = await this.classicalOptimization(request);
      fallbackResult.status = 'partial';
      fallbackResult.recommendations.unshift({
        type: 'budget_increase',
        description: 'Quantum optimization failed. Consider upgrading to ensure quantum advantage.',
        impact: {
          revenueChange: 0,
          costChange: 0,
          riskChange: 1,
        },
        priority: 'medium',
      });

      return fallbackResult;
    }
  }

  /**
   * Pure quantum optimization using Willow
   */
  private async quantumOptimization(request: OptimizationRequest): Promise<OptimizationResult> {
    console.log('[Quantum] Starting quantum optimization on Willow');

    // Check processor availability
    const status = await this.willowBridge.getProcessorStatus();
    if (!status.available) {
      throw new QuantumOptimizationError(
        'Willow processor not available',
        'PROCESSOR_UNAVAILABLE',
        { status }
      );
    }

    // Create quantum circuit
    const circuitId = await this.willowBridge.createOptimizationCircuit(
      request,
      this.config.circuitConfig
    );

    // Prepare quantum parameters
    const parameters = this.prepareQuantumParameters(request);

    // Execute quantum optimization
    const quantumResult = await this.willowBridge.executeOptimization(circuitId, parameters);

    // Convert quantum result to budget allocations
    const allocations = this.convertQuantumToBudgetAllocations(
      quantumResult.result,
      request
    );

    // Build optimization result
    const result: OptimizationResult = {
      id: `quantum_${Date.now()}`,
      requestId: request.id,
      status: 'success',
      method: 'quantum',
      allocations,
      totalAllocated: allocations.reduce((sum, a) => sum + a.allocatedBudget, 0),
      expectedTotalRevenue: allocations.reduce((sum, a) => sum + a.expectedRevenue, 0),
      expectedTotalConversions: allocations.reduce((sum, a) => sum + a.expectedConversions, 0),
      overallROAS: 0, // Will be calculated
      overallCPA: 0, // Will be calculated
      confidence: quantumResult.confidence,
      riskScore: this.calculateQuantumRiskScore(quantumResult),
      optimizationMetrics: {
        iterations: 1, // Quantum is single-shot
        convergenceTime: quantumResult.metrics.executionTime,
        objectiveValue: this.calculateObjectiveValue(allocations, request),
        constraintViolations: [],
      },
      quantumMetrics: {
        quantumAdvantage: quantumResult.quantumAdvantage,
        coherenceTime: quantumResult.metrics.coherenceTime,
        gateCount: quantumResult.metrics.gateCount,
        errorRate: quantumResult.metrics.errorRate,
        willowChipUtilization: status.qubits / 105, // Willow has 105 qubits
      },
      recommendations: [],
      createdAt: new Date(),
      computeTime: quantumResult.metrics.executionTime,
    };

    // Calculate derived metrics
    result.overallROAS = result.expectedTotalRevenue / Math.max(result.totalAllocated, 1);
    result.overallCPA = result.totalAllocated / Math.max(result.expectedTotalConversions, 1);

    return result;
  }

  /**
   * Classical optimization fallback
   */
  private async classicalOptimization(request: OptimizationRequest): Promise<OptimizationResult> {
    console.log('[Quantum] Using classical optimization fallback');
    return await this.classicalSolver.optimize(request);
  }

  /**
   * Hybrid quantum-classical optimization
   */
  private async hybridOptimization(request: OptimizationRequest): Promise<OptimizationResult> {
    console.log('[Quantum] Starting hybrid optimization');

    try {
      // Start with quantum optimization for global exploration
      const quantumResult = await this.quantumOptimization(request);

      // Use quantum result as starting point for classical refinement
      const refinedRequest = this.createRefinementRequest(request, quantumResult);
      const classicalResult = await this.classicalSolver.optimize(refinedRequest);

      // Combine results
      const hybridResult = this.combineResults(quantumResult, classicalResult);
      hybridResult.method = 'hybrid';

      return hybridResult;
    } catch (error) {
      console.warn('[Quantum] Hybrid optimization failed, using classical only:', error);
      return await this.classicalOptimization(request);
    }
  }

  /**
   * Select the best optimization strategy based on problem characteristics
   */
  private async selectOptimizationStrategy(request: OptimizationRequest): Promise<'quantum' | 'classical' | 'hybrid'> {
    const numChannels = request.channels.length;
    const numConstraints = request.constraints.length;
    const totalBudget = request.totalBudget;

    // Check if quantum optimization is preferred
    if (!request.preferences.useQuantumOptimization) {
      return 'classical';
    }

    // Check Willow availability
    try {
      const status = await this.willowBridge.getProcessorStatus();
      if (!status.available || status.queueLength > 10) {
        return 'classical';
      }

      // Quantum advantage criteria
      const quantumAdvantageScore = this.calculateQuantumAdvantageScore(request);
      
      if (quantumAdvantageScore > 0.8) {
        return 'quantum';
      } else if (quantumAdvantageScore > 0.5) {
        return 'hybrid';
      } else {
        return 'classical';
      }
    } catch (error) {
      console.warn('[Quantum] Cannot access Willow, using classical:', error);
      return 'classical';
    }
  }

  /**
   * Calculate quantum advantage score for the problem
   */
  private calculateQuantumAdvantageScore(request: OptimizationRequest): number {
    let score = 0;

    // Problem size factor (quantum advantage increases with problem size)
    const numChannels = request.channels.length;
    if (numChannels >= 10) score += 0.3;
    if (numChannels >= 20) score += 0.2;

    // Constraint complexity (quantum handles complex constraints better)
    const complexConstraints = request.constraints.filter(c => 
      c.type === 'roas_target' || c.type === 'cpa_target'
    ).length;
    score += Math.min(complexConstraints * 0.15, 0.3);

    // Optimization objective (some objectives benefit more from quantum)
    if (request.objective.primary === 'maximize_roas') score += 0.2;
    if (request.objective.secondary) score += 0.1;

    // Historical performance
    const quantumHistory = this.performanceHistory.get('quantum') || [];
    const classicalHistory = this.performanceHistory.get('classical') || [];
    
    if (quantumHistory.length > 0 && classicalHistory.length > 0) {
      const avgQuantumTime = quantumHistory.reduce((a, b) => a + b) / quantumHistory.length;
      const avgClassicalTime = classicalHistory.reduce((a, b) => a + b) / classicalHistory.length;
      
      if (avgQuantumTime < avgClassicalTime) score += 0.2;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Prepare parameters for quantum circuit
   */
  private prepareQuantumParameters(request: OptimizationRequest): Record<string, number> {
    const parameters: Record<string, number> = {};

    // Budget constraints
    parameters.totalBudget = request.totalBudget / 1000000; // Normalize
    
    // Channel parameters
    request.channels.forEach((channel, i) => {
      parameters[`channel_${i}_min`] = channel.minBudget / request.totalBudget;
      parameters[`channel_${i}_max`] = channel.maxBudget / request.totalBudget;
      parameters[`channel_${i}_conversion_rate`] = channel.conversionRate;
      parameters[`channel_${i}_aov`] = channel.averageOrderValue / 1000; // Normalize
    });

    // Objective weights
    parameters.primary_weight = request.objective.weights.primary;
    parameters.secondary_weight = request.objective.weights.secondary || 0;

    return parameters;
  }

  /**
   * Convert quantum measurement results to budget allocations
   */
  private convertQuantumToBudgetAllocations(
    quantumResult: number[],
    request: OptimizationRequest
  ): BudgetAllocation[] {
    const allocations: BudgetAllocation[] = [];
    const totalBudget = request.totalBudget;

    // Normalize quantum results to valid budget allocations
    const sum = quantumResult.reduce((a, b) => a + b, 0);
    const normalizedWeights = quantumResult.map(w => w / sum);

    request.channels.forEach((channel, i) => {
      const weight = normalizedWeights[i] || 0;
      const budget = weight * totalBudget;
      
      // Ensure budget is within channel constraints
      const constrainedBudget = Math.max(
        channel.minBudget,
        Math.min(channel.maxBudget, budget)
      );

      // Calculate expected performance
      const conversions = this.calculateExpectedConversions(constrainedBudget, channel);
      const revenue = conversions * channel.averageOrderValue;

      allocations.push({
        channelId: channel.id,
        allocatedBudget: constrainedBudget,
        expectedConversions: conversions,
        expectedRevenue: revenue,
        expectedCPA: constrainedBudget / Math.max(conversions, 1),
        expectedROAS: revenue / Math.max(constrainedBudget, 1),
        confidence: 0.9, // Quantum typically has high confidence
        riskScore: 2, // Quantum optimization is generally low risk
      });
    });

    return allocations;
  }

  /**
   * Calculate expected conversions for a channel given budget
   */
  private calculateExpectedConversions(budget: number, channel: any): number {
    // Simplified model with diminishing returns
    const baseConversions = budget * channel.conversionRate / channel.baseRate;
    
    // Apply saturation curve
    let saturationFactor = 1;
    if (channel.saturationPoint && budget > channel.saturationPoint) {
      saturationFactor = Math.sqrt(channel.saturationPoint / budget);
    }
    
    return baseConversions * saturationFactor * channel.scalingFactor;
  }

  /**
   * Calculate quantum-specific risk score
   */
  private calculateQuantumRiskScore(quantumResult: any): number {
    let riskScore = 2; // Base low risk for quantum

    // Increase risk if error rate is high
    if (quantumResult.metrics.errorRate > 0.01) {
      riskScore += 2;
    }

    // Increase risk if coherence time is low
    if (quantumResult.metrics.coherenceTime < 50) {
      riskScore += 1;
    }

    // Decrease risk if quantum advantage is high
    if (quantumResult.quantumAdvantage > 0.8) {
      riskScore -= 1;
    }

    return Math.max(1, Math.min(10, riskScore));
  }

  /**
   * Calculate objective value for allocations
   */
  private calculateObjectiveValue(allocations: BudgetAllocation[], request: OptimizationRequest): number {
    let value = 0;

    switch (request.objective.primary) {
      case 'maximize_revenue':
        value = allocations.reduce((sum, a) => sum + a.expectedRevenue, 0);
        break;
      case 'maximize_conversions':
        value = allocations.reduce((sum, a) => sum + a.expectedConversions, 0);
        break;
      case 'minimize_cpa':
        const totalCPA = allocations.reduce((sum, a) => sum + a.expectedCPA, 0) / allocations.length;
        value = -totalCPA; // Negative because we want to minimize
        break;
      case 'maximize_roas':
        const totalROAS = allocations.reduce((sum, a) => sum + a.expectedROAS, 0) / allocations.length;
        value = totalROAS;
        break;
    }

    return value;
  }

  /**
   * Generate quantum-specific recommendations
   */
  private generateQuantumRecommendations(result: OptimizationResult): any[] {
    const recommendations: any[] = [];

    // Quantum advantage recommendation
    if (result.quantumMetrics?.quantumAdvantage && result.quantumMetrics.quantumAdvantage > 0.8) {
      recommendations.push({
        type: 'budget_increase',
        description: 'High quantum advantage detected. Consider increasing budget to maximize quantum benefits.',
        impact: {
          revenueChange: result.expectedTotalRevenue * 0.15,
          costChange: result.totalAllocated * 0.1,
          riskChange: -0.5,
        },
        priority: 'high',
      });
    }

    // Error rate recommendation
    if (result.quantumMetrics?.errorRate && result.quantumMetrics.errorRate > 0.01) {
      recommendations.push({
        type: 'timing_adjustment',
        description: 'Higher than optimal quantum error rate. Consider running optimization during off-peak hours.',
        impact: {
          revenueChange: result.expectedTotalRevenue * 0.05,
          costChange: 0,
          riskChange: -1,
        },
        priority: 'medium',
      });
    }

    return recommendations;
  }

  /**
   * Create refinement request for hybrid optimization
   */
  private createRefinementRequest(original: OptimizationRequest, quantumResult: OptimizationResult): OptimizationRequest {
    // Use quantum result to create tighter constraints for classical refinement
    const refinedRequest = { ...original };
    
    // Add budget allocation constraints based on quantum result
    quantumResult.allocations.forEach((allocation, i) => {
      const tolerance = 0.2; // 20% tolerance around quantum solution
      const minBudget = allocation.allocatedBudget * (1 - tolerance);
      const maxBudget = allocation.allocatedBudget * (1 + tolerance);
      
      refinedRequest.constraints.push({
        id: `quantum_constraint_${i}`,
        type: 'channel_budget',
        value: maxBudget,
        operator: '<=',
        priority: 7,
        flexible: true,
        tolerance: 0.1,
      });
    });

    return refinedRequest;
  }

  /**
   * Combine quantum and classical results
   */
  private combineResults(quantumResult: OptimizationResult, classicalResult: OptimizationResult): OptimizationResult {
    // Use quantum result as base and incorporate classical improvements
    const combined = { ...quantumResult };
    
    // Take the better objective value
    if (classicalResult.optimizationMetrics.objectiveValue > quantumResult.optimizationMetrics.objectiveValue) {
      combined.allocations = classicalResult.allocations;
      combined.totalAllocated = classicalResult.totalAllocated;
      combined.expectedTotalRevenue = classicalResult.expectedTotalRevenue;
      combined.expectedTotalConversions = classicalResult.expectedTotalConversions;
      combined.overallROAS = classicalResult.overallROAS;
      combined.overallCPA = classicalResult.overallCPA;
    }

    // Combine confidence scores
    combined.confidence = (quantumResult.confidence + classicalResult.confidence) / 2;
    
    // Take minimum risk score
    combined.riskScore = Math.min(quantumResult.riskScore, classicalResult.riskScore);

    return combined;
  }

  /**
   * Update performance history for algorithm selection
   */
  private updatePerformanceHistory(strategy: string, computeTime: number): void {
    if (!this.performanceHistory.has(strategy)) {
      this.performanceHistory.set(strategy, []);
    }
    
    const history = this.performanceHistory.get(strategy)!;
    history.push(computeTime);
    
    // Keep only last 10 results
    if (history.length > 10) {
      history.shift();
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    await this.willowBridge.cleanup();
    this.performanceHistory.clear();
  }
}
