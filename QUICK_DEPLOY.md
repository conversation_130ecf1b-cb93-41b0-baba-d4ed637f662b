# 🚀 Metamorphic Flux - Quick Deploy Guide

## 🎯 Deploy in 15 Minutes

### Step 1: Setup Supabase (5 minutes)

1. **Create Supabase Project**
   ```bash
   # Go to https://supabase.com/dashboard
   # Click "New Project"
   # Choose organization and region
   # Wait for project to be ready
   ```

2. **Get Your Keys**
   ```bash
   # From Project Settings > API
   # Copy: Project URL and anon public key
   # From Project Settings > Database
   # Copy: Connection string
   ```

3. **Run Database Setup**
   ```bash
   cd supabase
   npx supabase db push --db-url "your-connection-string"
   ```

### Step 2: Get Google AI API Key (2 minutes)

1. **Get API Key**
   ```bash
   # Go to https://makersuite.google.com/app/apikey
   # Click "Create API Key"
   # Copy the key (starts with AI<PERSON>...)
   ```

### Step 3: Deploy Web App to Vercel (3 minutes)

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy**
   ```bash
   cd apps/web
   vercel --prod
   ```

3. **Set Environment Variables**
   ```bash
   # In Vercel Dashboard > Settings > Environment Variables
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   GOOGLE_AI_API_KEY=your_google_ai_key
   NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
   ```

4. **Redeploy**
   ```bash
   vercel --prod
   ```

### Step 4: Deploy Agents Service to Railway (3 minutes)

1. **Install Railway CLI**
   ```bash
   npm i -g @railway/cli
   ```

2. **Deploy**
   ```bash
   cd apps/agents
   railway login
   railway up
   ```

3. **Set Environment Variables**
   ```bash
   # In Railway Dashboard > Variables
   NODE_ENV=production
   PORT=3001
   GOOGLE_AI_API_KEY=your_google_ai_key
   DATABASE_URL=your_supabase_connection_string
   ```

### Step 5: Update Web App with Agents URL (2 minutes)

1. **Get Railway URL**
   ```bash
   # From Railway Dashboard
   # Copy the generated URL (e.g., https://your-app.railway.app)
   ```

2. **Update Vercel Environment**
   ```bash
   # Add to Vercel environment variables
   NEXT_PUBLIC_AGENTS_URL=https://your-agents-app.railway.app
   ```

3. **Redeploy**
   ```bash
   vercel --prod
   ```

## ✅ Verify Deployment

### Check Health Status
```bash
# Web app health
curl https://your-domain.vercel.app/api/health

# Agents service health
curl https://your-agents-app.railway.app/health
```

### Test the Application
1. Visit your deployed URL
2. Sign up for an account
3. Access the dashboard
4. Test the Canvas interface

## 🎉 You're Live!

Your Metamorphic Flux MVP is now live and ready for users!

### Next Steps:
1. **Custom Domain**: Configure your own domain in Vercel
2. **Monitoring**: Set up Sentry for error tracking
3. **Analytics**: Enable Vercel Analytics
4. **Scaling**: Monitor usage and scale as needed

### Support:
- Health Check: `https://your-domain.com/api/health`
- Documentation: Check the DEPLOYMENT.md file
- Issues: Monitor logs in Vercel and Railway dashboards

## 🔧 Common Issues

### "Database connection failed"
- Check your Supabase connection string
- Ensure RLS policies are properly configured
- Verify service role key permissions

### "Google AI API key not configured"
- Verify the API key starts with "AIza"
- Check the key has proper permissions
- Ensure it's set in both web app and agents service

### "Agents service not responding"
- Check Railway deployment logs
- Verify environment variables are set
- Ensure the service is running and healthy

## 📊 Monitoring

### Key URLs to Monitor:
- **Web App**: `https://your-domain.com`
- **Health Check**: `https://your-domain.com/api/health`
- **Agents Health**: `https://your-agents-app.railway.app/health`

### Success Indicators:
- Health status: "healthy" or "degraded" (not "unhealthy")
- Response time: < 2 seconds
- All services: "connected" or "configured"

---

**Congratulations! 🎉 Metamorphic Flux is now live and transforming marketing with AI!**
