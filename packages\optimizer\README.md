# @metamorphic-flux/optimizer

🚀 **Quantum-Enhanced Budget Optimization with Google Willow Integration**

A cutting-edge optimization package that leverages Google's Willow quantum processor for marketing budget allocation, with robust classical algorithm fallbacks.

## ✨ Features

### 🔬 Quantum Optimization
- **Google Willow Integration**: Direct REST API bridge to Google's 105-qubit quantum processor
- **Quantum Advantage**: Automatic detection and utilization of quantum speedup
- **Error Correction**: Built-in quantum error correction and coherence management
- **Hybrid Algorithms**: VQE, QAOA, and quantum annealing implementations

### 🧮 Classical Algorithms
- **Sequential Quadratic Programming (SQP)**: For smooth, differentiable problems
- **Genetic Algorithm**: For complex, discrete optimization
- **Particle Swarm Optimization (PSO)**: For continuous multi-modal problems
- **Simulated Annealing**: For escaping local optima
- **Interior Point Method**: For convex problems with constraints

### 🎯 Smart Algorithm Selection
- **Automatic Strategy Selection**: Chooses optimal quantum/classical/hybrid approach
- **Performance Monitoring**: Tracks and learns from optimization history
- **Adaptive Fallback**: Seamless quantum-to-classical fallback on errors
- **Real-time Monitoring**: Live optimization with performance callbacks

## 🚀 Quick Start

```bash
npm install @metamorphic-flux/optimizer
```

### Basic Usage

```typescript
import { optimizeBudget } from '@metamorphic-flux/optimizer';

const channels = [
  {
    id: 'google',
    name: 'Google Ads',
    type: 'google',
    minBudget: 1000,
    maxBudget: 50000,
    costModel: 'cpc',
    baseRate: 2.5,
    conversionRate: 0.04,
    averageOrderValue: 85,
  },
  {
    id: 'facebook',
    name: 'Facebook Ads', 
    type: 'facebook',
    minBudget: 800,
    maxBudget: 40000,
    costModel: 'cpm',
    baseRate: 12.0,
    conversionRate: 0.035,
    averageOrderValue: 75,
  },
];

const result = await optimizeBudget(
  100000, // Total budget
  channels,
  'maximize_revenue' // Objective
);

console.log(`Expected Revenue: $${result.expectedTotalRevenue}`);
console.log(`Overall ROAS: ${result.overallROAS}x`);
console.log(`Method: ${result.method}`); // 'quantum', 'classical', or 'hybrid'
```

### Advanced Usage

```typescript
import { QuantumOptimizer, createOptimizer } from '@metamorphic-flux/optimizer';

// Configure Willow quantum processor
const optimizer = createOptimizer({
  endpoint: 'https://quantum.googleapis.com',
  apiKey: process.env.WILLOW_API_KEY,
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
  circuitConfig: {
    qubits: 50,
    depth: 200,
    errorCorrection: true,
    coherenceTime: 100,
    fidelity: 0.999,
  },
});

const request = {
  id: 'campaign-q1-2024',
  totalBudget: 500000,
  channels: channels,
  constraints: [
    {
      id: 'total_budget',
      type: 'total_budget',
      value: 500000,
      operator: '<=',
      priority: 10,
    },
    {
      id: 'min_roas',
      type: 'roas_target',
      value: 4.0,
      operator: '>=',
      priority: 8,
      flexible: true,
      tolerance: 0.1,
    },
  ],
  objective: {
    primary: 'maximize_revenue',
    secondary: 'maximize_conversions',
    weights: { primary: 0.7, secondary: 0.3 },
  },
  preferences: {
    useQuantumOptimization: true,
    riskTolerance: 'moderate',
  },
};

const result = await optimizer.optimize(request);

// Quantum-specific metrics
if (result.quantumMetrics) {
  console.log(`Quantum Advantage: ${result.quantumMetrics.quantumAdvantage}`);
  console.log(`Coherence Time: ${result.quantumMetrics.coherenceTime}μs`);
  console.log(`Error Rate: ${result.quantumMetrics.errorRate}`);
}
```

## 🔧 Configuration

### Environment Variables

```bash
# Required for quantum optimization
WILLOW_API_KEY=your_google_quantum_api_key
GOOGLE_CLOUD_PROJECT=your_project_id

# Optional
WILLOW_ENDPOINT=https://quantum.googleapis.com
WILLOW_REGION=us-central1
```

### Willow Configuration

```typescript
const willowConfig = {
  endpoint: 'https://quantum.googleapis.com',
  apiKey: process.env.WILLOW_API_KEY,
  projectId: process.env.GOOGLE_CLOUD_PROJECT,
  region: 'us-central1',
  timeout: 30000,
  retries: 3,
  circuitConfig: {
    qubits: 20,              // Number of qubits (max 105 for Willow)
    depth: 100,              // Circuit depth
    gateSet: ['H', 'X', 'CNOT'], // Available quantum gates
    errorCorrection: true,    // Enable quantum error correction
    coherenceTime: 100,       // Coherence time in microseconds
    fidelity: 0.999,         // Target gate fidelity
  },
};
```

## 📊 Optimization Results

```typescript
interface OptimizationResult {
  id: string;
  status: 'success' | 'partial' | 'failed';
  method: 'quantum' | 'classical' | 'hybrid';
  
  // Budget allocations
  allocations: BudgetAllocation[];
  totalAllocated: number;
  
  // Performance predictions
  expectedTotalRevenue: number;
  expectedTotalConversions: number;
  overallROAS: number;
  overallCPA: number;
  
  // Confidence and risk
  confidence: number;        // 0-1
  riskScore: number;         // 1-10
  
  // Quantum metrics (if applicable)
  quantumMetrics?: {
    quantumAdvantage: number;
    coherenceTime: number;
    gateCount: number;
    errorRate: number;
    willowChipUtilization: number;
  };
  
  // Recommendations
  recommendations: Recommendation[];
  
  // Performance
  computeTime: number;
  optimizationMetrics: {
    iterations: number;
    convergenceTime: number;
    objectiveValue: number;
  };
}
```

## 🎯 Optimization Objectives

- **`maximize_revenue`**: Maximize total expected revenue
- **`maximize_conversions`**: Maximize total conversions
- **`minimize_cpa`**: Minimize cost per acquisition
- **`maximize_roas`**: Maximize return on ad spend

## 🔒 Constraints

- **`total_budget`**: Total budget limit
- **`channel_budget`**: Per-channel budget limits
- **`daily_budget`**: Daily spending limits
- **`cpa_target`**: Cost per acquisition targets
- **`roas_target`**: Return on ad spend targets

## 🧪 Testing

```bash
# Run all tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test suite
npm test quantum-optimizer
npm test classical-solver
```

## 📈 Performance Comparison

```typescript
import { compareOptimizationMethods } from '@metamorphic-flux/optimizer';

const comparison = await compareOptimizationMethods(request);

console.log('Quantum Result:', comparison.quantum);
console.log('Classical Result:', comparison.classical);
console.log('Recommendation:', comparison.comparison.recommendation);
console.log('Performance Difference:', comparison.comparison.performanceDifference);
```

## 🔄 Real-time Monitoring

```typescript
import { OptimizationMonitor } from '@metamorphic-flux/optimizer';

const monitor = new OptimizationMonitor(willowConfig);

await monitor.startMonitoring(
  request,
  (result) => {
    console.log('Updated optimization:', result);
    // Update dashboard, send alerts, etc.
  },
  60000 // Re-optimize every minute
);

// Stop monitoring
monitor.stopMonitoring();
await monitor.cleanup();
```

## 🛠️ Utilities

```typescript
import { 
  validateRequest, 
  formatResult, 
  generateInsights,
  calculatePortfolioRisk 
} from '@metamorphic-flux/optimizer';

// Validate optimization request
const validation = validateRequest(request);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}

// Format result for display
const formatted = formatResult(result);
console.log(formatted.summary);
console.table(formatted.allocations);

// Generate insights
const insights = generateInsights(result);
console.log('Top Performers:', insights.topPerformers);
console.log('Opportunities:', insights.opportunities);

// Calculate portfolio risk
const risk = calculatePortfolioRisk(result);
console.log('Diversification Score:', risk.diversificationScore);
console.log('Overall Risk:', risk.overallRisk);
```

## 🔬 Quantum Algorithms

### Variational Quantum Eigensolver (VQE)
Optimal for finding ground state solutions in budget optimization problems.

### Quantum Approximate Optimization Algorithm (QAOA)
Excellent for combinatorial optimization with discrete budget allocations.

### Quantum Annealing
Best for problems with many local optima and complex constraint landscapes.

## 🏗️ Architecture

```
@metamorphic-flux/optimizer
├── quantum/
│   ├── optimizer.ts      # Main quantum optimizer
│   ├── willow-bridge.ts  # Google Willow API bridge
│   └── index.ts
├── classical/
│   ├── solver.ts         # Classical algorithms
│   └── index.ts
├── types/
│   └── index.ts          # TypeScript definitions
├── utils/
│   └── index.ts          # Utility functions
└── __tests__/            # Comprehensive test suite
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## 🆘 Support

- 📧 Email: <EMAIL>
- 📖 Documentation: https://docs.metamorphic-flux.com
- 🐛 Issues: https://github.com/metamorphic-flux/optimizer/issues

---

**Powered by Google Willow Quantum Processor** 🚀
