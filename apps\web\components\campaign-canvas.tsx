'use client';

import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { Draggable } from 'gsap/Draggable';
import { Button } from '@/components/ui/button';
import { FluxCanvas } from '@/components/flux-canvas';
import { useAgents } from '@/hooks/use-agents';
import { 
  Zap, 
  Target, 
  BarChart3, 
  Users, 
  Palette, 
  Brain,
  Play,
  Pause,
  Settings,
  Plus,
  Sparkles
} from 'lucide-react';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(Draggable);
}

interface CampaignNode {
  id: string;
  type: 'persona' | 'creative' | 'channel' | 'metric' | 'agent';
  title: string;
  subtitle?: string;
  status: 'idle' | 'processing' | 'complete' | 'error';
  position: { x: number; y: number };
  connections: string[];
  data?: any;
}

interface CampaignCanvasProps {
  campaignId?: string;
  className?: string;
}

export function CampaignCanvas({ campaignId, className }: CampaignCanvasProps): React.JSX.Element {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [nodes, setNodes] = useState<CampaignNode[]>([]);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const { workflow, areAgentsHealthy } = useAgents();

  // Initialize default nodes
  useEffect(() => {
    const defaultNodes: CampaignNode[] = [
      {
        id: 'personas',
        type: 'persona',
        title: 'Target Personas',
        subtitle: '3 personas defined',
        status: 'complete',
        position: { x: 100, y: 200 },
        connections: ['copy-agent', 'design-agent'],
      },
      {
        id: 'copy-agent',
        type: 'agent',
        title: 'Copy Agent',
        subtitle: 'AI copywriting',
        status: 'idle',
        position: { x: 300, y: 150 },
        connections: ['design-agent', 'creatives'],
      },
      {
        id: 'design-agent',
        type: 'agent',
        title: 'Design Agent',
        subtitle: 'Visual creation',
        status: 'idle',
        position: { x: 300, y: 250 },
        connections: ['creatives'],
      },
      {
        id: 'creatives',
        type: 'creative',
        title: 'Creative Assets',
        subtitle: '5 variants generated',
        status: 'idle',
        position: { x: 500, y: 200 },
        connections: ['channels'],
      },
      {
        id: 'channels',
        type: 'channel',
        title: 'Distribution',
        subtitle: 'Facebook, Google, LinkedIn',
        status: 'idle',
        position: { x: 700, y: 200 },
        connections: ['metrics'],
      },
      {
        id: 'metrics',
        type: 'metric',
        title: 'Performance',
        subtitle: 'Real-time analytics',
        status: 'idle',
        position: { x: 900, y: 200 },
        connections: [],
      },
    ];

    setNodes(defaultNodes);
  }, []);

  // Make nodes draggable
  useEffect(() => {
    if (!canvasRef.current) return;

    const draggables = nodes.map(node => {
      const element = canvasRef.current?.querySelector(`[data-node-id="${node.id}"]`);
      if (!element) return null;

      return Draggable.create(element, {
        type: 'x,y',
        bounds: canvasRef.current,
        onDrag: function() {
          // Update node position
          setNodes(prev => prev.map(n => 
            n.id === node.id 
              ? { ...n, position: { x: this.x, y: this.y } }
              : n
          ));
        },
        onDragEnd: function() {
          // Snap to grid
          const gridSize = 20;
          const snappedX = Math.round(this.x / gridSize) * gridSize;
          const snappedY = Math.round(this.y / gridSize) * gridSize;
          
          gsap.to(this.target, {
            x: snappedX,
            y: snappedY,
            duration: 0.3,
            ease: 'power2.out',
          });

          setNodes(prev => prev.map(n => 
            n.id === node.id 
              ? { ...n, position: { x: snappedX, y: snappedY } }
              : n
          ));
        },
      });
    }).filter(Boolean);

    return () => {
      draggables.forEach(draggable => draggable?.[0]?.kill());
    };
  }, [nodes]);

  const getNodeIcon = (type: CampaignNode['type']) => {
    switch (type) {
      case 'persona': return Users;
      case 'creative': return Palette;
      case 'channel': return Target;
      case 'metric': return BarChart3;
      case 'agent': return Brain;
      default: return Zap;
    }
  };

  const getStatusColor = (status: CampaignNode['status']) => {
    switch (status) {
      case 'processing': return 'border-yellow-500 bg-yellow-500/20';
      case 'complete': return 'border-green-500 bg-green-500/20';
      case 'error': return 'border-red-500 bg-red-500/20';
      default: return 'border-gray-500 bg-gray-500/20';
    }
  };

  const handlePlayPause = async () => {
    if (!campaignId) return;

    setIsPlaying(!isPlaying);
    
    if (!isPlaying) {
      // Start workflow execution
      const context = {
        campaignId,
        executionId: `exec_${Date.now()}`,
        userId: 'current-user', // Would come from auth
        organizationId: 'current-org', // Would come from auth
        campaign: {
          id: campaignId,
          name: 'AI Marketing Campaign',
          description: 'Automated campaign optimization',
        },
        personas: [],
        existingCreatives: [],
        objectives: { primary: 'brand_awareness' },
        constraints: {},
        metadata: {},
      };

      // Animate nodes to show processing
      nodes.forEach((node, index) => {
        setTimeout(() => {
          setNodes(prev => prev.map(n => 
            n.id === node.id ? { ...n, status: 'processing' } : n
          ));
        }, index * 500);
      });

      try {
        await workflow.execute(context);
        
        // Mark all nodes as complete
        setNodes(prev => prev.map(n => ({ ...n, status: 'complete' })));
      } catch (error) {
        console.error('Workflow execution failed:', error);
        setNodes(prev => prev.map(n => ({ ...n, status: 'error' })));
      }
    } else {
      // Stop workflow
      setNodes(prev => prev.map(n => ({ ...n, status: 'idle' })));
    }
  };

  const addNode = (type: CampaignNode['type']) => {
    const newNode: CampaignNode = {
      id: `${type}_${Date.now()}`,
      type,
      title: `New ${type}`,
      status: 'idle',
      position: { x: 400, y: 300 },
      connections: [],
    };

    setNodes(prev => [...prev, newNode]);
  };

  return (
    <div className={`relative w-full h-full bg-background overflow-hidden ${className ?? ''}`}>
      {/* Background Flux Canvas */}
      <FluxCanvas 
        intensity="low" 
        interactive={false}
        className="opacity-30"
      />

      {/* Canvas Controls */}
      <div className="absolute top-4 left-4 z-10 flex items-center space-x-2">
        <Button
          onClick={handlePlayPause}
          disabled={workflow.isLoading}
          className={`${isPlaying ? 'bg-red-500 hover:bg-red-600' : 'flux-gradient'}`}
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          {isPlaying ? 'Stop' : 'Start'} Workflow
        </Button>

        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>

        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => addNode('persona')}
            title="Add Persona"
          >
            <Users className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => addNode('creative')}
            title="Add Creative"
          >
            <Palette className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => addNode('agent')}
            title="Add Agent"
          >
            <Brain className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Agent Status */}
      <div className="absolute top-4 right-4 z-10">
        <div className="bg-card/80 backdrop-blur-sm border rounded-lg p-3">
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${areAgentsHealthy() ? 'bg-green-500' : 'bg-red-500'}`} />
            <span>Agents {areAgentsHealthy() ? 'Online' : 'Offline'}</span>
          </div>
        </div>
      </div>

      {/* Canvas Area */}
      <div 
        ref={canvasRef}
        className="relative w-full h-full"
        style={{ minHeight: '600px' }}
      >
        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none z-0">
          {nodes.map(node => 
            node.connections.map(targetId => {
              const targetNode = nodes.find(n => n.id === targetId);
              if (!targetNode) return null;

              return (
                <line
                  key={`${node.id}-${targetId}`}
                  x1={node.position.x + 60}
                  y1={node.position.y + 40}
                  x2={targetNode.position.x + 60}
                  y2={targetNode.position.y + 40}
                  stroke="hsl(var(--border))"
                  strokeWidth="2"
                  strokeDasharray="5,5"
                  className="opacity-50"
                />
              );
            })
          )}
        </svg>

        {/* Nodes */}
        {nodes.map(node => {
          const Icon = getNodeIcon(node.type);
          
          return (
            <div
              key={node.id}
              data-node-id={node.id}
              className={`absolute cursor-move select-none ${getStatusColor(node.status)} border-2 rounded-xl p-4 backdrop-blur-sm transition-all duration-300 hover:scale-105 z-10`}
              style={{
                left: node.position.x,
                top: node.position.y,
                width: '120px',
                height: '80px',
              }}
              onClick={() => setSelectedNode(selectedNode === node.id ? null : node.id)}
            >
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between mb-1">
                  <Icon className="h-5 w-5 text-foreground" />
                  {node.status === 'processing' && (
                    <Sparkles className="h-4 w-4 text-yellow-500 animate-pulse" />
                  )}
                </div>
                
                <div className="flex-1">
                  <h3 className="text-xs font-semibold text-foreground truncate">
                    {node.title}
                  </h3>
                  {node.subtitle && (
                    <p className="text-xs text-muted-foreground truncate">
                      {node.subtitle}
                    </p>
                  )}
                </div>
              </div>

              {selectedNode === node.id && (
                <div className="absolute -top-2 -right-2 bg-flux-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                  ✓
                </div>
              )}
            </div>
          );
        })}

        {/* Add Node Button */}
        <Button
          variant="outline"
          className="absolute bottom-4 right-4 rounded-full w-12 h-12 p-0"
          onClick={() => addNode('agent')}
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>

      {/* Node Details Panel */}
      {selectedNode && (
        <div className="absolute bottom-4 left-4 bg-card/90 backdrop-blur-sm border rounded-lg p-4 w-80 z-20">
          {(() => {
            const node = nodes.find(n => n.id === selectedNode);
            if (!node) return null;

            const Icon = getNodeIcon(node.type);

            return (
              <div>
                <div className="flex items-center space-x-3 mb-3">
                  <Icon className="h-6 w-6 text-flux-500" />
                  <div>
                    <h3 className="font-semibold">{node.title}</h3>
                    <p className="text-sm text-muted-foreground">{node.subtitle}</p>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className={`capitalize ${
                      node.status === 'complete' ? 'text-green-500' :
                      node.status === 'processing' ? 'text-yellow-500' :
                      node.status === 'error' ? 'text-red-500' :
                      'text-gray-500'
                    }`}>
                      {node.status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Connections:</span>
                    <span>{node.connections.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Type:</span>
                    <span className="capitalize">{node.type}</span>
                  </div>
                </div>

                <div className="mt-4 flex space-x-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    Configure
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    Analyze
                  </Button>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}
