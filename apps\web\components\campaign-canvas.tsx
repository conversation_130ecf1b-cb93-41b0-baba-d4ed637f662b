'use client';

import { useState, useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { Draggable } from 'gsap/Draggable';
import { Button } from '@/components/ui/button';
import { FluxCanvas } from '@/components/flux-canvas';
import { CanvasToolbar, useCanvasKeyboardShortcuts } from '@/components/canvas-toolbar';
import { CanvasNodeComponent } from '@/components/canvas-node';
import { CanvasMinimap } from '@/components/canvas-minimap';
import { useAgents } from '@/hooks/use-agents';
import {
  useCanvasStore,
  useCanvasNodes,
  useCanvasConnections,
  useCanvasWorkflow,
  useCanvasSettings,
  useCanvasViewport,
  type CanvasNode
} from '@/lib/stores/canvas-store';
import {
  Zap,
  Target,
  BarChart3,
  Users,
  Palette,
  Brain,
  Play,
  Pause,
  Settings,
  Plus,
  Sparkles,
  Maximize2,
  Minimize2
} from 'lucide-react';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(Draggable);
}

interface CampaignCanvasProps {
  campaignId?: string;
  className?: string;
  isFullscreen?: boolean;
  onToggleFullscreen?: () => void;
}

export function CampaignCanvas({
  campaignId,
  className,
  isFullscreen = false,
  onToggleFullscreen
}: CampaignCanvasProps): React.JSX.Element {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<string | null>(null);

  // Zustand store hooks
  const nodes = useCanvasNodes();
  const connections = useCanvasConnections();
  const viewport = useCanvasViewport();
  const settings = useCanvasSettings();
  const { isPlaying, startWorkflow, stopWorkflow } = useCanvasWorkflow();
  const {
    addNode,
    addConnection,
    selectNode,
    clearSelection,
    setViewport,
    saveToHistory
  } = useCanvasStore();

  const { workflow, areAgentsHealthy } = useAgents();

  // Set up keyboard shortcuts
  useCanvasKeyboardShortcuts();

  // Initialize default nodes if canvas is empty
  useEffect(() => {
    if (nodes.length === 0) {
      const defaultNodes = [
        {
          type: 'persona' as const,
          title: 'Target Personas',
          subtitle: '3 personas defined',
          status: 'complete' as const,
          position: { x: 100, y: 200 },
          size: { width: 120, height: 80 },
          connections: ['copy-agent', 'design-agent'],
        },
        {
          type: 'agent' as const,
          title: 'Copy Agent',
          subtitle: 'AI copywriting',
          status: 'idle' as const,
          position: { x: 300, y: 150 },
          size: { width: 120, height: 80 },
          connections: ['design-agent', 'creatives'],
        },
        {
          type: 'agent' as const,
          title: 'Design Agent',
          subtitle: 'Visual creation',
          status: 'idle' as const,
          position: { x: 300, y: 250 },
          size: { width: 120, height: 80 },
          connections: ['creatives'],
        },
        {
          type: 'creative' as const,
          title: 'Creative Assets',
          subtitle: '5 variants generated',
          status: 'idle' as const,
          position: { x: 500, y: 200 },
          size: { width: 120, height: 80 },
          connections: ['channels'],
        },
        {
          type: 'channel' as const,
          title: 'Distribution',
          subtitle: 'Facebook, Google, LinkedIn',
          status: 'idle' as const,
          position: { x: 700, y: 200 },
          size: { width: 120, height: 80 },
          connections: ['metrics'],
        },
        {
          type: 'metric' as const,
          title: 'Performance',
          subtitle: 'Real-time analytics',
          status: 'idle' as const,
          position: { x: 900, y: 200 },
          size: { width: 120, height: 80 },
          connections: [],
        },
      ];

      defaultNodes.forEach(node => addNode(node));
      saveToHistory();
    }
  }, [nodes.length, addNode, saveToHistory]);

  // Handle canvas interactions
  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      clearSelection();
      setSelectedNode(null);
      setIsConnecting(false);
      setConnectionStart(null);
    }
  };

  const handleNodeConnect = (nodeId: string) => {
    if (isConnecting && connectionStart && connectionStart !== nodeId) {
      // Create connection
      addConnection({
        sourceId: connectionStart,
        targetId: nodeId,
        type: 'workflow',
        status: 'active',
      });
      setIsConnecting(false);
      setConnectionStart(null);
    } else {
      // Start connection
      setIsConnecting(true);
      setConnectionStart(nodeId);
    }
  };

  const addNewNode = (type: CanvasNode['type']) => {
    const newNode = {
      type,
      title: `New ${type}`,
      subtitle: `${type} description`,
      status: 'idle' as const,
      position: {
        x: Math.random() * 400 + 200,
        y: Math.random() * 300 + 200
      },
      size: { width: 120, height: 80 },
      connections: [],
    };

    addNode(newNode);
    saveToHistory();
  };

  const handleWorkflowToggle = async () => {
    if (!campaignId) return;

    if (!isPlaying) {
      startWorkflow();

      // Start workflow execution
      const context = {
        campaignId,
        executionId: `exec_${Date.now()}`,
        userId: 'current-user', // Would come from auth
        organizationId: 'current-org', // Would come from auth
        campaign: {
          id: campaignId,
          name: 'AI Marketing Campaign',
          description: 'Automated campaign optimization',
        },
        personas: [],
        existingCreatives: [],
        objectives: { primary: 'brand_awareness' },
        constraints: {},
        metadata: {},
      };

      try {
        await workflow.execute(context);
      } catch (error) {
        console.error('Workflow execution failed:', error);
        stopWorkflow();
      }
    } else {
      stopWorkflow();
    }
  };

  return (
    <div className={`relative w-full h-full bg-background overflow-hidden ${className ?? ''}`}>
      {/* Background Flux Canvas */}
      <FluxCanvas
        intensity="low"
        interactive={false}
        className="opacity-30"
      />

      {/* Canvas Toolbar */}
      <div className="absolute top-4 left-4 z-20">
        <CanvasToolbar orientation="horizontal" />
      </div>

      {/* Fullscreen Toggle */}
      {onToggleFullscreen && (
        <div className="absolute top-4 right-4 z-20">
          <Button
            variant="outline"
            size="sm"
            onClick={onToggleFullscreen}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      )}

      {/* Add Node Buttons */}
      <div className="absolute top-4 right-20 z-20 flex items-center space-x-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => addNewNode('persona')}
          title="Add Persona"
        >
          <Users className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => addNewNode('creative')}
          title="Add Creative"
        >
          <Palette className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => addNewNode('agent')}
          title="Add Agent"
        >
          <Brain className="h-4 w-4" />
        </Button>
      </div>

      {/* Minimap */}
      {settings.showMinimap && (
        <div className="absolute bottom-4 right-4 z-20">
          <CanvasMinimap width={200} height={150} />
        </div>
      )}

      {/* Agent Status */}
      <div className="absolute bottom-4 left-4 z-20">
        <div className="bg-card/80 backdrop-blur-sm border rounded-lg p-3">
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full ${areAgentsHealthy() ? 'bg-green-500' : 'bg-red-500'}`} />
            <span>Agents {areAgentsHealthy() ? 'Online' : 'Offline'}</span>
          </div>
        </div>
      </div>

      {/* Canvas Area */}
      <div
        ref={canvasRef}
        className="relative w-full h-full cursor-default"
        style={{
          minHeight: '600px',
          transform: `translate(${viewport.x}px, ${viewport.y}px) scale(${viewport.zoom})`,
          transformOrigin: '0 0',
        }}
        onClick={handleCanvasClick}
      >
        {/* Grid Background */}
        {settings.gridEnabled && (
          <div
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: `
                linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
                linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px)
              `,
              backgroundSize: `${settings.gridSize}px ${settings.gridSize}px`,
            }}
          />
        )}

        {/* Connection Lines */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none z-0">
          {connections.map(connection => {
            const sourceNode = nodes.find(n => n.id === connection.sourceId);
            const targetNode = nodes.find(n => n.id === connection.targetId);
            if (!sourceNode || !targetNode) return null;

            return (
              <line
                key={connection.id}
                x1={sourceNode.position.x + sourceNode.size.width / 2}
                y1={sourceNode.position.y + sourceNode.size.height / 2}
                x2={targetNode.position.x + targetNode.size.width / 2}
                y2={targetNode.position.y + targetNode.size.height / 2}
                stroke="hsl(var(--flux-500))"
                strokeWidth="2"
                strokeDasharray={connection.animated ? "5,5" : "none"}
                className={`opacity-60 ${connection.animated ? 'animate-pulse' : ''}`}
              />
            );
          })}
        </svg>

        {/* Nodes */}
        {nodes.map(node => (
          <CanvasNodeComponent
            key={node.id}
            node={node}
            isSelected={selectedNode === node.id}
            isConnecting={isConnecting}
            onConnect={handleNodeConnect}
          />
        ))}

      </div>

      {/* Connection Mode Indicator */}
      {isConnecting && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30">
          <div className="bg-flux-500 text-white px-4 py-2 rounded-lg shadow-lg">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 animate-pulse" />
              <span>Click another node to connect</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
