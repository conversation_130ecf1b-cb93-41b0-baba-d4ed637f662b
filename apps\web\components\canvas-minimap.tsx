'use client';

import { useRef, useEffect, useState } from 'react';
import { useCanvasNodes, useCanvasConnections, useCanvasViewport, useCanvasStore } from '@/lib/stores/canvas-store';

interface CanvasMinimapProps {
  width?: number;
  height?: number;
  className?: string;
}

export function CanvasMinimap({ 
  width = 200, 
  height = 150, 
  className = '' 
}: CanvasMinimapProps): React.JSX.Element {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  
  const nodes = useCanvasNodes();
  const connections = useCanvasConnections();
  const viewport = useCanvasViewport();
  const { setViewport } = useCanvasStore();

  // Calculate canvas bounds
  const getCanvasBounds = () => {
    if (nodes.length === 0) {
      return { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
    }

    const bounds = nodes.reduce(
      (acc, node) => ({
        minX: Math.min(acc.minX, node.position.x),
        minY: Math.min(acc.minY, node.position.y),
        maxX: Math.max(acc.maxX, node.position.x + node.size.width),
        maxY: Math.max(acc.maxY, node.position.y + node.size.height),
      }),
      { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity }
    );

    // Add padding
    const padding = 100;
    return {
      minX: bounds.minX - padding,
      minY: bounds.minY - padding,
      maxX: bounds.maxX + padding,
      maxY: bounds.maxY + padding,
    };
  };

  // Convert world coordinates to minimap coordinates
  const worldToMinimap = (worldX: number, worldY: number) => {
    const bounds = getCanvasBounds();
    const scaleX = width / (bounds.maxX - bounds.minX);
    const scaleY = height / (bounds.maxY - bounds.minY);
    
    return {
      x: (worldX - bounds.minX) * scaleX,
      y: (worldY - bounds.minY) * scaleY,
    };
  };

  // Convert minimap coordinates to world coordinates
  const minimapToWorld = (minimapX: number, minimapY: number) => {
    const bounds = getCanvasBounds();
    const scaleX = (bounds.maxX - bounds.minX) / width;
    const scaleY = (bounds.maxY - bounds.minY) / height;
    
    return {
      x: bounds.minX + minimapX * scaleX,
      y: bounds.minY + minimapY * scaleY,
    };
  };

  // Render minimap
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = width;
    canvas.height = height;

    // Clear canvas
    ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
    ctx.fillRect(0, 0, width, height);

    // Draw connections
    ctx.strokeStyle = 'rgba(139, 92, 246, 0.5)';
    ctx.lineWidth = 1;
    connections.forEach(connection => {
      const sourceNode = nodes.find(n => n.id === connection.sourceId);
      const targetNode = nodes.find(n => n.id === connection.targetId);
      
      if (sourceNode && targetNode) {
        const source = worldToMinimap(
          sourceNode.position.x + sourceNode.size.width / 2,
          sourceNode.position.y + sourceNode.size.height / 2
        );
        const target = worldToMinimap(
          targetNode.position.x + targetNode.size.width / 2,
          targetNode.position.y + targetNode.size.height / 2
        );
        
        ctx.beginPath();
        ctx.moveTo(source.x, source.y);
        ctx.lineTo(target.x, target.y);
        ctx.stroke();
      }
    });

    // Draw nodes
    nodes.forEach(node => {
      const pos = worldToMinimap(node.position.x, node.position.y);
      const size = {
        width: (node.size.width * width) / (getCanvasBounds().maxX - getCanvasBounds().minX),
        height: (node.size.height * height) / (getCanvasBounds().maxY - getCanvasBounds().minY),
      };

      // Node background
      ctx.fillStyle = getNodeColor(node.type, node.status);
      ctx.fillRect(pos.x, pos.y, Math.max(size.width, 4), Math.max(size.height, 4));

      // Node border
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
      ctx.lineWidth = 0.5;
      ctx.strokeRect(pos.x, pos.y, Math.max(size.width, 4), Math.max(size.height, 4));
    });

    // Draw viewport indicator
    const viewportBounds = getCanvasBounds();
    const viewportWidth = window.innerWidth / viewport.zoom;
    const viewportHeight = window.innerHeight / viewport.zoom;
    
    const viewportPos = worldToMinimap(-viewport.x, -viewport.y);
    const viewportSize = {
      width: (viewportWidth * width) / (viewportBounds.maxX - viewportBounds.minX),
      height: (viewportHeight * height) / (viewportBounds.maxY - viewportBounds.minY),
    };

    ctx.strokeStyle = 'rgba(217, 91, 60, 0.8)';
    ctx.lineWidth = 2;
    ctx.strokeRect(viewportPos.x, viewportPos.y, viewportSize.width, viewportSize.height);

    // Viewport fill
    ctx.fillStyle = 'rgba(217, 91, 60, 0.1)';
    ctx.fillRect(viewportPos.x, viewportPos.y, viewportSize.width, viewportSize.height);

  }, [nodes, connections, viewport, width, height]);

  const getNodeColor = (type: string, status: string) => {
    const baseColors = {
      persona: 'rgba(59, 130, 246, 0.8)',
      creative: 'rgba(139, 92, 246, 0.8)',
      channel: 'rgba(34, 197, 94, 0.8)',
      metric: 'rgba(249, 115, 22, 0.8)',
      agent: 'rgba(217, 91, 60, 0.8)',
      workflow: 'rgba(236, 72, 153, 0.8)',
    };

    const statusModifiers = {
      processing: 1.2,
      complete: 1.0,
      error: 0.8,
      warning: 1.1,
      idle: 0.9,
    };

    const baseColor = baseColors[type as keyof typeof baseColors] || 'rgba(156, 163, 175, 0.8)';
    const modifier = statusModifiers[status as keyof typeof statusModifiers] || 1.0;
    
    // Apply status modifier to opacity
    return baseColor.replace(/0\.\d+\)$/, `${Math.min(0.8 * modifier, 1.0)})`);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    handleMouseMove(e);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging && e.type !== 'mousedown') return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const worldPos = minimapToWorld(x, y);
    
    // Center the viewport on the clicked position
    const viewportWidth = window.innerWidth / viewport.zoom;
    const viewportHeight = window.innerHeight / viewport.zoom;
    
    setViewport({
      x: -(worldPos.x - viewportWidth / 2),
      y: -(worldPos.y - viewportHeight / 2),
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e: MouseEvent) => {
        handleMouseMove(e as any);
      };
      
      const handleGlobalMouseUp = () => {
        setIsDragging(false);
      };

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging]);

  return (
    <div className={`bg-card/80 backdrop-blur-sm border rounded-lg p-2 ${className}`}>
      <div className="text-xs font-medium text-muted-foreground mb-2">Canvas Overview</div>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="border rounded cursor-pointer hover:border-flux-500/50 transition-colors"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        style={{ width, height }}
      />
      <div className="mt-2 text-xs text-muted-foreground">
        <div className="flex justify-between">
          <span>Nodes: {nodes.length}</span>
          <span>Zoom: {Math.round(viewport.zoom * 100)}%</span>
        </div>
      </div>
    </div>
  );
}
