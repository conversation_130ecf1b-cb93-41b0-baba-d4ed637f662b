# Production Environment Template
# Copy this file to .env.production and fill in your production values

# Supabase Configuration (Production)
NEXT_PUBLIC_SUPABASE_URL=https://djijalhrsxxvcyuefrus.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEyNTE4MjcsImV4cCI6MjA2NjgyNzgyN30.b6TqzTcCfiNLI7tqLPYDui4WMJUCTF7lZ57WAmI4SaY
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqaWphbGhyc3h4dmN5dWVmcnVzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTI1MTgyNywiZXhwIjoyMDY2ODI3ODI3fQ.AZBAIrXlidFelf1JyteZ-8kfeIEGPfP1W2qVEMg-hxw

# Google AI Configuration
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=your_production_google_ai_api_key

# OAuth Configuration (Production)
GOOGLE_CLIENT_ID=your_production_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_production_google_oauth_client_secret
GITHUB_CLIENT_ID=your_production_github_oauth_client_id
GITHUB_CLIENT_SECRET=your_production_github_oauth_client_secret

# App Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# Database Configuration (Production)
DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres

# Agents Service Configuration
NEXT_PUBLIC_AGENTS_URL=https://your-agents-service.com

# Additional Production Settings
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_SECRET=your_nextauth_secret_key

# Analytics & Monitoring (Optional)
VERCEL_ANALYTICS_ID=your_vercel_analytics_id
SENTRY_DSN=your_sentry_dsn

# Rate Limiting & Security
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Image/Video Generation Services
IMAGEN_API_KEY=your_imagen_api_key
VEO_API_KEY=your_veo_api_key

# External Integrations
FACEBOOK_ACCESS_TOKEN=your_facebook_access_token
GOOGLE_ADS_DEVELOPER_TOKEN=your_google_ads_token
LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token
