// Shared types for agent communication
// This is a simplified version of the agents types for the web app

export interface AgentContext {
  campaignId: string;
  creativeId?: string;
  executionId: string;
  userId: string;
  organizationId: string;
  campaign: {
    id: string;
    name: string;
    description?: string;
    objectives?: Record<string, any>;
    channels?: string[];
  };
  personas: Array<{
    id: string;
    name: string;
    description?: string;
    demographics?: Record<string, any>;
    interests?: string[];
  }>;
  existingCreatives: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  objectives: Record<string, any>;
  constraints: Record<string, any>;
  metadata: Record<string, any>;
}

export interface CopyAgentInput {
  persona: {
    id: string;
    name: string;
    description?: string;
    demographics?: Record<string, any>;
    interests?: string[];
  };
  objective: string;
  tone: string;
  format: 'headline' | 'description' | 'cta' | 'full_copy';
  constraints?: {
    maxLength?: number;
    keywords?: string[];
    brandGuidelines?: string;
  };
}

export interface DesignAgentInput {
  copy: {
    headline?: string;
    description?: string;
    cta?: string;
  };
  style: string;
  format: 'image' | 'video' | 'carousel';
  dimensions: {
    width: number;
    height: number;
  };
  constraints?: {
    colorScheme?: string;
    brandColors?: string[];
    logoUrl?: string;
  };
}

export interface AnalystAgentInput {
  timeframe: string;
  metrics: string[];
  campaignId: string;
  creativeIds?: string[];
}

export interface AgentOutput {
  success: boolean;
  data: Record<string, any>;
  confidence: number;
  reasoning: string;
  suggestions?: string[];
  errors?: string[];
  metadata?: Record<string, any>;
}

export interface AgentExecutionResult {
  agentType: string;
  executionId: string;
  status: 'completed' | 'failed';
  input: any;
  output: AgentOutput;
  duration: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface WorkflowState {
  campaignId: string;
  executionId: string;
  currentStep: string;
  context: AgentContext;
  results: Record<string, AgentOutput | null>;
  errors: string[];
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
}
