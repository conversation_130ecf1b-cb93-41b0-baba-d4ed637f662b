version: '3.8'

services:
  # Supabase Local Development Stack
  supabase-db:
    image: supabase/postgres:*********
    container_name: metamorphic-flux-db
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "54322:5432"
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
    networks:
      - metamorphic-flux

  supabase-studio:
    image: supabase/studio:20240729-ce42139
    container_name: metamorphic-flux-studio
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_REST_URL: http://localhost:54321/rest/v1/
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
    ports:
      - "54323:3000"
    depends_on:
      - supabase-db
    networks:
      - metamorphic-flux

  supabase-kong:
    image: kong:2.8.1
    container_name: metamorphic-flux-kong
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
      KONG_NGINX_PROXY_PROXY_BUFFER_SIZE: 160k
      KONG_NGINX_PROXY_PROXY_BUFFERS: 64 160k
    ports:
      - "54321:8000"
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    depends_on:
      - supabase-db
    networks:
      - metamorphic-flux

  # Metamorphic Flux Web Application
  web-app:
    build:
      context: .
      target: development
    container_name: metamorphic-flux-web
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      - DATABASE_URL=***********************************************/postgres
      - NEXT_PUBLIC_AGENTS_URL=http://localhost:3001
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/web/node_modules
      - /app/apps/agents/node_modules
    depends_on:
      - supabase-kong
      - agents-service
    networks:
      - metamorphic-flux
    command: ["sh", "-c", "cd apps/web && pnpm dev"]

  # Metamorphic Flux Agents Service
  agents-service:
    build:
      context: .
      target: development
    container_name: metamorphic-flux-agents
    environment:
      - NODE_ENV=development
      - PORT=3001
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - DATABASE_URL=***********************************************/postgres
    ports:
      - "3001:3001"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/apps/agents/node_modules
    depends_on:
      - supabase-db
    networks:
      - metamorphic-flux
    command: ["sh", "-c", "cd apps/agents && pnpm dev"]

  # Redis for caching and session storage (optional)
  redis:
    image: redis:7-alpine
    container_name: metamorphic-flux-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - metamorphic-flux

  # Nginx reverse proxy (for production-like setup)
  nginx:
    image: nginx:alpine
    container_name: metamorphic-flux-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - web-app
      - agents-service
    networks:
      - metamorphic-flux
    profiles:
      - production

volumes:
  supabase_db_data:
  redis_data:

networks:
  metamorphic-flux:
    driver: bridge
