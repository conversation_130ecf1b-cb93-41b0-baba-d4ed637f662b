/**
 * Quantum optimization module
 * 
 * Provides quantum-enhanced budget optimization using Google Willow
 * quantum processor with advanced error correction and coherence.
 */

export { QuantumOptimizer } from './optimizer';
export { Willow<PERSON>ridge } from './willow-bridge';

export type {
  WillowConfig,
  QuantumCircuitConfig,
} from '../types';

export {
  QuantumOptimizationError,
  Willow<PERSON>IError,
} from '../types';

// Quantum algorithm implementations
export const QUANTUM_ALGORITHMS = {
  VQE: 'Variational Quantum Eigensolver',
  QAOA: 'Quantum Approximate Optimization Algorithm',
  QUANTUM_ANNEALING: 'Quantum Annealing',
  GROVER: 'Grover Search Algorithm',
  SHOR: 'Shor Factoring Algorithm',
} as const;

// Willow-specific constants
export const WILLOW_SPECS = {
  MAX_QUBITS: 105,
  COHERENCE_TIME: 100, // microseconds
  ERROR_RATE: 0.001,
  GATE_TIME: 0.1, // microseconds
  READOUT_FIDELITY: 0.999,
  TWO_QUBIT_GATE_FIDELITY: 0.995,
} as const;

export default {
  QuantumOptimizer,
  WillowBridge,
  QUAN<PERSON>M_ALGORITHMS,
  WILLOW_SPECS,
};
