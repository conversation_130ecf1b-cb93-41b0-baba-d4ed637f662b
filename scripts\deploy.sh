#!/bin/bash

# Metamorphic Flux Deployment Script
# This script handles the complete deployment of the Metamorphic Flux MVP

set -e

echo "🚀 Starting Metamorphic Flux Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v pnpm &> /dev/null; then
        print_error "PNPM is not installed"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "Git is not installed"
        exit 1
    fi
    
    print_success "All dependencies are installed"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    pnpm install
    print_success "Dependencies installed"
}

# Run type checking
type_check() {
    print_status "Running TypeScript type checking..."
    
    # Check web app
    cd apps/web
    if pnpm type-check; then
        print_success "Web app type checking passed"
    else
        print_error "Web app type checking failed"
        exit 1
    fi
    cd ../..
    
    # Check agents service
    cd apps/agents
    if pnpm type-check; then
        print_success "Agents service type checking passed"
    else
        print_warning "Agents service type checking failed (continuing anyway)"
    fi
    cd ../..
}

# Run tests
run_tests() {
    print_status "Running tests..."
    
    # Run web app tests
    cd apps/web
    if pnpm test; then
        print_success "Web app tests passed"
    else
        print_warning "Web app tests failed (continuing anyway)"
    fi
    cd ../..
    
    # Run agents tests
    cd apps/agents
    if pnpm test; then
        print_success "Agents service tests passed"
    else
        print_warning "Agents service tests failed (continuing anyway)"
    fi
    cd ../..
}

# Build applications
build_apps() {
    print_status "Building applications..."
    
    # Build web app
    cd apps/web
    if pnpm build; then
        print_success "Web app built successfully"
    else
        print_error "Web app build failed"
        exit 1
    fi
    cd ../..
    
    # Build agents service
    cd apps/agents
    if pnpm build; then
        print_success "Agents service built successfully"
    else
        print_error "Agents service build failed"
        exit 1
    fi
    cd ../..
}

# Check environment variables
check_env() {
    print_status "Checking environment variables..."
    
    if [ ! -f ".env.production" ]; then
        print_warning ".env.production not found, using .env.local"
        if [ ! -f ".env.local" ]; then
            print_error "No environment file found"
            exit 1
        fi
    fi
    
    print_success "Environment configuration found"
}

# Health check
health_check() {
    print_status "Running health checks..."
    
    # Check if Supabase is accessible
    if [ -n "$NEXT_PUBLIC_SUPABASE_URL" ]; then
        if curl -f "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/" > /dev/null 2>&1; then
            print_success "Supabase connection successful"
        else
            print_warning "Supabase connection failed"
        fi
    fi
    
    # Check if Google AI API key is set
    if [ -n "$GOOGLE_AI_API_KEY" ]; then
        print_success "Google AI API key configured"
    else
        print_warning "Google AI API key not configured"
    fi
}

# Main deployment function
deploy() {
    print_status "🚀 Starting deployment process..."
    
    check_dependencies
    install_dependencies
    check_env
    type_check
    run_tests
    build_apps
    health_check
    
    print_success "🎉 Deployment completed successfully!"
    print_status "Next steps:"
    echo "  1. Deploy to your hosting platform (Vercel, Netlify, etc.)"
    echo "  2. Set up your production database"
    echo "  3. Configure your domain and SSL"
    echo "  4. Set up monitoring and analytics"
}

# Parse command line arguments
case "${1:-deploy}" in
    "deps")
        check_dependencies
        install_dependencies
        ;;
    "check")
        type_check
        ;;
    "test")
        run_tests
        ;;
    "build")
        build_apps
        ;;
    "health")
        health_check
        ;;
    "deploy")
        deploy
        ;;
    *)
        echo "Usage: $0 [deps|check|test|build|health|deploy]"
        echo "  deps   - Install dependencies"
        echo "  check  - Run type checking"
        echo "  test   - Run tests"
        echo "  build  - Build applications"
        echo "  health - Run health checks"
        echo "  deploy - Full deployment (default)"
        exit 1
        ;;
esac
