import { defineConfig } from 'tsup';

export default defineConfig({
  entry: {
    index: 'src/index.ts',
    'quantum/index': 'src/quantum/index.ts',
    'classical/index': 'src/classical/index.ts',
  },
  format: ['cjs', 'esm'],
  dts: true,
  clean: true,
  splitting: false,
  sourcemap: true,
  minify: false,
  target: 'node18',
  external: ['axios', 'mathjs', 'ml-matrix', 'lodash'],
  tsconfig: './tsconfig.json',
});
