import { z } from 'zod';
import { BaseAgent } from './base';
import { 
  CopyAgentInputSchema, 
  AgentOutputSchema,
  type CopyAgentInput,
  type AgentOutput,
  type AgentContext,
  type AgentConfig 
} from '@/types';

export class CopyAgent extends BaseAgent {
  constructor() {
    const config: AgentConfig = {
      name: 'Copy Agent',
      type: 'copy',
      model: 'gemini-1.5-flash',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: `You are an expert copywriter specializing in AI-powered marketing campaigns. Your role is to create compelling, persuasive copy that resonates with specific target personas while achieving campaign objectives.

Key responsibilities:
- Generate headlines, descriptions, CTAs, and full copy based on persona insights
- Adapt tone and messaging to match target audience preferences
- Optimize for specific channels and formats
- Ensure brand consistency and compliance
- Maximize engagement and conversion potential

Always respond with valid JSON containing:
- headline: Main headline (if requested)
- description: Supporting description (if requested)  
- cta: Call-to-action (if requested)
- full_copy: Complete copy text (if requested)
- tone_analysis: Analysis of tone effectiveness
- persona_alignment: How well copy matches target persona
- optimization_suggestions: Recommendations for improvement
- confidence: Confidence score (0-1)
- reasoning: Explanation of creative decisions`,
    };
    super(config);
  }

  get inputSchema() {
    return CopyAgentInputSchema;
  }

  get outputSchema() {
    return AgentOutputSchema;
  }

  protected async processInput(input: CopyAgentInput, context: AgentContext): Promise<AgentOutput> {
    const { persona, objective, tone, format, constraints } = input;

    // Build context-aware prompt
    const personaContext = this.formatPersonaForCopy(persona);
    const campaignContext = this.formatCampaignContext(context.campaign);
    const constraintsText = this.formatConstraints(constraints);

    const userPrompt = `
Create ${format} copy for the following context:

CAMPAIGN CONTEXT:
${campaignContext}

TARGET PERSONA:
${personaContext}

OBJECTIVE: ${objective}
TONE: ${tone}
FORMAT: ${format}

${constraintsText}

REQUIREMENTS:
- Copy must resonate with the target persona's values and interests
- Tone should be ${tone} and appropriate for the audience
- Include persuasive elements that drive action
- Optimize for ${context.campaign.channels?.join(', ') || 'digital channels'}
- Ensure compliance with advertising standards

Please generate compelling copy and provide analysis of its effectiveness.

Respond with JSON in this exact format:
{
  "headline": "string (if format includes headline)",
  "description": "string (if format includes description)",
  "cta": "string (if format includes cta)",
  "full_copy": "string (if format is full_copy)",
  "tone_analysis": "Analysis of tone effectiveness",
  "persona_alignment": "How well copy matches target persona",
  "optimization_suggestions": ["suggestion1", "suggestion2"],
  "confidence": 0.85,
  "reasoning": "Explanation of creative decisions"
}`;

    try {
      const response = await this.callModel(this.config.systemPrompt, userPrompt, context);
      const parsedResponse = this.parseJsonResponse(response);

      // Calculate confidence based on various factors
      const confidence = this.calculateConfidence({
        inputQuality: this.assessInputQuality(input),
        outputCoherence: this.assessOutputCoherence(parsedResponse),
        alignmentWithObjectives: this.assessObjectiveAlignment(parsedResponse, objective),
        technicalCorrectness: this.assessTechnicalCorrectness(parsedResponse, format),
      });

      return {
        success: true,
        data: {
          headline: parsedResponse.headline,
          description: parsedResponse.description,
          cta: parsedResponse.cta,
          full_copy: parsedResponse.full_copy,
          tone_analysis: parsedResponse.tone_analysis,
          persona_alignment: parsedResponse.persona_alignment,
          format,
          tone,
          persona_name: persona.name,
        },
        confidence,
        reasoning: parsedResponse.reasoning || 'Copy generated based on persona insights and campaign objectives',
        suggestions: parsedResponse.optimization_suggestions || [],
        metadata: {
          persona_id: persona.id,
          objective,
          tone,
          format,
          constraints,
        },
      };

    } catch (error) {
      throw new Error(`Copy generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private formatPersonaForCopy(persona: any): string {
    const demographics = persona.demographics ? 
      Object.entries(persona.demographics).map(([k, v]) => `${k}: ${v}`).join(', ') : '';
    const psychographics = persona.psychographics ?
      Object.entries(persona.psychographics).map(([k, v]) => `${k}: ${v}`).join(', ') : '';
    const interests = persona.interests ? persona.interests.join(', ') : '';

    return `
Name: ${persona.name}
Description: ${persona.description || 'N/A'}
Demographics: ${demographics}
Psychographics: ${psychographics}
Interests: ${interests}
    `.trim();
  }

  private formatConstraints(constraints?: any): string {
    if (!constraints) return '';

    const parts = [];
    if (constraints.maxLength) {
      parts.push(`Maximum length: ${constraints.maxLength} characters`);
    }
    if (constraints.keywords?.length) {
      parts.push(`Include keywords: ${constraints.keywords.join(', ')}`);
    }
    if (constraints.brandGuidelines) {
      parts.push(`Brand guidelines: ${constraints.brandGuidelines}`);
    }

    return parts.length > 0 ? `\nCONSTRAINTS:\n${parts.join('\n')}` : '';
  }

  private assessInputQuality(input: CopyAgentInput): number {
    let score = 0.5; // Base score

    // Check persona completeness
    if (input.persona.description) score += 0.1;
    if (input.persona.demographics) score += 0.1;
    if (input.persona.interests?.length) score += 0.1;

    // Check objective clarity
    if (input.objective.length > 10) score += 0.1;

    // Check constraints
    if (input.constraints) score += 0.1;

    return Math.min(score, 1);
  }

  private assessOutputCoherence(output: any): number {
    let score = 0.5;

    // Check if required fields are present and non-empty
    const requiredFields = ['reasoning'];
    requiredFields.forEach(field => {
      if (output[field] && typeof output[field] === 'string' && output[field].length > 10) {
        score += 0.2;
      }
    });

    // Check for analysis fields
    if (output.tone_analysis) score += 0.15;
    if (output.persona_alignment) score += 0.15;

    return Math.min(score, 1);
  }

  private assessObjectiveAlignment(output: any, objective: string): number {
    // Simple keyword matching for now - could be enhanced with semantic analysis
    const objectiveLower = objective.toLowerCase();
    const outputText = JSON.stringify(output).toLowerCase();

    let score = 0.5;

    // Check for objective-related keywords in output
    if (objectiveLower.includes('awareness') && outputText.includes('awareness')) score += 0.2;
    if (objectiveLower.includes('conversion') && outputText.includes('conversion')) score += 0.2;
    if (objectiveLower.includes('engagement') && outputText.includes('engagement')) score += 0.2;

    return Math.min(score, 1);
  }

  private assessTechnicalCorrectness(output: any, format: string): number {
    let score = 0.5;

    // Check if format-specific fields are present
    switch (format) {
      case 'headline':
        if (output.headline && output.headline.length > 0) score += 0.5;
        break;
      case 'description':
        if (output.description && output.description.length > 0) score += 0.5;
        break;
      case 'cta':
        if (output.cta && output.cta.length > 0) score += 0.5;
        break;
      case 'full_copy':
        if (output.full_copy && output.full_copy.length > 0) score += 0.5;
        break;
    }

    return Math.min(score, 1);
  }
}
