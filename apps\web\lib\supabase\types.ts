export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      agent_logs: {
        Row: {
          agent_type: string
          campaign_id: string | null
          created_at: string
          creative_id: string | null
          duration_ms: number | null
          error_message: string | null
          execution_id: string
          id: string
          input_data: Json
          metrics: Json
          output_data: Json
          status: string
        }
        Insert: {
          agent_type: string
          campaign_id?: string | null
          created_at?: string
          creative_id?: string | null
          duration_ms?: number | null
          error_message?: string | null
          execution_id: string
          id?: string
          input_data?: Json
          metrics?: Json
          output_data?: Json
          status: string
        }
        Update: {
          agent_type?: string
          campaign_id?: string | null
          created_at?: string
          creative_id?: string | null
          duration_ms?: number | null
          error_message?: string | null
          execution_id?: string
          id?: string
          input_data?: Json
          metrics?: Json
          output_data?: Json
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_logs_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_logs_creative_id_fkey"
            columns: ["creative_id"]
            isOneToOne: false
            referencedRelation: "creatives"
            referencedColumns: ["id"]
          }
        ]
      }
      campaign_logs: {
        Row: {
          campaign_id: string
          channel: Database["public"]["Enums"]["channel_type"]
          creative_id: string | null
          event_data: Json
          event_type: string
          id: string
          ip_address: unknown | null
          metrics: Json
          session_id: string | null
          timestamp: string
          user_agent: string | null
        }
        Insert: {
          campaign_id: string
          channel: Database["public"]["Enums"]["channel_type"]
          creative_id?: string | null
          event_data?: Json
          event_type: string
          id?: string
          ip_address?: unknown | null
          metrics?: Json
          session_id?: string | null
          timestamp?: string
          user_agent?: string | null
        }
        Update: {
          campaign_id?: string
          channel?: Database["public"]["Enums"]["channel_type"]
          creative_id?: string | null
          event_data?: Json
          event_type?: string
          id?: string
          ip_address?: unknown | null
          metrics?: Json
          session_id?: string | null
          timestamp?: string
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_logs_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_logs_creative_id_fkey"
            columns: ["creative_id"]
            isOneToOne: false
            referencedRelation: "creatives"
            referencedColumns: ["id"]
          }
        ]
      }
      campaigns: {
        Row: {
          budget_daily: number | null
          budget_spent: number | null
          budget_total: number | null
          channels: Database["public"]["Enums"]["channel_type"][] | null
          created_at: string
          created_by: string | null
          description: string | null
          end_date: string | null
          id: string
          name: string
          objectives: Json
          organization_id: string
          performance_metrics: Json
          settings: Json
          start_date: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          target_personas: string[] | null
          updated_at: string
        }
        Insert: {
          budget_daily?: number | null
          budget_spent?: number | null
          budget_total?: number | null
          channels?: Database["public"]["Enums"]["channel_type"][] | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          end_date?: string | null
          id?: string
          name: string
          objectives?: Json
          organization_id: string
          performance_metrics?: Json
          settings?: Json
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_personas?: string[] | null
          updated_at?: string
        }
        Update: {
          budget_daily?: number | null
          budget_spent?: number | null
          budget_total?: number | null
          channels?: Database["public"]["Enums"]["channel_type"][] | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          end_date?: string | null
          id?: string
          name?: string
          objectives?: Json
          organization_id?: string
          performance_metrics?: Json
          settings?: Json
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_personas?: string[] | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaigns_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaigns_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      creatives: {
        Row: {
          a_b_test_group: string | null
          campaign_id: string
          content_url: string | null
          copy_cta: string | null
          copy_description: string | null
          copy_headline: string | null
          copy_text: string | null
          created_at: string
          created_by: string | null
          dimensions: Json
          duration: number | null
          embedding: string | null
          file_size: number | null
          id: string
          is_active: boolean | null
          metadata: Json
          name: string
          performance_metrics: Json
          thumbnail_url: string | null
          type: Database["public"]["Enums"]["creative_type"]
          updated_at: string
        }
        Insert: {
          a_b_test_group?: string | null
          campaign_id: string
          content_url?: string | null
          copy_cta?: string | null
          copy_description?: string | null
          copy_headline?: string | null
          copy_text?: string | null
          created_at?: string
          created_by?: string | null
          dimensions?: Json
          duration?: number | null
          embedding?: string | null
          file_size?: number | null
          id?: string
          is_active?: boolean | null
          metadata?: Json
          name: string
          performance_metrics?: Json
          thumbnail_url?: string | null
          type: Database["public"]["Enums"]["creative_type"]
          updated_at?: string
        }
        Update: {
          a_b_test_group?: string | null
          campaign_id?: string
          content_url?: string | null
          copy_cta?: string | null
          copy_description?: string | null
          copy_headline?: string | null
          copy_text?: string | null
          created_at?: string
          created_by?: string | null
          dimensions?: Json
          duration?: number | null
          embedding?: string | null
          file_size?: number | null
          id?: string
          is_active?: boolean | null
          metadata?: Json
          name?: string
          performance_metrics?: Json
          thumbnail_url?: string | null
          type?: Database["public"]["Enums"]["creative_type"]
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "creatives_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "creatives_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      optimization_jobs: {
        Row: {
          campaign_id: string
          completed_at: string | null
          created_at: string
          created_by: string | null
          error_message: string | null
          id: string
          input_data: Json
          output_data: Json
          started_at: string | null
          status: Database["public"]["Enums"]["optimization_status"] | null
          type: string
        }
        Insert: {
          campaign_id: string
          completed_at?: string | null
          created_at?: string
          created_by?: string | null
          error_message?: string | null
          id?: string
          input_data?: Json
          output_data?: Json
          started_at?: string | null
          status?: Database["public"]["Enums"]["optimization_status"] | null
          type: string
        }
        Update: {
          campaign_id?: string
          completed_at?: string | null
          created_at?: string
          created_by?: string | null
          error_message?: string | null
          id?: string
          input_data?: Json
          output_data?: Json
          started_at?: string | null
          status?: Database["public"]["Enums"]["optimization_status"] | null
          type?: string
        }
        Relationships: [
          {
            foreignKeyName: "optimization_jobs_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "optimization_jobs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      organization_members: {
        Row: {
          created_at: string
          id: string
          organization_id: string
          permissions: Json
          role: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          organization_id: string
          permissions?: Json
          role?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          organization_id?: string
          permissions?: Json
          role?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_members_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      organizations: {
        Row: {
          created_at: string
          description: string | null
          id: string
          logo_url: string | null
          name: string
          settings: Json
          slug: string
          updated_at: string
          website_url: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          logo_url?: string | null
          name: string
          settings?: Json
          slug: string
          updated_at?: string
          website_url?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          logo_url?: string | null
          name?: string
          settings?: Json
          slug?: string
          updated_at?: string
          website_url?: string | null
        }
        Relationships: []
      }
      personas: {
        Row: {
          behaviors: Json
          created_at: string
          created_by: string | null
          demographics: Json
          description: string | null
          embedding: string | null
          id: string
          interests: string[] | null
          name: string
          organization_id: string
          performance_metrics: Json
          psychographics: Json
          updated_at: string
        }
        Insert: {
          behaviors?: Json
          created_at?: string
          created_by?: string | null
          demographics?: Json
          description?: string | null
          embedding?: string | null
          id?: string
          interests?: string[] | null
          name: string
          organization_id: string
          performance_metrics?: Json
          psychographics?: Json
          updated_at?: string
        }
        Update: {
          behaviors?: Json
          created_at?: string
          created_by?: string | null
          demographics?: Json
          description?: string | null
          embedding?: string | null
          id?: string
          interests?: string[] | null
          name?: string
          organization_id?: string
          performance_metrics?: Json
          psychographics?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "personas_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "personas_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          api_credits: number | null
          avatar_url: string | null
          company_name: string | null
          created_at: string
          email: string
          full_name: string | null
          id: string
          role: string | null
          subscription_tier: string | null
          updated_at: string
        }
        Insert: {
          api_credits?: number | null
          avatar_url?: string | null
          company_name?: string | null
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          role?: string | null
          subscription_tier?: string | null
          updated_at?: string
        }
        Update: {
          api_credits?: number | null
          avatar_url?: string | null
          company_name?: string | null
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          role?: string | null
          subscription_tier?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "users_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_organization_ids: {
        Args: {
          user_uuid: string
        }
        Returns: string[]
      }
    }
    Enums: {
      campaign_status: "draft" | "active" | "paused" | "completed" | "archived"
      channel_type: "facebook" | "instagram" | "google" | "tiktok" | "linkedin" | "email" | "push"
      creative_type: "image" | "video" | "text" | "carousel" | "story"
      optimization_status: "pending" | "running" | "completed" | "failed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
