/**
 * Canvas UI Integration Tests
 * Tests all Canvas UI components and their interactions
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useCanvasStore } from '@/lib/stores/canvas-store';
import { CampaignCanvas } from '@/components/campaign-canvas';
import { CanvasToolbar } from '@/components/canvas-toolbar';
import { CanvasNodeComponent } from '@/components/canvas-node';
import { CanvasMinimap } from '@/components/canvas-minimap';
import { CanvasSettings } from '@/components/canvas-settings';

// Mock GSAP
vi.mock('gsap', () => ({
  gsap: {
    registerPlugin: vi.fn(),
    to: vi.fn(),
    fromTo: vi.fn(),
    set: vi.fn(),
  },
  Draggable: {
    create: vi.fn(() => [{ kill: vi.fn() }]),
  },
}));

// Mock hooks
vi.mock('@/hooks/use-agents', () => ({
  useAgents: () => ({
    workflow: { execute: vi.fn(), isLoading: false },
    areAgentsHealthy: () => true,
  }),
}));

describe('Canvas UI Components', () => {
  beforeEach(() => {
    // Reset Zustand store
    useCanvasStore.getState().clearCanvas();
  });

  describe('Canvas Store', () => {
    it('should initialize with default state', () => {
      const state = useCanvasStore.getState();
      expect(state.nodes).toEqual([]);
      expect(state.connections).toEqual([]);
      expect(state.selectedNodes).toEqual([]);
      expect(state.isPlaying).toBe(false);
    });

    it('should add nodes correctly', () => {
      const { addNode } = useCanvasStore.getState();
      
      addNode({
        type: 'persona',
        title: 'Test Persona',
        status: 'idle',
        position: { x: 100, y: 100 },
        size: { width: 120, height: 80 },
        connections: [],
      });

      const state = useCanvasStore.getState();
      expect(state.nodes).toHaveLength(1);
      expect(state.nodes[0].title).toBe('Test Persona');
      expect(state.nodes[0].type).toBe('persona');
    });

    it('should update node properties', () => {
      const { addNode, updateNode } = useCanvasStore.getState();
      
      addNode({
        type: 'agent',
        title: 'Test Agent',
        status: 'idle',
        position: { x: 200, y: 200 },
        size: { width: 120, height: 80 },
        connections: [],
      });

      const nodeId = useCanvasStore.getState().nodes[0].id;
      updateNode(nodeId, { status: 'processing', title: 'Updated Agent' });

      const state = useCanvasStore.getState();
      expect(state.nodes[0].status).toBe('processing');
      expect(state.nodes[0].title).toBe('Updated Agent');
    });

    it('should handle connections correctly', () => {
      const { addNode, addConnection } = useCanvasStore.getState();
      
      // Add two nodes
      addNode({
        type: 'persona',
        title: 'Node 1',
        status: 'idle',
        position: { x: 100, y: 100 },
        size: { width: 120, height: 80 },
        connections: [],
      });
      
      addNode({
        type: 'agent',
        title: 'Node 2',
        status: 'idle',
        position: { x: 300, y: 100 },
        size: { width: 120, height: 80 },
        connections: [],
      });

      const nodes = useCanvasStore.getState().nodes;
      
      addConnection({
        sourceId: nodes[0].id,
        targetId: nodes[1].id,
        type: 'workflow',
        status: 'active',
      });

      const state = useCanvasStore.getState();
      expect(state.connections).toHaveLength(1);
      expect(state.connections[0].sourceId).toBe(nodes[0].id);
      expect(state.connections[0].targetId).toBe(nodes[1].id);
    });

    it('should handle selection correctly', () => {
      const { addNode, selectNode } = useCanvasStore.getState();
      
      addNode({
        type: 'creative',
        title: 'Test Creative',
        status: 'idle',
        position: { x: 150, y: 150 },
        size: { width: 120, height: 80 },
        connections: [],
      });

      const nodeId = useCanvasStore.getState().nodes[0].id;
      selectNode(nodeId);

      const state = useCanvasStore.getState();
      expect(state.selectedNodes).toContain(nodeId);
    });

    it('should handle viewport changes', () => {
      const { setViewport } = useCanvasStore.getState();
      
      setViewport({ x: 100, y: 200, zoom: 1.5 });

      const state = useCanvasStore.getState();
      expect(state.viewport.x).toBe(100);
      expect(state.viewport.y).toBe(200);
      expect(state.viewport.zoom).toBe(1.5);
    });
  });

  describe('Canvas Toolbar', () => {
    it('should render all toolbar buttons', () => {
      render(<CanvasToolbar />);
      
      expect(screen.getByTitle('Start Workflow')).toBeInTheDocument();
      expect(screen.getByTitle('Undo (Ctrl+Z)')).toBeInTheDocument();
      expect(screen.getByTitle('Redo (Ctrl+Y)')).toBeInTheDocument();
      expect(screen.getByTitle('Zoom In (+)')).toBeInTheDocument();
      expect(screen.getByTitle('Zoom Out (-)')).toBeInTheDocument();
    });

    it('should handle workflow start/stop', () => {
      render(<CanvasToolbar />);
      
      const startButton = screen.getByTitle('Start Workflow');
      fireEvent.click(startButton);

      const state = useCanvasStore.getState();
      expect(state.isPlaying).toBe(true);
    });

    it('should handle zoom controls', () => {
      render(<CanvasToolbar />);
      
      const zoomInButton = screen.getByTitle('Zoom In (+)');
      const initialZoom = useCanvasStore.getState().viewport.zoom;
      
      fireEvent.click(zoomInButton);
      
      const newZoom = useCanvasStore.getState().viewport.zoom;
      expect(newZoom).toBeGreaterThan(initialZoom);
    });
  });

  describe('Canvas Node Component', () => {
    const mockNode = {
      id: 'test-node',
      type: 'persona' as const,
      title: 'Test Node',
      subtitle: 'Test Description',
      status: 'idle' as const,
      position: { x: 100, y: 100 },
      size: { width: 120, height: 80 },
      connections: [],
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1,
      },
    };

    it('should render node with correct content', () => {
      render(<CanvasNodeComponent node={mockNode} />);
      
      expect(screen.getByText('Test Node')).toBeInTheDocument();
      expect(screen.getByText('Test Description')).toBeInTheDocument();
    });

    it('should handle node selection', () => {
      const onConnect = vi.fn();
      render(<CanvasNodeComponent node={mockNode} onConnect={onConnect} />);
      
      const nodeElement = screen.getByText('Test Node').closest('div');
      fireEvent.click(nodeElement!);

      const state = useCanvasStore.getState();
      expect(state.selectedNodes).toContain(mockNode.id);
    });

    it('should show context menu on right click', () => {
      render(<CanvasNodeComponent node={mockNode} />);
      
      const nodeElement = screen.getByText('Test Node').closest('div');
      fireEvent.contextMenu(nodeElement!);

      expect(screen.getByText('Duplicate')).toBeInTheDocument();
      expect(screen.getByText('Configure')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });
  });

  describe('Canvas Settings', () => {
    it('should render settings panel when open', () => {
      const onClose = vi.fn();
      render(<CanvasSettings isOpen={true} onClose={onClose} />);
      
      expect(screen.getByText('Canvas Settings')).toBeInTheDocument();
      expect(screen.getByText('Appearance')).toBeInTheDocument();
      expect(screen.getByText('Grid & Snapping')).toBeInTheDocument();
      expect(screen.getByText('Animations')).toBeInTheDocument();
    });

    it('should not render when closed', () => {
      const onClose = vi.fn();
      render(<CanvasSettings isOpen={false} onClose={onClose} />);
      
      expect(screen.queryByText('Canvas Settings')).not.toBeInTheDocument();
    });

    it('should handle settings changes', () => {
      const onClose = vi.fn();
      render(<CanvasSettings isOpen={true} onClose={onClose} />);
      
      const gridToggle = screen.getByText('Show Grid').parentElement?.querySelector('button');
      fireEvent.click(gridToggle!);

      // Settings should be updated when saved
      const saveButton = screen.getByText('Save Settings');
      fireEvent.click(saveButton);

      expect(onClose).toHaveBeenCalled();
    });
  });

  describe('Campaign Canvas Integration', () => {
    it('should render campaign canvas with all components', () => {
      render(<CampaignCanvas campaignId="test-campaign" />);
      
      // Should have flux canvas background
      expect(document.querySelector('canvas')).toBeInTheDocument();
      
      // Should initialize with default nodes
      waitFor(() => {
        const state = useCanvasStore.getState();
        expect(state.nodes.length).toBeGreaterThan(0);
      });
    });

    it('should handle fullscreen toggle', () => {
      const onToggleFullscreen = vi.fn();
      render(
        <CampaignCanvas 
          campaignId="test-campaign" 
          isFullscreen={false}
          onToggleFullscreen={onToggleFullscreen}
        />
      );
      
      const fullscreenButton = screen.getByRole('button');
      fireEvent.click(fullscreenButton);
      
      expect(onToggleFullscreen).toHaveBeenCalled();
    });
  });

  describe('Canvas Performance', () => {
    it('should handle large numbers of nodes efficiently', () => {
      const { addNode } = useCanvasStore.getState();
      
      // Add 100 nodes
      for (let i = 0; i < 100; i++) {
        addNode({
          type: 'agent',
          title: `Node ${i}`,
          status: 'idle',
          position: { x: i * 10, y: i * 10 },
          size: { width: 120, height: 80 },
          connections: [],
        });
      }

      const state = useCanvasStore.getState();
      expect(state.nodes).toHaveLength(100);
      expect(state.metrics.nodeCount).toBe(100);
    });

    it('should update metrics correctly', () => {
      const { updateMetrics } = useCanvasStore.getState();
      
      updateMetrics({ fps: 45, renderTime: 16.7 });

      const state = useCanvasStore.getState();
      expect(state.metrics.fps).toBe(45);
      expect(state.metrics.renderTime).toBe(16.7);
    });
  });

  describe('Canvas History', () => {
    it('should save and restore history', () => {
      const { addNode, saveToHistory, undo } = useCanvasStore.getState();
      
      // Save initial state
      saveToHistory();
      
      // Add a node
      addNode({
        type: 'metric',
        title: 'Test Metric',
        status: 'idle',
        position: { x: 250, y: 250 },
        size: { width: 120, height: 80 },
        connections: [],
      });

      expect(useCanvasStore.getState().nodes).toHaveLength(1);
      
      // Undo should restore previous state
      undo();
      
      expect(useCanvasStore.getState().nodes).toHaveLength(0);
    });
  });
});

// Export test utilities for other test files
export const createMockNode = (overrides = {}) => ({
  id: 'mock-node',
  type: 'agent' as const,
  title: 'Mock Node',
  status: 'idle' as const,
  position: { x: 0, y: 0 },
  size: { width: 120, height: 80 },
  connections: [],
  metadata: {
    createdAt: new Date(),
    updatedAt: new Date(),
    version: 1,
  },
  ...overrides,
});

export const setupCanvasTest = () => {
  beforeEach(() => {
    useCanvasStore.getState().clearCanvas();
  });
};
