"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+postgrest-js@1.19.4";
exports.ids = ["vendor-chunks/@supabase+postgrest-js@1.19.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js":
/*!*******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js ***!
  \*******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// @ts-ignore\nconst node_fetch_1 = __importDefault(__webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\"));\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nclass PostgrestBuilder {\n    constructor(builder) {\n        this.shouldThrowOnError = false;\n        this.method = builder.method;\n        this.url = builder.url;\n        this.headers = builder.headers;\n        this.schema = builder.schema;\n        this.body = builder.body;\n        this.shouldThrowOnError = builder.shouldThrowOnError;\n        this.signal = builder.signal;\n        this.isMaybeSingle = builder.isMaybeSingle;\n        if (builder.fetch) {\n            this.fetch = builder.fetch;\n        }\n        else if (typeof fetch === 'undefined') {\n            this.fetch = node_fetch_1.default;\n        }\n        else {\n            this.fetch = fetch;\n        }\n    }\n    /**\n     * If there's an error with the query, throwOnError will reject the promise by\n     * throwing the error instead of returning it as part of a successful response.\n     *\n     * {@link https://github.com/supabase/supabase-js/issues/92}\n     */\n    throwOnError() {\n        this.shouldThrowOnError = true;\n        return this;\n    }\n    /**\n     * Set an HTTP header for the request.\n     */\n    setHeader(name, value) {\n        this.headers = Object.assign({}, this.headers);\n        this.headers[name] = value;\n        return this;\n    }\n    then(onfulfilled, onrejected) {\n        // https://postgrest.org/en/stable/api.html#switching-schemas\n        if (this.schema === undefined) {\n            // skip\n        }\n        else if (['GET', 'HEAD'].includes(this.method)) {\n            this.headers['Accept-Profile'] = this.schema;\n        }\n        else {\n            this.headers['Content-Profile'] = this.schema;\n        }\n        if (this.method !== 'GET' && this.method !== 'HEAD') {\n            this.headers['Content-Type'] = 'application/json';\n        }\n        // NOTE: Invoke w/o `this` to avoid illegal invocation error.\n        // https://github.com/supabase/postgrest-js/pull/247\n        const _fetch = this.fetch;\n        let res = _fetch(this.url.toString(), {\n            method: this.method,\n            headers: this.headers,\n            body: JSON.stringify(this.body),\n            signal: this.signal,\n        }).then(async (res) => {\n            var _a, _b, _c;\n            let error = null;\n            let data = null;\n            let count = null;\n            let status = res.status;\n            let statusText = res.statusText;\n            if (res.ok) {\n                if (this.method !== 'HEAD') {\n                    const body = await res.text();\n                    if (body === '') {\n                        // Prefer: return=minimal\n                    }\n                    else if (this.headers['Accept'] === 'text/csv') {\n                        data = body;\n                    }\n                    else if (this.headers['Accept'] &&\n                        this.headers['Accept'].includes('application/vnd.pgrst.plan+text')) {\n                        data = body;\n                    }\n                    else {\n                        data = JSON.parse(body);\n                    }\n                }\n                const countHeader = (_a = this.headers['Prefer']) === null || _a === void 0 ? void 0 : _a.match(/count=(exact|planned|estimated)/);\n                const contentRange = (_b = res.headers.get('content-range')) === null || _b === void 0 ? void 0 : _b.split('/');\n                if (countHeader && contentRange && contentRange.length > 1) {\n                    count = parseInt(contentRange[1]);\n                }\n                // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n                // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n                if (this.isMaybeSingle && this.method === 'GET' && Array.isArray(data)) {\n                    if (data.length > 1) {\n                        error = {\n                            // https://github.com/PostgREST/postgrest/blob/a867d79c42419af16c18c3fb019eba8df992626f/src/PostgREST/Error.hs#L553\n                            code: 'PGRST116',\n                            details: `Results contain ${data.length} rows, application/vnd.pgrst.object+json requires 1 row`,\n                            hint: null,\n                            message: 'JSON object requested, multiple (or no) rows returned',\n                        };\n                        data = null;\n                        count = null;\n                        status = 406;\n                        statusText = 'Not Acceptable';\n                    }\n                    else if (data.length === 1) {\n                        data = data[0];\n                    }\n                    else {\n                        data = null;\n                    }\n                }\n            }\n            else {\n                const body = await res.text();\n                try {\n                    error = JSON.parse(body);\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (Array.isArray(error) && res.status === 404) {\n                        data = [];\n                        error = null;\n                        status = 200;\n                        statusText = 'OK';\n                    }\n                }\n                catch (_d) {\n                    // Workaround for https://github.com/supabase/postgrest-js/issues/295\n                    if (res.status === 404 && body === '') {\n                        status = 204;\n                        statusText = 'No Content';\n                    }\n                    else {\n                        error = {\n                            message: body,\n                        };\n                    }\n                }\n                if (error && this.isMaybeSingle && ((_c = error === null || error === void 0 ? void 0 : error.details) === null || _c === void 0 ? void 0 : _c.includes('0 rows'))) {\n                    error = null;\n                    status = 200;\n                    statusText = 'OK';\n                }\n                if (error && this.shouldThrowOnError) {\n                    throw new PostgrestError_1.default(error);\n                }\n            }\n            const postgrestResponse = {\n                error,\n                data,\n                count,\n                status,\n                statusText,\n            };\n            return postgrestResponse;\n        });\n        if (!this.shouldThrowOnError) {\n            res = res.catch((fetchError) => {\n                var _a, _b, _c;\n                return ({\n                    error: {\n                        message: `${(_a = fetchError === null || fetchError === void 0 ? void 0 : fetchError.name) !== null && _a !== void 0 ? _a : 'FetchError'}: ${fetchError === null || fetchError === void 0 ? void 0 : fetchError.message}`,\n                        details: `${(_b = fetchError === null || fetchError === void 0 ? void 0 : fetchError.stack) !== null && _b !== void 0 ? _b : ''}`,\n                        hint: '',\n                        code: `${(_c = fetchError === null || fetchError === void 0 ? void 0 : fetchError.code) !== null && _c !== void 0 ? _c : ''}`,\n                    },\n                    data: null,\n                    count: null,\n                    status: 0,\n                    statusText: '',\n                });\n            });\n        }\n        return res.then(onfulfilled, onrejected);\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        /* istanbul ignore next */\n        return this;\n    }\n    /**\n     * Override the type of the returned `data` field in the response.\n     *\n     * @typeParam NewResult - The new type to cast the response data to\n     * @typeParam Options - Optional type configuration (defaults to { merge: true })\n     * @typeParam Options.merge - When true, merges the new type with existing return type. When false, replaces the existing types entirely (defaults to true)\n     * @example\n     * ```typescript\n     * // Merge with existing types (default behavior)\n     * const query = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ custom_field: string }>()\n     *\n     * // Replace existing types completely\n     * const replaceQuery = supabase\n     *   .from('users')\n     *   .select()\n     *   .overrideTypes<{ id: number; name: string }, { merge: false }>()\n     * ```\n     * @returns A PostgrestBuilder instance with the new type\n     */\n    overrideTypes() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestBuilder;\n//# sourceMappingURL=PostgrestBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js":
/*!******************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js ***!
  \******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nconst constants_1 = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\");\n/**\n * PostgREST client.\n *\n * @typeParam Database - Types for the schema from the [type\n * generator](https://supabase.com/docs/reference/javascript/next/typescript-support)\n *\n * @typeParam SchemaName - Postgres schema to switch to. Must be a string\n * literal, the same one passed to the constructor. If the schema is not\n * `\"public\"`, this must be supplied manually.\n */\nclass PostgrestClient {\n    // TODO: Add back shouldThrowOnError once we figure out the typings\n    /**\n     * Creates a PostgREST client.\n     *\n     * @param url - URL of the PostgREST endpoint\n     * @param options - Named parameters\n     * @param options.headers - Custom headers\n     * @param options.schema - Postgres schema to switch to\n     * @param options.fetch - Custom fetch\n     */\n    constructor(url, { headers = {}, schema, fetch, } = {}) {\n        this.url = url;\n        this.headers = Object.assign(Object.assign({}, constants_1.DEFAULT_HEADERS), headers);\n        this.schemaName = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        const url = new URL(`${this.url}/${relation}`);\n        return new PostgrestQueryBuilder_1.default(url, {\n            headers: Object.assign({}, this.headers),\n            schema: this.schemaName,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return new PostgrestClient(this.url, {\n            headers: this.headers,\n            schema,\n            fetch: this.fetch,\n        });\n    }\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, { head = false, get = false, count, } = {}) {\n        let method;\n        const url = new URL(`${this.url}/rpc/${fn}`);\n        let body;\n        if (head || get) {\n            method = head ? 'HEAD' : 'GET';\n            Object.entries(args)\n                // params with undefined value needs to be filtered out, otherwise it'll\n                // show up as `?param=undefined`\n                .filter(([_, value]) => value !== undefined)\n                // array values need special syntax\n                .map(([name, value]) => [name, Array.isArray(value) ? `{${value.join(',')}}` : `${value}`])\n                .forEach(([name, value]) => {\n                url.searchParams.append(name, value);\n            });\n        }\n        else {\n            method = 'POST';\n            body = args;\n        }\n        const headers = Object.assign({}, this.headers);\n        if (count) {\n            headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url,\n            headers,\n            schema: this.schemaName,\n            body,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports[\"default\"] = PostgrestClient;\n//# sourceMappingURL=PostgrestClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js":
/*!*****************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Error format\n *\n * {@link https://postgrest.org/en/stable/api.html?highlight=options#errors-and-http-status-codes}\n */\nclass PostgrestError extends Error {\n    constructor(context) {\n        super(context.message);\n        this.name = 'PostgrestError';\n        this.details = context.details;\n        this.hint = context.hint;\n        this.code = context.code;\n    }\n}\nexports[\"default\"] = PostgrestError;\n//# sourceMappingURL=PostgrestError.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL1Bvc3RncmVzdEVycm9yLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFlO0FBQ2YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmVkcGFuZGFcXERlc2t0b3BcXE1ldGFtb3JwaGljIGZsdXhcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxccG9zdGdyZXN0LWpzXFxkaXN0XFxjanNcXFBvc3RncmVzdEVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuLyoqXG4gKiBFcnJvciBmb3JtYXRcbiAqXG4gKiB7QGxpbmsgaHR0cHM6Ly9wb3N0Z3Jlc3Qub3JnL2VuL3N0YWJsZS9hcGkuaHRtbD9oaWdobGlnaHQ9b3B0aW9ucyNlcnJvcnMtYW5kLWh0dHAtc3RhdHVzLWNvZGVzfVxuICovXG5jbGFzcyBQb3N0Z3Jlc3RFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcihjb250ZXh0KSB7XG4gICAgICAgIHN1cGVyKGNvbnRleHQubWVzc2FnZSk7XG4gICAgICAgIHRoaXMubmFtZSA9ICdQb3N0Z3Jlc3RFcnJvcic7XG4gICAgICAgIHRoaXMuZGV0YWlscyA9IGNvbnRleHQuZGV0YWlscztcbiAgICAgICAgdGhpcy5oaW50ID0gY29udGV4dC5oaW50O1xuICAgICAgICB0aGlzLmNvZGUgPSBjb250ZXh0LmNvZGU7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gUG9zdGdyZXN0RXJyb3I7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1Qb3N0Z3Jlc3RFcnJvci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js":
/*!*************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js ***!
  \*************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nclass PostgrestFilterBuilder extends PostgrestTransformBuilder_1.default {\n    /**\n     * Match only rows where `column` is equal to `value`.\n     *\n     * To check if the value of `column` is NULL, you should use `.is()` instead.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    eq(column, value) {\n        this.url.searchParams.append(column, `eq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is not equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    neq(column, value) {\n        this.url.searchParams.append(column, `neq.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gt(column, value) {\n        this.url.searchParams.append(column, `gt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is greater than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    gte(column, value) {\n        this.url.searchParams.append(column, `gte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lt(column, value) {\n        this.url.searchParams.append(column, `lt.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is less than or equal to `value`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    lte(column, value) {\n        this.url.searchParams.append(column, `lte.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    like(column, pattern) {\n        this.url.searchParams.append(column, `like.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `like(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-sensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    likeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `like(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches `pattern` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param pattern - The pattern to match with\n     */\n    ilike(column, pattern) {\n        this.url.searchParams.append(column, `ilike.${pattern}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches all of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAllOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(all).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` matches any of `patterns` case-insensitively.\n     *\n     * @param column - The column to filter on\n     * @param patterns - The patterns to match with\n     */\n    ilikeAnyOf(column, patterns) {\n        this.url.searchParams.append(column, `ilike(any).{${patterns.join(',')}}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` IS `value`.\n     *\n     * For non-boolean columns, this is only relevant for checking if the value of\n     * `column` is NULL by setting `value` to `null`.\n     *\n     * For boolean columns, you can also set `value` to `true` or `false` and it\n     * will behave the same way as `.eq()`.\n     *\n     * @param column - The column to filter on\n     * @param value - The value to filter with\n     */\n    is(column, value) {\n        this.url.searchParams.append(column, `is.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows where `column` is included in the `values` array.\n     *\n     * @param column - The column to filter on\n     * @param values - The values array to filter with\n     */\n    in(column, values) {\n        const cleanedValues = Array.from(new Set(values))\n            .map((s) => {\n            // handle postgrest reserved characters\n            // https://postgrest.org/en/v7.0.0/api.html#reserved-characters\n            if (typeof s === 'string' && new RegExp('[,()]').test(s))\n                return `\"${s}\"`;\n            else\n                return `${s}`;\n        })\n            .join(',');\n        this.url.searchParams.append(column, `in.(${cleanedValues})`);\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * `column` contains every element appearing in `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    contains(column, value) {\n        if (typeof value === 'string') {\n            // range types can be inclusive '[', ']' or exclusive '(', ')' so just\n            // keep it simple and accept a string\n            this.url.searchParams.append(column, `cs.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cs.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cs.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for jsonb, array, and range columns. Match only rows where\n     * every element appearing in `column` is contained by `value`.\n     *\n     * @param column - The jsonb, array, or range column to filter on\n     * @param value - The jsonb, array, or range value to filter with\n     */\n    containedBy(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `cd.${value}`);\n        }\n        else if (Array.isArray(value)) {\n            // array\n            this.url.searchParams.append(column, `cd.{${value.join(',')}}`);\n        }\n        else {\n            // json\n            this.url.searchParams.append(column, `cd.${JSON.stringify(value)}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is greater than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGt(column, range) {\n        this.url.searchParams.append(column, `sr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or greater than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeGte(column, range) {\n        this.url.searchParams.append(column, `nxl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is less than any element in `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLt(column, range) {\n        this.url.searchParams.append(column, `sl.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where every element in\n     * `column` is either contained in `range` or less than any element in\n     * `range`.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeLte(column, range) {\n        this.url.searchParams.append(column, `nxr.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for range columns. Match only rows where `column` is\n     * mutually exclusive to `range` and there can be no element between the two\n     * ranges.\n     *\n     * @param column - The range column to filter on\n     * @param range - The range to filter with\n     */\n    rangeAdjacent(column, range) {\n        this.url.searchParams.append(column, `adj.${range}`);\n        return this;\n    }\n    /**\n     * Only relevant for array and range columns. Match only rows where\n     * `column` and `value` have an element in common.\n     *\n     * @param column - The array or range column to filter on\n     * @param value - The array or range value to filter with\n     */\n    overlaps(column, value) {\n        if (typeof value === 'string') {\n            // range\n            this.url.searchParams.append(column, `ov.${value}`);\n        }\n        else {\n            // array\n            this.url.searchParams.append(column, `ov.{${value.join(',')}}`);\n        }\n        return this;\n    }\n    /**\n     * Only relevant for text and tsvector columns. Match only rows where\n     * `column` matches the query string in `query`.\n     *\n     * @param column - The text or tsvector column to filter on\n     * @param query - The query text to match with\n     * @param options - Named parameters\n     * @param options.config - The text search configuration to use\n     * @param options.type - Change how the `query` text is interpreted\n     */\n    textSearch(column, query, { config, type } = {}) {\n        let typePart = '';\n        if (type === 'plain') {\n            typePart = 'pl';\n        }\n        else if (type === 'phrase') {\n            typePart = 'ph';\n        }\n        else if (type === 'websearch') {\n            typePart = 'w';\n        }\n        const configPart = config === undefined ? '' : `(${config})`;\n        this.url.searchParams.append(column, `${typePart}fts${configPart}.${query}`);\n        return this;\n    }\n    /**\n     * Match only rows where each column in `query` keys is equal to its\n     * associated value. Shorthand for multiple `.eq()`s.\n     *\n     * @param query - The object to filter with, with column names as keys mapped\n     * to their filter values\n     */\n    match(query) {\n        Object.entries(query).forEach(([column, value]) => {\n            this.url.searchParams.append(column, `eq.${value}`);\n        });\n        return this;\n    }\n    /**\n     * Match only rows which doesn't satisfy the filter.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to be negated to filter with, following\n     * PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    not(column, operator, value) {\n        this.url.searchParams.append(column, `not.${operator}.${value}`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy at least one of the filters.\n     *\n     * Unlike most filters, `filters` is used as-is and needs to follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure it's properly sanitized.\n     *\n     * It's currently not possible to do an `.or()` filter across multiple tables.\n     *\n     * @param filters - The filters to use, following PostgREST syntax\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to filter on referenced tables\n     * instead of the parent table\n     * @param options.foreignTable - Deprecated, use `referencedTable` instead\n     */\n    or(filters, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.or` : 'or';\n        this.url.searchParams.append(key, `(${filters})`);\n        return this;\n    }\n    /**\n     * Match only rows which satisfy the filter. This is an escape hatch - you\n     * should use the specific filter methods wherever possible.\n     *\n     * Unlike most filters, `opearator` and `value` are used as-is and need to\n     * follow [PostgREST\n     * syntax](https://postgrest.org/en/stable/api.html#operators). You also need\n     * to make sure they are properly sanitized.\n     *\n     * @param column - The column to filter on\n     * @param operator - The operator to filter with, following PostgREST syntax\n     * @param value - The value to filter with, following PostgREST syntax\n     */\n    filter(column, operator, value) {\n        this.url.searchParams.append(column, `${operator}.${value}`);\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestFilterBuilder;\n//# sourceMappingURL=PostgrestFilterBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js":
/*!************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js ***!
  \************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nclass PostgrestQueryBuilder {\n    constructor(url, { headers = {}, schema, fetch, }) {\n        this.url = url;\n        this.headers = headers;\n        this.schema = schema;\n        this.fetch = fetch;\n    }\n    /**\n     * Perform a SELECT query on the table or view.\n     *\n     * @param columns - The columns to retrieve, separated by commas. Columns can be renamed when returned with `customName:columnName`\n     *\n     * @param options - Named parameters\n     *\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     *\n     * @param options.count - Count algorithm to use to count rows in the table or view.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    select(columns, { head = false, count, } = {}) {\n        const method = head ? 'HEAD' : 'GET';\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (count) {\n            this.headers['Prefer'] = `count=${count}`;\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an INSERT into the table or view.\n     *\n     * By default, inserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to insert. Pass an object to insert a single row\n     * or an array to insert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count inserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. Only applies for bulk\n     * inserts.\n     */\n    insert(values, { count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPSERT on the table or view. Depending on the column(s) passed\n     * to `onConflict`, `.upsert()` allows you to perform the equivalent of\n     * `.insert()` if a row with the corresponding `onConflict` columns doesn't\n     * exist, or if it does exist, perform an alternative action depending on\n     * `ignoreDuplicates`.\n     *\n     * By default, upserted rows are not returned. To return it, chain the call\n     * with `.select()`.\n     *\n     * @param values - The values to upsert with. Pass an object to upsert a\n     * single row or an array to upsert multiple rows.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.onConflict - Comma-separated UNIQUE column(s) to specify how\n     * duplicate rows are determined. Two rows are duplicates if all the\n     * `onConflict` columns are equal.\n     *\n     * @param options.ignoreDuplicates - If `true`, duplicate rows are ignored. If\n     * `false`, duplicate rows are merged with existing rows.\n     *\n     * @param options.count - Count algorithm to use to count upserted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     *\n     * @param options.defaultToNull - Make missing fields default to `null`.\n     * Otherwise, use the default value for the column. This only applies when\n     * inserting new rows, not when merging with existing rows under\n     * `ignoreDuplicates: false`. This also only applies when doing bulk upserts.\n     */\n    upsert(values, { onConflict, ignoreDuplicates = false, count, defaultToNull = true, } = {}) {\n        const method = 'POST';\n        const prefersHeaders = [`resolution=${ignoreDuplicates ? 'ignore' : 'merge'}-duplicates`];\n        if (onConflict !== undefined)\n            this.url.searchParams.set('on_conflict', onConflict);\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (!defaultToNull) {\n            prefersHeaders.push('missing=default');\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        if (Array.isArray(values)) {\n            const columns = values.reduce((acc, x) => acc.concat(Object.keys(x)), []);\n            if (columns.length > 0) {\n                const uniqueColumns = [...new Set(columns)].map((column) => `\"${column}\"`);\n                this.url.searchParams.set('columns', uniqueColumns.join(','));\n            }\n        }\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform an UPDATE on the table or view.\n     *\n     * By default, updated rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param values - The values to update with\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count updated rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    update(values, { count, } = {}) {\n        const method = 'PATCH';\n        const prefersHeaders = [];\n        if (this.headers['Prefer']) {\n            prefersHeaders.push(this.headers['Prefer']);\n        }\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            body: values,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n    /**\n     * Perform a DELETE on the table or view.\n     *\n     * By default, deleted rows are not returned. To return it, chain the call\n     * with `.select()` after filters.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.count - Count algorithm to use to count deleted rows.\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    delete({ count, } = {}) {\n        const method = 'DELETE';\n        const prefersHeaders = [];\n        if (count) {\n            prefersHeaders.push(`count=${count}`);\n        }\n        if (this.headers['Prefer']) {\n            prefersHeaders.unshift(this.headers['Prefer']);\n        }\n        this.headers['Prefer'] = prefersHeaders.join(',');\n        return new PostgrestFilterBuilder_1.default({\n            method,\n            url: this.url,\n            headers: this.headers,\n            schema: this.schema,\n            fetch: this.fetch,\n            allowEmpty: false,\n        });\n    }\n}\nexports[\"default\"] = PostgrestQueryBuilder;\n//# sourceMappingURL=PostgrestQueryBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js":
/*!****************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js ***!
  \****************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nclass PostgrestTransformBuilder extends PostgrestBuilder_1.default {\n    /**\n     * Perform a SELECT on the query result.\n     *\n     * By default, `.insert()`, `.update()`, `.upsert()`, and `.delete()` do not\n     * return modified rows. By calling this method, modified rows are returned in\n     * `data`.\n     *\n     * @param columns - The columns to retrieve, separated by commas\n     */\n    select(columns) {\n        // Remove whitespaces except when quoted\n        let quoted = false;\n        const cleanedColumns = (columns !== null && columns !== void 0 ? columns : '*')\n            .split('')\n            .map((c) => {\n            if (/\\s/.test(c) && !quoted) {\n                return '';\n            }\n            if (c === '\"') {\n                quoted = !quoted;\n            }\n            return c;\n        })\n            .join('');\n        this.url.searchParams.set('select', cleanedColumns);\n        if (this.headers['Prefer']) {\n            this.headers['Prefer'] += ',';\n        }\n        this.headers['Prefer'] += 'return=representation';\n        return this;\n    }\n    /**\n     * Order the query result by `column`.\n     *\n     * You can call this method multiple times to order by multiple columns.\n     *\n     * You can order referenced tables, but it only affects the ordering of the\n     * parent table if you use `!inner` in the query.\n     *\n     * @param column - The column to order by\n     * @param options - Named parameters\n     * @param options.ascending - If `true`, the result will be in ascending order\n     * @param options.nullsFirst - If `true`, `null`s appear first. If `false`,\n     * `null`s appear last.\n     * @param options.referencedTable - Set this to order a referenced table by\n     * its columns\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    order(column, { ascending = true, nullsFirst, foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = referencedTable ? `${referencedTable}.order` : 'order';\n        const existingOrder = this.url.searchParams.get(key);\n        this.url.searchParams.set(key, `${existingOrder ? `${existingOrder},` : ''}${column}.${ascending ? 'asc' : 'desc'}${nullsFirst === undefined ? '' : nullsFirst ? '.nullsfirst' : '.nullslast'}`);\n        return this;\n    }\n    /**\n     * Limit the query result by `count`.\n     *\n     * @param count - The maximum number of rows to return\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    limit(count, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const key = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(key, `${count}`);\n        return this;\n    }\n    /**\n     * Limit the query result by starting at an offset `from` and ending at the offset `to`.\n     * Only records within this range are returned.\n     * This respects the query order and if there is no order clause the range could behave unexpectedly.\n     * The `from` and `to` values are 0-based and inclusive: `range(1, 3)` will include the second, third\n     * and fourth rows of the query.\n     *\n     * @param from - The starting index from which to limit the result\n     * @param to - The last index to which to limit the result\n     * @param options - Named parameters\n     * @param options.referencedTable - Set this to limit rows of referenced\n     * tables instead of the parent table\n     * @param options.foreignTable - Deprecated, use `options.referencedTable`\n     * instead\n     */\n    range(from, to, { foreignTable, referencedTable = foreignTable, } = {}) {\n        const keyOffset = typeof referencedTable === 'undefined' ? 'offset' : `${referencedTable}.offset`;\n        const keyLimit = typeof referencedTable === 'undefined' ? 'limit' : `${referencedTable}.limit`;\n        this.url.searchParams.set(keyOffset, `${from}`);\n        // Range is inclusive, so add 1\n        this.url.searchParams.set(keyLimit, `${to - from + 1}`);\n        return this;\n    }\n    /**\n     * Set the AbortSignal for the fetch request.\n     *\n     * @param signal - The AbortSignal to use for the fetch request\n     */\n    abortSignal(signal) {\n        this.signal = signal;\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be one row (e.g. using `.limit(1)`), otherwise this\n     * returns an error.\n     */\n    single() {\n        this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        return this;\n    }\n    /**\n     * Return `data` as a single object instead of an array of objects.\n     *\n     * Query result must be zero or one row (e.g. using `.limit(1)`), otherwise\n     * this returns an error.\n     */\n    maybeSingle() {\n        // Temporary partial fix for https://github.com/supabase/postgrest-js/issues/361\n        // Issue persists e.g. for `.insert([...]).select().maybeSingle()`\n        if (this.method === 'GET') {\n            this.headers['Accept'] = 'application/json';\n        }\n        else {\n            this.headers['Accept'] = 'application/vnd.pgrst.object+json';\n        }\n        this.isMaybeSingle = true;\n        return this;\n    }\n    /**\n     * Return `data` as a string in CSV format.\n     */\n    csv() {\n        this.headers['Accept'] = 'text/csv';\n        return this;\n    }\n    /**\n     * Return `data` as an object in [GeoJSON](https://geojson.org) format.\n     */\n    geojson() {\n        this.headers['Accept'] = 'application/geo+json';\n        return this;\n    }\n    /**\n     * Return `data` as the EXPLAIN plan for the query.\n     *\n     * You need to enable the\n     * [db_plan_enabled](https://supabase.com/docs/guides/database/debugging-performance#enabling-explain)\n     * setting before using this method.\n     *\n     * @param options - Named parameters\n     *\n     * @param options.analyze - If `true`, the query will be executed and the\n     * actual run time will be returned\n     *\n     * @param options.verbose - If `true`, the query identifier will be returned\n     * and `data` will include the output columns of the query\n     *\n     * @param options.settings - If `true`, include information on configuration\n     * parameters that affect query planning\n     *\n     * @param options.buffers - If `true`, include information on buffer usage\n     *\n     * @param options.wal - If `true`, include information on WAL record generation\n     *\n     * @param options.format - The format of the output, can be `\"text\"` (default)\n     * or `\"json\"`\n     */\n    explain({ analyze = false, verbose = false, settings = false, buffers = false, wal = false, format = 'text', } = {}) {\n        var _a;\n        const options = [\n            analyze ? 'analyze' : null,\n            verbose ? 'verbose' : null,\n            settings ? 'settings' : null,\n            buffers ? 'buffers' : null,\n            wal ? 'wal' : null,\n        ]\n            .filter(Boolean)\n            .join('|');\n        // An Accept header can carry multiple media types but postgrest-js always sends one\n        const forMediatype = (_a = this.headers['Accept']) !== null && _a !== void 0 ? _a : 'application/json';\n        this.headers['Accept'] = `application/vnd.pgrst.plan+${format}; for=\"${forMediatype}\"; options=${options};`;\n        if (format === 'json')\n            return this;\n        else\n            return this;\n    }\n    /**\n     * Rollback the query.\n     *\n     * `data` will still be returned, but the query is not committed.\n     */\n    rollback() {\n        var _a;\n        if (((_a = this.headers['Prefer']) !== null && _a !== void 0 ? _a : '').trim().length > 0) {\n            this.headers['Prefer'] += ',tx=rollback';\n        }\n        else {\n            this.headers['Prefer'] = 'tx=rollback';\n        }\n        return this;\n    }\n    /**\n     * Override the type of the returned `data`.\n     *\n     * @typeParam NewResult - The new result type to override with\n     * @deprecated Use overrideTypes<yourType, { merge: false }>() method at the end of your call chain instead\n     */\n    returns() {\n        return this;\n    }\n}\nexports[\"default\"] = PostgrestTransformBuilder;\n//# sourceMappingURL=PostgrestTransformBuilder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js":
/*!************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DEFAULT_HEADERS = void 0;\nconst version_1 = __webpack_require__(/*! ./version */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\");\nexports.DEFAULT_HEADERS = { 'X-Client-Info': `postgrest-js/${version_1.version}` };\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx1QkFBdUI7QUFDdkIsa0JBQWtCLG1CQUFPLENBQUMsdUlBQVc7QUFDckMsdUJBQXVCLEtBQUssaUNBQWlDLGtCQUFrQjtBQUMvRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZWRwYW5kYVxcRGVza3RvcFxcTWV0YW1vcnBoaWMgZmx1eFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxwb3N0Z3Jlc3QtanNcXGRpc3RcXGNqc1xcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5ERUZBVUxUX0hFQURFUlMgPSB2b2lkIDA7XG5jb25zdCB2ZXJzaW9uXzEgPSByZXF1aXJlKFwiLi92ZXJzaW9uXCIpO1xuZXhwb3J0cy5ERUZBVUxUX0hFQURFUlMgPSB7ICdYLUNsaWVudC1JbmZvJzogYHBvc3RncmVzdC1qcy8ke3ZlcnNpb25fMS52ZXJzaW9ufWAgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js":
/*!********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js ***!
  \********************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PostgrestError = exports.PostgrestBuilder = exports.PostgrestTransformBuilder = exports.PostgrestFilterBuilder = exports.PostgrestQueryBuilder = exports.PostgrestClient = void 0;\n// Always update wrapper.mjs when updating this file.\nconst PostgrestClient_1 = __importDefault(__webpack_require__(/*! ./PostgrestClient */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js\"));\nexports.PostgrestClient = PostgrestClient_1.default;\nconst PostgrestQueryBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestQueryBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js\"));\nexports.PostgrestQueryBuilder = PostgrestQueryBuilder_1.default;\nconst PostgrestFilterBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestFilterBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js\"));\nexports.PostgrestFilterBuilder = PostgrestFilterBuilder_1.default;\nconst PostgrestTransformBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestTransformBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js\"));\nexports.PostgrestTransformBuilder = PostgrestTransformBuilder_1.default;\nconst PostgrestBuilder_1 = __importDefault(__webpack_require__(/*! ./PostgrestBuilder */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js\"));\nexports.PostgrestBuilder = PostgrestBuilder_1.default;\nconst PostgrestError_1 = __importDefault(__webpack_require__(/*! ./PostgrestError */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js\"));\nexports.PostgrestError = PostgrestError_1.default;\nexports[\"default\"] = {\n    PostgrestClient: PostgrestClient_1.default,\n    PostgrestQueryBuilder: PostgrestQueryBuilder_1.default,\n    PostgrestFilterBuilder: PostgrestFilterBuilder_1.default,\n    PostgrestTransformBuilder: PostgrestTransformBuilder_1.default,\n    PostgrestBuilder: PostgrestBuilder_1.default,\n    PostgrestError: PostgrestError_1.default,\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.version = void 0;\nexports.version = '0.0.0-automated';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvY2pzL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZUFBZTtBQUNmLGVBQWU7QUFDZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZWRwYW5kYVxcRGVza3RvcFxcTWV0YW1vcnBoaWMgZmx1eFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3Bvc3RncmVzdC1qc0AxLjE5LjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxwb3N0Z3Jlc3QtanNcXGRpc3RcXGNqc1xcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMudmVyc2lvbiA9IHZvaWQgMDtcbmV4cG9ydHMudmVyc2lvbiA9ICcwLjAuMC1hdXRvbWF0ZWQnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/version.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgrestBuilder: () => (/* binding */ PostgrestBuilder),\n/* harmony export */   PostgrestClient: () => (/* binding */ PostgrestClient),\n/* harmony export */   PostgrestError: () => (/* binding */ PostgrestError),\n/* harmony export */   PostgrestFilterBuilder: () => (/* binding */ PostgrestFilterBuilder),\n/* harmony export */   PostgrestQueryBuilder: () => (/* binding */ PostgrestQueryBuilder),\n/* harmony export */   PostgrestTransformBuilder: () => (/* binding */ PostgrestTransformBuilder),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cjs/index.js */ \"(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/cjs/index.js\");\n\nconst {\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n} = _cjs_index_js__WEBPACK_IMPORTED_MODULE_0__\n\n\n\n// compatibility with CJS output\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  PostgrestClient,\n  PostgrestQueryBuilder,\n  PostgrestFilterBuilder,\n  PostgrestTransformBuilder,\n  PostgrestBuilder,\n  PostgrestError,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStwb3N0Z3Jlc3QtanNAMS4xOS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2UvcG9zdGdyZXN0LWpzL2Rpc3QvZXNtL3dyYXBwZXIubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxFQUFFLDBDQUFLOztBQVNSOztBQUVEO0FBQ0EsaUVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlZHBhbmRhXFxEZXNrdG9wXFxNZXRhbW9ycGhpYyBmbHV4XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrcG9zdGdyZXN0LWpzQDEuMTkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHBvc3RncmVzdC1qc1xcZGlzdFxcZXNtXFx3cmFwcGVyLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaW5kZXggZnJvbSAnLi4vY2pzL2luZGV4LmpzJ1xuY29uc3Qge1xuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59ID0gaW5kZXhcblxuZXhwb3J0IHtcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0Q2xpZW50LFxuICBQb3N0Z3Jlc3RGaWx0ZXJCdWlsZGVyLFxuICBQb3N0Z3Jlc3RRdWVyeUJ1aWxkZXIsXG4gIFBvc3RncmVzdFRyYW5zZm9ybUJ1aWxkZXIsXG4gIFBvc3RncmVzdEVycm9yLFxufVxuXG4vLyBjb21wYXRpYmlsaXR5IHdpdGggQ0pTIG91dHB1dFxuZXhwb3J0IGRlZmF1bHQge1xuICBQb3N0Z3Jlc3RDbGllbnQsXG4gIFBvc3RncmVzdFF1ZXJ5QnVpbGRlcixcbiAgUG9zdGdyZXN0RmlsdGVyQnVpbGRlcixcbiAgUG9zdGdyZXN0VHJhbnNmb3JtQnVpbGRlcixcbiAgUG9zdGdyZXN0QnVpbGRlcixcbiAgUG9zdGdyZXN0RXJyb3IsXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\n");

/***/ })

};
;