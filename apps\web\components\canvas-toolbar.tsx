'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useCanvasStore, useCanvasWorkflow, useCanvasSettings } from '@/lib/stores/canvas-store';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Grid3X3,
  Settings,
  Download,
  Upload,
  Trash2,
  Copy,
  Layers,
  Eye,
  EyeOff,
  MousePointer,
  Hand,
  Plus,
  Minus,
  MoreHorizontal,
} from 'lucide-react';

interface CanvasToolbarProps {
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  compact?: boolean;
}

export function CanvasToolbar({ 
  className = '', 
  orientation = 'horizontal',
  compact = false 
}: CanvasToolbarProps): React.JSX.Element {
  const [showMoreTools, setShowMoreTools] = useState(false);
  const [tool, setTool] = useState<'select' | 'pan' | 'connect'>('select');
  
  const {
    undo,
    redo,
    zoomIn,
    zoomOut,
    zoomToFit,
    resetViewport,
    clearCanvas,
    exportCanvas,
    importCanvas,
    saveToHistory,
  } = useCanvasStore();
  
  const { isPlaying, startWorkflow, stopWorkflow, pauseWorkflow } = useCanvasWorkflow();
  const { gridEnabled, showMinimap, updateSettings } = useCanvasSettings();

  const handleExport = () => {
    const data = exportCanvas();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `canvas-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          importCanvas(data);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const handleClearCanvas = () => {
    if (confirm('Are you sure you want to clear the entire canvas? This action cannot be undone.')) {
      saveToHistory();
      clearCanvas();
    }
  };

  const toolbarClasses = `
    flex items-center gap-2 p-2 bg-card/80 backdrop-blur-sm border rounded-lg shadow-lg
    ${orientation === 'vertical' ? 'flex-col' : 'flex-row'}
    ${compact ? 'gap-1 p-1' : 'gap-2 p-2'}
    ${className}
  `;

  const buttonSize = compact ? 'sm' : 'default';

  const primaryTools = (
    <>
      {/* Workflow Controls */}
      <div className={`flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1`}>
        {!isPlaying ? (
          <Button
            size={buttonSize}
            onClick={startWorkflow}
            className="flux-gradient text-white"
            title="Start Workflow"
          >
            <Play className="h-4 w-4" />
            {!compact && <span className="ml-1">Start</span>}
          </Button>
        ) : (
          <>
            <Button
              size={buttonSize}
              onClick={pauseWorkflow}
              variant="outline"
              title="Pause Workflow"
            >
              <Pause className="h-4 w-4" />
              {!compact && <span className="ml-1">Pause</span>}
            </Button>
            <Button
              size={buttonSize}
              onClick={stopWorkflow}
              variant="outline"
              title="Stop Workflow"
            >
              <Square className="h-4 w-4" />
              {!compact && <span className="ml-1">Stop</span>}
            </Button>
          </>
        )}
      </div>

      {orientation === 'horizontal' && <div className="w-px h-6 bg-border" />}

      {/* Tool Selection */}
      <div className={`flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1`}>
        <Button
          size={buttonSize}
          variant={tool === 'select' ? 'default' : 'outline'}
          onClick={() => setTool('select')}
          title="Select Tool"
        >
          <MousePointer className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant={tool === 'pan' ? 'default' : 'outline'}
          onClick={() => setTool('pan')}
          title="Pan Tool"
        >
          <Hand className="h-4 w-4" />
        </Button>
      </div>

      {orientation === 'horizontal' && <div className="w-px h-6 bg-border" />}

      {/* History Controls */}
      <div className={`flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1`}>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={undo}
          title="Undo (Ctrl+Z)"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={redo}
          title="Redo (Ctrl+Y)"
        >
          <RotateCw className="h-4 w-4" />
        </Button>
      </div>

      {orientation === 'horizontal' && <div className="w-px h-6 bg-border" />}

      {/* Zoom Controls */}
      <div className={`flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1`}>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={zoomIn}
          title="Zoom In (+)"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={zoomOut}
          title="Zoom Out (-)"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={zoomToFit}
          title="Zoom to Fit"
        >
          <Maximize className="h-4 w-4" />
        </Button>
      </div>
    </>
  );

  const secondaryTools = (
    <>
      {orientation === 'horizontal' && <div className="w-px h-6 bg-border" />}

      {/* View Controls */}
      <div className={`flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1`}>
        <Button
          size={buttonSize}
          variant={gridEnabled ? 'default' : 'outline'}
          onClick={() => updateSettings({ gridEnabled: !gridEnabled })}
          title="Toggle Grid"
        >
          <Grid3X3 className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant={showMinimap ? 'default' : 'outline'}
          onClick={() => updateSettings({ showMinimap: !showMinimap })}
          title="Toggle Minimap"
        >
          <Layers className="h-4 w-4" />
        </Button>
      </div>

      {orientation === 'horizontal' && <div className="w-px h-6 bg-border" />}

      {/* File Operations */}
      <div className={`flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1`}>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={handleExport}
          title="Export Canvas"
        >
          <Download className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={handleImport}
          title="Import Canvas"
        >
          <Upload className="h-4 w-4" />
        </Button>
        <Button
          size={buttonSize}
          variant="outline"
          onClick={handleClearCanvas}
          title="Clear Canvas"
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </>
  );

  if (compact) {
    return (
      <div className={toolbarClasses}>
        {primaryTools}
        <Button
          size={buttonSize}
          variant="outline"
          onClick={() => setShowMoreTools(!showMoreTools)}
          title="More Tools"
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
        
        {showMoreTools && (
          <div className={`
            absolute ${orientation === 'vertical' ? 'left-full ml-2' : 'top-full mt-2'} 
            flex ${orientation === 'vertical' ? 'flex-col' : 'flex-row'} gap-1 
            p-2 bg-card/90 backdrop-blur-sm border rounded-lg shadow-lg z-50
          `}>
            {secondaryTools}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={toolbarClasses}>
      {primaryTools}
      {secondaryTools}
    </div>
  );
}

// Keyboard shortcuts hook
export function useCanvasKeyboardShortcuts() {
  const {
    undo,
    redo,
    zoomIn,
    zoomOut,
    zoomToFit,
    clearSelection,
    selectAll,
    saveToHistory,
  } = useCanvasStore();

  const { startWorkflow, stopWorkflow } = useCanvasWorkflow();

  // Handle keyboard shortcuts
  const handleKeyDown = (e: KeyboardEvent) => {
    const isCtrl = e.ctrlKey || e.metaKey;
    const isShift = e.shiftKey;

    // Prevent default for our shortcuts
    if (isCtrl) {
      switch (e.key.toLowerCase()) {
        case 'z':
          e.preventDefault();
          if (isShift) {
            redo();
          } else {
            undo();
          }
          break;
        case 'y':
          e.preventDefault();
          redo();
          break;
        case 'a':
          e.preventDefault();
          selectAll();
          break;
        case 's':
          e.preventDefault();
          saveToHistory();
          break;
        case '0':
          e.preventDefault();
          zoomToFit();
          break;
      }
    }

    // Non-ctrl shortcuts
    switch (e.key) {
      case 'Escape':
        clearSelection();
        break;
      case ' ':
        e.preventDefault();
        if (e.target === document.body) {
          // Space bar for play/pause when not in input
          const { isPlaying } = useCanvasStore.getState();
          if (isPlaying) {
            stopWorkflow();
          } else {
            startWorkflow();
          }
        }
        break;
      case '+':
      case '=':
        if (!e.target || (e.target as HTMLElement).tagName !== 'INPUT') {
          e.preventDefault();
          zoomIn();
        }
        break;
      case '-':
        if (!e.target || (e.target as HTMLElement).tagName !== 'INPUT') {
          e.preventDefault();
          zoomOut();
        }
        break;
    }
  };

  // Set up event listeners
  if (typeof window !== 'undefined') {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }
}
