/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '*.supabase.co',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
      },
    ],
  },
  transpilePackages: ['@metamorphic-flux/ui', '@metamorphic-flux/shared'],
  eslint: {
    dirs: ['app', 'components', 'lib', 'hooks'],
  },
};

module.exports = nextConfig;
