import { FluxCanvas } from '@/components/flux-canvas';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON>, Zap, Target, BarChart3 } from 'lucide-react';

export default function HomePage(): React.JSX.Element {
  return (
    <div className="flex flex-col">
      {/* Hero Section with Flux Canvas */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-flux-950/20">
        <div className="absolute inset-0">
          <FluxCanvas />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-6xl md:text-8xl font-bold mb-6">
              <span className="flux-text-gradient">Metamorphic</span>
              <br />
              <span className="text-foreground">Flux™</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              AI-powered marketing engine that dynamically shape-shifts campaign creative, 
              channels, and spend in real-time.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="flux-gradient text-white hover:opacity-90">
                Start Your Campaign
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline">
                Watch Demo
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-flux-500" />
                </div>
                <h3 className="font-semibold mb-2">Real-time DCO</h3>
                <p className="text-sm text-muted-foreground">
                  &lt; 200ms idea-to-impression latency
                </p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4">
                  <Target className="h-6 w-6 text-flux-500" />
                </div>
                <h3 className="font-semibold mb-2">AI Agents</h3>
                <p className="text-sm text-muted-foreground">
                  6 specialized micro-agents for every campaign aspect
                </p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4">
                  <BarChart3 className="h-6 w-6 text-flux-500" />
                </div>
                <h3 className="font-semibold mb-2">Quantum Optimization</h3>
                <p className="text-sm text-muted-foreground">
                  Budget allocation via Google Willow
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
