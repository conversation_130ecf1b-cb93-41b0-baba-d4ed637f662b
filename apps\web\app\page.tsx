import { FluxCanvas } from '@/components/flux-canvas';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Zap, Target, BarChart3, <PERSON>, <PERSON><PERSON>, <PERSON>, CheckCircle, Star, Play } from 'lucide-react';
import Link from 'next/link';

export default function HomePage(): React.JSX.Element {
  return (
    <div className="flex flex-col">
      {/* Hero Section with Flux Canvas */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-flux-950/20">
        <div className="absolute inset-0">
          <FluxCanvas />
        </div>
        
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-6xl md:text-8xl font-bold mb-6">
              <span className="flux-text-gradient">Metamorphic</span>
              <br />
              <span className="text-foreground">Flux™</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              AI-powered marketing engine that dynamically shape-shifts campaign creative, 
              channels, and spend in real-time.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="flux-gradient text-white hover:opacity-90" asChild>
                <Link href="/auth/signup">
                  Start Your Campaign
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/dashboard">
                  <Play className="mr-2 h-5 w-5" />
                  Watch Demo
                </Link>
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-flux-500" />
                </div>
                <h3 className="font-semibold mb-2">Real-time DCO</h3>
                <p className="text-sm text-muted-foreground">
                  &lt; 200ms idea-to-impression latency
                </p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4">
                  <Target className="h-6 w-6 text-flux-500" />
                </div>
                <h3 className="font-semibold mb-2">AI Agents</h3>
                <p className="text-sm text-muted-foreground">
                  6 specialized micro-agents for every campaign aspect
                </p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 rounded-full bg-flux-500/20 flex items-center justify-center mb-4">
                  <BarChart3 className="h-6 w-6 text-flux-500" />
                </div>
                <h3 className="font-semibold mb-2">Quantum Optimization</h3>
                <p className="text-sm text-muted-foreground">
                  Budget allocation via Google Willow
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-card/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Six AI Agents, Infinite Possibilities
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our AI agent swarm works together to create, optimize, and analyze your campaigns
              with superhuman speed and precision.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Brain,
                title: "Copy Agent",
                description: "AI-powered copywriting that adapts to your brand voice and audience personas",
                color: "text-blue-500",
                bgColor: "bg-blue-500/10",
              },
              {
                icon: Palette,
                title: "Design Agent",
                description: "Visual content creation with Imagen 4 integration for stunning creatives",
                color: "text-purple-500",
                bgColor: "bg-purple-500/10",
              },
              {
                icon: BarChart3,
                title: "Analyst Agent",
                description: "Real-time performance analysis with predictive optimization insights",
                color: "text-green-500",
                bgColor: "bg-green-500/10",
              },
              {
                icon: Target,
                title: "Buyer Agent",
                description: "Automated media buying with intelligent budget allocation",
                color: "text-red-500",
                bgColor: "bg-red-500/10",
              },
              {
                icon: Shield,
                title: "Ethics Agent",
                description: "Brand safety and compliance monitoring for responsible AI marketing",
                color: "text-yellow-500",
                bgColor: "bg-yellow-500/10",
              },
              {
                icon: CheckCircle,
                title: "QA Agent",
                description: "Quality assurance and performance validation before campaign launch",
                color: "text-flux-500",
                bgColor: "bg-flux-500/10",
              },
            ].map((feature, index) => (
              <div
                key={feature.title}
                className="bg-card border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className={`w-12 h-12 ${feature.bgColor} rounded-lg flex items-center justify-center mb-4`}>
                  <feature.icon className={`h-6 w-6 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Ready to Transform Your Marketing?
            </h2>
            <p className="text-xl text-muted-foreground mb-8">
              Join thousands of marketers who are already using AI to create better campaigns,
              faster than ever before.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Button size="lg" className="flux-gradient text-lg px-8 py-4" asChild>
                <Link href="/auth/signup">
                  Start Your Free Trial
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>

              <Button variant="outline" size="lg" className="text-lg px-8 py-4" asChild>
                <Link href="/contact">
                  Schedule Demo
                </Link>
              </Button>
            </div>

            <p className="text-sm text-muted-foreground mt-6">
              No credit card required • 14-day free trial • Cancel anytime
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border bg-card/50">
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Zap className="h-6 w-6 text-flux-500" />
                <span className="text-xl font-bold text-foreground">Metamorphic Flux</span>
              </div>
              <p className="text-muted-foreground">
                AI-powered marketing automation that transforms campaigns at the speed of thought.
              </p>
            </div>

            <div>
              <h3 className="font-semibold text-foreground mb-4">Product</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/features" className="hover:text-foreground">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-foreground">Pricing</Link></li>
                <li><Link href="/dashboard" className="hover:text-foreground">Demo</Link></li>
                <li><Link href="/api/health" className="hover:text-foreground">Status</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-foreground mb-4">Company</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/about" className="hover:text-foreground">About</Link></li>
                <li><Link href="/blog" className="hover:text-foreground">Blog</Link></li>
                <li><Link href="/careers" className="hover:text-foreground">Careers</Link></li>
                <li><Link href="/contact" className="hover:text-foreground">Contact</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="font-semibold text-foreground mb-4">Support</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><Link href="/docs" className="hover:text-foreground">Documentation</Link></li>
                <li><Link href="/help" className="hover:text-foreground">Help Center</Link></li>
                <li><Link href="/auth/signin" className="hover:text-foreground">Sign In</Link></li>
                <li><Link href="/privacy" className="hover:text-foreground">Privacy</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-border mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 Metamorphic Flux. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
