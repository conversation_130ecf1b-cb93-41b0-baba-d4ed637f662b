"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0";
exports.ids = ["vendor-chunks/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.81.5/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools auto */ // src/ReactQueryDevtools.tsx\n\n\n\n\nfunction ReactQueryDevtools(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { buttonPosition, position, initialIsOpen, errorTypes, styleNonce, shadowDOMTarget } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtools({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition,\n        position,\n        initialIsOpen,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setClient(queryClient);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (buttonPosition) {\n                devtools.setButtonPosition(buttonPosition);\n            }\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        buttonPosition,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (position) {\n                devtools.setPosition(position);\n            }\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        position,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setInitialIsOpen(initialIsOpen || false);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        initialIsOpen,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            devtools.setErrorTypes(errorTypes || []);\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtools.useEffect\": ()=>{\n            if (ref.current) {\n                devtools.mount(ref.current);\n            }\n            return ({\n                \"ReactQueryDevtools.useEffect\": ()=>{\n                    devtools.unmount();\n                }\n            })[\"ReactQueryDevtools.useEffect\"];\n        }\n    }[\"ReactQueryDevtools.useEffect\"], [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        dir: \"ltr\",\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtools.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js":
/*!******************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js ***!
  \******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query@5.81.5_react@19.1.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-core@5.81.5/node_modules/@tanstack/query-core/build/modern/onlineManager.js\");\n/* harmony import */ var _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-devtools */ \"(ssr)/../../node_modules/.pnpm/@tanstack+query-devtools@5.81.2/node_modules/@tanstack/query-devtools/build/dev.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtoolsPanel auto */ // src/ReactQueryDevtoolsPanel.tsx\n\n\n\n\nfunction ReactQueryDevtoolsPanel(props) {\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)(props.client);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { errorTypes, styleNonce, shadowDOMTarget } = props;\n    const [devtools] = react__WEBPACK_IMPORTED_MODULE_0__.useState(new _tanstack_query_devtools__WEBPACK_IMPORTED_MODULE_1__.TanstackQueryDevtoolsPanel({\n        client: queryClient,\n        queryFlavor: \"React Query\",\n        version: \"5\",\n        onlineManager: _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.onlineManager,\n        buttonPosition: \"bottom-left\",\n        position: \"bottom\",\n        initialIsOpen: true,\n        errorTypes,\n        styleNonce,\n        shadowDOMTarget,\n        onClose: props.onClose\n    }));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setClient(queryClient);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        queryClient,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setOnClose(props.onClose ?? ({\n                \"ReactQueryDevtoolsPanel.useEffect\": ()=>{}\n            })[\"ReactQueryDevtoolsPanel.useEffect\"]);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        props.onClose,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            devtools.setErrorTypes(errorTypes || []);\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        errorTypes,\n        devtools\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n            if (ref.current) {\n                devtools.mount(ref.current);\n            }\n            return ({\n                \"ReactQueryDevtoolsPanel.useEffect\": ()=>{\n                    devtools.unmount();\n                }\n            })[\"ReactQueryDevtoolsPanel.useEffect\"];\n        }\n    }[\"ReactQueryDevtoolsPanel.useEffect\"], [\n        devtools\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n        style: {\n            height: \"500px\",\n            ...props.style\n        },\n        className: \"tsqd-parent-container\",\n        ref\n    });\n}\n //# sourceMappingURL=ReactQueryDevtoolsPanel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/index.js":
/*!************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/index.js ***!
  \************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReactQueryDevtools: () => (/* binding */ ReactQueryDevtools2),\n/* harmony export */   ReactQueryDevtoolsPanel: () => (/* binding */ ReactQueryDevtoolsPanel2)\n/* harmony export */ });\n/* harmony import */ var _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ReactQueryDevtools.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtools.js\");\n/* harmony import */ var _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ReactQueryDevtoolsPanel.js */ \"(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/ReactQueryDevtoolsPanel.js\");\n/* __next_internal_client_entry_do_not_use__ ReactQueryDevtools,ReactQueryDevtoolsPanel auto */ // src/index.ts\n\n\nvar ReactQueryDevtools2 =  false ? 0 : _ReactQueryDevtools_js__WEBPACK_IMPORTED_MODULE_0__.ReactQueryDevtools;\nvar ReactQueryDevtoolsPanel2 =  false ? 0 : _ReactQueryDevtoolsPanel_js__WEBPACK_IMPORTED_MODULE_1__.ReactQueryDevtoolsPanel;\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytyZWFjdC1xdWVyeS1kZXZ0b29sc0A1LjgxLjVfQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5QDUuODEuNV9yZWFjdEAxOS4xLjBfX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5LWRldnRvb2xzL2J1aWxkL21vZGVybi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUUwQjtBQUNLO0FBRXhCLElBQU1BLHNCQUNYLE1BQXlCLEdBQ3JCLENBRUMsR0FDUTtBQUVSLElBQU1DLDJCQUNYLE1BQXlCLEdBQ3JCLENBRUMsR0FDYSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyZWRwYW5kYVxcRGVza3RvcFxcTWV0YW1vcnBoaWMgZmx1eFxcc3JjXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgRGV2dG9vbHMgZnJvbSAnLi9SZWFjdFF1ZXJ5RGV2dG9vbHMnXG5pbXBvcnQgKiBhcyBEZXZ0b29sc1BhbmVsIGZyb20gJy4vUmVhY3RRdWVyeURldnRvb2xzUGFuZWwnXG5cbmV4cG9ydCBjb25zdCBSZWFjdFF1ZXJ5RGV2dG9vbHM6ICh0eXBlb2YgRGV2dG9vbHMpWydSZWFjdFF1ZXJ5RGV2dG9vbHMnXSA9XG4gIHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnXG4gICAgPyBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBudWxsXG4gICAgICB9XG4gICAgOiBEZXZ0b29scy5SZWFjdFF1ZXJ5RGV2dG9vbHNcblxuZXhwb3J0IGNvbnN0IFJlYWN0UXVlcnlEZXZ0b29sc1BhbmVsOiAodHlwZW9mIERldnRvb2xzUGFuZWwpWydSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbCddID1cbiAgcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCdcbiAgICA/IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIG51bGxcbiAgICAgIH1cbiAgICA6IERldnRvb2xzUGFuZWwuUmVhY3RRdWVyeURldnRvb2xzUGFuZWxcbiJdLCJuYW1lcyI6WyJSZWFjdFF1ZXJ5RGV2dG9vbHMiLCJSZWFjdFF1ZXJ5RGV2dG9vbHNQYW5lbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@tanstack+react-query-devtools@5.81.5_@tanstack+react-query@5.81.5_react@19.1.0__react@19.1.0/node_modules/@tanstack/react-query-devtools/build/modern/index.js\n");

/***/ })

};
;