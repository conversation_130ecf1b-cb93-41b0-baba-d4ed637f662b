'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { useTheme } from 'next-themes';

interface FluxCanvasProps {
  className?: string;
  intensity?: 'low' | 'medium' | 'high';
  interactive?: boolean;
  showMetrics?: boolean;
}

interface FluxMetrics {
  particleCount: number;
  fps: number;
  connections: number;
}

export function FluxCanvas({
  className,
  intensity = 'medium',
  interactive = true,
  showMetrics = false
}: FluxCanvasProps): React.JSX.Element {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const [metrics, setMetrics] = useState<FluxMetrics>({ particleCount: 0, fps: 0, connections: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const { theme } = useTheme();

  // Performance monitoring
  const fpsRef = useRef({ frames: 0, lastTime: Date.now() });

  const getIntensityConfig = useCallback(() => {
    const configs = {
      low: { particles: 30, maxConnections: 80, speed: 1 },
      medium: { particles: 50, maxConnections: 120, speed: 1.5 },
      high: { particles: 80, maxConnections: 200, speed: 2 }
    };
    return configs[intensity];
  }, [intensity]);

  const getThemeColors = useCallback(() => {
    const isDark = theme === 'dark';
    return {
      primary: isDark ? [217, 91, 60] : [221, 83, 53], // HSL values
      secondary: isDark ? [200, 70, 50] : [210, 80, 45],
      accent: isDark ? [180, 60, 40] : [190, 70, 35],
      background: isDark ? 0.1 : 0.05,
    };
  }, [theme]);

  useEffect(() => {
    const canvas = canvasRef.current;
    const container = containerRef.current;
    if (!canvas || !container) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const config = getIntensityConfig();
    const colors = getThemeColors();

    // Set canvas size with device pixel ratio for crisp rendering
    const resizeCanvas = (): void => {
      const rect = container.getBoundingClientRect();
      const dpr = window.devicePixelRatio || 1;

      canvas.width = rect.width * dpr;
      canvas.height = rect.height * dpr;
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;

      ctx.scale(dpr, dpr);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Enhanced particle system
    class FluxParticle {
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      hue: number;
      saturation: number;
      lightness: number;
      life: number;
      maxLife: number;
      energy: number;

      constructor(x: number, y: number) {
        this.x = x;
        this.y = y;
        this.vx = (Math.random() - 0.5) * config.speed;
        this.vy = (Math.random() - 0.5) * config.speed;
        this.size = Math.random() * 4 + 1;
        this.opacity = Math.random() * 0.6 + 0.2;

        // Use theme-aware colors
        const colorSet = Math.random() < 0.6 ? colors.primary :
                        Math.random() < 0.8 ? colors.secondary : colors.accent;
        this.hue = (colorSet?.[0] ?? 217) + (Math.random() - 0.5) * 20;
        this.saturation = (colorSet?.[1] ?? 91) + (Math.random() - 0.5) * 20;
        this.lightness = (colorSet?.[2] ?? 60) + (Math.random() - 0.5) * 15;

        this.life = 0;
        this.maxLife = Math.random() * 1000 + 500;
        this.energy = Math.random() * 0.5 + 0.5;
      }

      update(mouseX?: number, mouseY?: number): void {
        // Mouse interaction
        if (interactive && mouseX !== undefined && mouseY !== undefined) {
          const dx = mouseX - this.x;
          const dy = mouseY - this.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 150) {
            const force = (150 - distance) / 150 * 0.02;
            this.vx += dx * force * this.energy;
            this.vy += dy * force * this.energy;
          }
        }

        // Apply velocity with damping
        this.x += this.vx;
        this.y += this.vy;
        this.vx *= 0.99;
        this.vy *= 0.99;

        // Boundary handling with smooth wrapping
        const margin = 50;
        const canvasWidth = canvas?.width ? canvas.width / (window.devicePixelRatio || 1) : 800;
        const canvasHeight = canvas?.height ? canvas.height / (window.devicePixelRatio || 1) : 600;

        if (this.x < -margin) this.x = canvasWidth + margin;
        if (this.x > canvasWidth + margin) this.x = -margin;
        if (this.y < -margin) this.y = canvasHeight + margin;
        if (this.y > canvasHeight + margin) this.y = -margin;

        // Life cycle and morphing
        this.life++;
        const lifeRatio = this.life / this.maxLife;

        // Subtle morphing effects
        this.hue += Math.sin(this.life * 0.01) * 0.5;
        this.opacity = (Math.sin(this.life * 0.005) * 0.3 + 0.7) * (1 - lifeRatio * 0.3);
        this.size = (Math.sin(this.life * 0.008) * 0.5 + 1) * this.energy;

        // Regenerate particle when life ends
        if (this.life >= this.maxLife) {
          this.life = 0;
          this.x = Math.random() * canvasWidth;
          this.y = Math.random() * canvasHeight;
          this.energy = Math.random() * 0.5 + 0.5;
        }
      }

      draw(): void {
        if (!ctx) return;

        ctx.save();
        ctx.globalAlpha = this.opacity;

        // Create gradient for each particle
        const gradient = ctx.createRadialGradient(
          this.x, this.y, 0,
          this.x, this.y, this.size * 2
        );
        gradient.addColorStop(0, `hsla(${this.hue}, ${this.saturation}%, ${this.lightness}%, 1)`);
        gradient.addColorStop(1, `hsla(${this.hue}, ${this.saturation}%, ${this.lightness}%, 0)`);

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      }
    }

    // Create particle system
    const particles: FluxParticle[] = [];
    const initialWidth = canvas.width / (window.devicePixelRatio || 1);
    const initialHeight = canvas.height / (window.devicePixelRatio || 1);

    for (let i = 0; i < config.particles; i++) {
      particles.push(
        new FluxParticle(
          Math.random() * initialWidth,
          Math.random() * initialHeight
        )
      );
    }

    // Mouse tracking
    let mouseX: number | undefined;
    let mouseY: number | undefined;

    const handleMouseMove = (e: MouseEvent): void => {
      const rect = canvas.getBoundingClientRect();
      mouseX = e.clientX - rect.left;
      mouseY = e.clientY - rect.top;
    };

    const handleMouseLeave = (): void => {
      mouseX = undefined;
      mouseY = undefined;
    };

    if (interactive) {
      canvas.addEventListener('mousemove', handleMouseMove);
      canvas.addEventListener('mouseleave', handleMouseLeave);
    }

    // Animation loop with performance monitoring
    let connectionCount = 0;

    const animate = (): void => {
      // FPS calculation
      fpsRef.current.frames++;
      const now = Date.now();
      if (now - fpsRef.current.lastTime >= 1000) {
        const fps = Math.round((fpsRef.current.frames * 1000) / (now - fpsRef.current.lastTime));
        fpsRef.current.frames = 0;
        fpsRef.current.lastTime = now;

        if (showMetrics) {
          setMetrics({
            particleCount: particles.length,
            fps,
            connections: connectionCount,
          });
        }
      }

      // Clear canvas with subtle background
      ctx.fillStyle = `rgba(0, 0, 0, ${colors.background})`;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      connectionCount = 0;

      // Draw connections between nearby particles
      particles.forEach((particle, i) => {
        particles.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < config.maxConnections && connectionCount < config.maxConnections) {
            const opacity = (config.maxConnections - distance) / config.maxConnections * 0.15;
            const avgHue = (particle.hue + otherParticle.hue) / 2;
            const avgSat = (particle.saturation + otherParticle.saturation) / 2;
            const avgLight = (particle.lightness + otherParticle.lightness) / 2;

            ctx.save();
            ctx.globalAlpha = opacity * (particle.opacity + otherParticle.opacity) / 2;
            ctx.strokeStyle = `hsl(${avgHue}, ${avgSat}%, ${avgLight}%)`;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.stroke();
            ctx.restore();

            connectionCount++;
          }
        });
      });

      // Update and draw particles
      particles.forEach(particle => {
        particle.update(mouseX, mouseY);
        particle.draw();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    // GSAP morphing animation for the container
    const tl = gsap.timeline({ repeat: -1, yoyo: true });
    tl.to(container, {
      duration: 4,
      scale: isHovered ? 1.05 : 1.02,
      rotation: isHovered ? 2 : 1,
      ease: 'power2.inOut',
    });

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (interactive) {
        canvas.removeEventListener('mousemove', handleMouseMove);
        canvas.removeEventListener('mouseleave', handleMouseLeave);
      }
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      tl.kill();
    };
  }, [intensity, interactive, theme, isHovered, getIntensityConfig, getThemeColors, showMetrics]);

  return (
    <div
      ref={containerRef}
      className={`absolute inset-0 flux-morph-container ${className ?? ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ mixBlendMode: theme === 'dark' ? 'screen' : 'multiply' }}
      />

      {showMetrics && (
        <div className="absolute top-4 right-4 bg-black/20 backdrop-blur-sm rounded-lg p-3 text-xs text-white font-mono">
          <div>Particles: {metrics.particleCount}</div>
          <div>FPS: {metrics.fps}</div>
          <div>Connections: {metrics.connections}</div>
        </div>
      )}
    </div>
  );
}
