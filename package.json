{"name": "metamorphic-flux", "version": "0.1.0", "private": true, "description": "Metamorphic Flux™ - AI-powered marketing engine that dynamically shape-shifts campaign creative, channels, and spend", "author": "<PERSON> (Digital Mind Pulse)", "license": "MIT", "packageManager": "pnpm@9.15.0", "engines": {"node": ">=20.0.0", "pnpm": ">=9.0.0"}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "test": "turbo test", "test:e2e": "turbo test:e2e", "type-check": "turbo type-check", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "prepare": "husky install"}, "devDependencies": {"@turbo/gen": "^2.3.0", "turbo": "^2.3.0", "prettier": "^3.4.2", "husky": "^9.1.7", "lint-staged": "^15.2.10", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.18.0", "eslint-config-next": "^15.3.0", "eslint-config-prettier": "^9.1.0", "typescript": "^5.7.2"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}