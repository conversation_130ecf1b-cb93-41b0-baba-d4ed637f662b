/**
 * Classical optimization module
 * 
 * Provides robust classical optimization algorithms as fallback
 * when quantum optimization is unavailable or suboptimal.
 */

import { ClassicalSolver } from './solver';

export { ClassicalSolver };

export type {
  OptimizationMetrics,
  PerformanceMetrics,
  Matrix,
  Vector,
} from '../types';

export {
  ClassicalOptimizationError,
} from '../types';

// Classical algorithm implementations
export const CLASSICAL_ALGORITHMS = {
  SQP: 'Sequential Quadratic Programming',
  GENETIC: 'Genetic Algorithm',
  PSO: 'Particle Swarm Optimization',
  SIMULATED_ANNEALING: 'Simulated Annealing',
  INTERIOR_POINT: 'Interior Point Method',
  GRADIENT_DESCENT: 'Gradient Descent',
  NELDER_MEAD: 'Nelder-Mead Simplex',
  BFGS: '<PERSON><PERSON><PERSON>-<PERSON>-<PERSON>-<PERSON>',
} as const;

// Algorithm characteristics
export const ALGORITHM_CHARACTERISTICS = {
  SQP: {
    type: 'gradient-based',
    convergence: 'fast',
    globalOptimum: false,
    constraints: 'excellent',
    scalability: 'medium',
  },
  GENETIC: {
    type: 'evolutionary',
    convergence: 'slow',
    globalOptimum: true,
    constraints: 'good',
    scalability: 'excellent',
  },
  PSO: {
    type: 'swarm-based',
    convergence: 'medium',
    globalOptimum: true,
    constraints: 'medium',
    scalability: 'good',
  },
  SIMULATED_ANNEALING: {
    type: 'metaheuristic',
    convergence: 'slow',
    globalOptimum: true,
    constraints: 'medium',
    scalability: 'good',
  },
  INTERIOR_POINT: {
    type: 'gradient-based',
    convergence: 'fast',
    globalOptimum: false,
    constraints: 'excellent',
    scalability: 'medium',
  },
} as const;

const defaultExport = {
  ClassicalSolver,
  CLASSICAL_ALGORITHMS,
  ALGORITHM_CHARACTERISTICS,
};

export default defaultExport;
