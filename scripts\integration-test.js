#!/usr/bin/env node

/**
 * Metamorphic Flux Integration Test Suite
 * Tests the complete system integration including web app, agents service, and database
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const http = require('http');
const https = require('https');

// Simple fetch implementation for Node.js
function fetch(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;

    const req = client.request(url, {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || 10000,
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          json: () => Promise.resolve(JSON.parse(data)),
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.end();
  });
}

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

class IntegrationTester {
  constructor() {
    this.webAppUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    this.agentsUrl = process.env.NEXT_PUBLIC_AGENTS_URL || 'http://localhost:3001';
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0,
    };
  }

  async runTest(name, testFn) {
    this.results.total++;
    logInfo(`Running test: ${name}`);
    
    try {
      await testFn();
      this.results.passed++;
      logSuccess(`Test passed: ${name}`);
    } catch (error) {
      this.results.failed++;
      logError(`Test failed: ${name} - ${error.message}`);
    }
  }

  async runWarningTest(name, testFn) {
    logInfo(`Running warning test: ${name}`);
    
    try {
      await testFn();
      logSuccess(`Test passed: ${name}`);
    } catch (error) {
      this.results.warnings++;
      logWarning(`Warning: ${name} - ${error.message}`);
    }
  }

  // Test environment configuration
  async testEnvironment() {
    await this.runTest('Environment Variables', async () => {
      const requiredVars = [
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY',
        'GOOGLE_AI_API_KEY',
      ];

      for (const varName of requiredVars) {
        if (!process.env[varName]) {
          throw new Error(`Missing required environment variable: ${varName}`);
        }
      }
    });
  }

  // Test web app health
  async testWebAppHealth() {
    await this.runTest('Web App Health Check', async () => {
      const response = await fetch(`${this.webAppUrl}/api/health`, {
        timeout: 10000,
      });

      if (!response.ok) {
        throw new Error(`Health check failed with status: ${response.status}`);
      }

      const health = await response.json();
      
      if (health.status === 'unhealthy') {
        throw new Error(`System is unhealthy: ${JSON.stringify(health.checks)}`);
      }

      logInfo(`Health status: ${health.status}`);
      logInfo(`Services: ${health.summary.healthy}/${health.summary.total} healthy`);
    });
  }

  // Test agents service
  async testAgentsService() {
    await this.runWarningTest('Agents Service Health', async () => {
      const response = await fetch(`${this.agentsUrl}/health`, {
        timeout: 10000,
      });

      if (!response.ok) {
        throw new Error(`Agents service health check failed: ${response.status}`);
      }

      const health = await response.json();
      logInfo(`Agents service status: ${health.status || 'unknown'}`);
    });
  }

  // Test database connectivity
  async testDatabase() {
    await this.runTest('Database Connectivity', async () => {
      const response = await fetch(`${this.webAppUrl}/api/health`);
      const health = await response.json();
      
      const dbCheck = health.checks?.find(check => check.service === 'database');
      if (!dbCheck || dbCheck.status === 'unhealthy') {
        throw new Error('Database connectivity failed');
      }

      logInfo(`Database response time: ${dbCheck.responseTime}ms`);
    });
  }

  // Test AI service configuration
  async testAIService() {
    await this.runTest('AI Service Configuration', async () => {
      const response = await fetch(`${this.webAppUrl}/api/health`);
      const health = await response.json();
      
      const aiCheck = health.checks?.find(check => check.service === 'google-ai');
      if (!aiCheck) {
        throw new Error('AI service check not found');
      }

      if (aiCheck.status === 'unhealthy') {
        throw new Error(`AI service unhealthy: ${aiCheck.error}`);
      }

      logInfo(`AI service status: ${aiCheck.status}`);
    });
  }

  // Test build artifacts
  async testBuildArtifacts() {
    await this.runTest('Build Artifacts', async () => {
      const webBuildPath = path.join(__dirname, '../apps/web/.next');
      const agentsBuildPath = path.join(__dirname, '../apps/agents/dist');

      if (!fs.existsSync(webBuildPath)) {
        throw new Error('Web app build artifacts not found');
      }

      // Agents build is optional for now
      if (fs.existsSync(agentsBuildPath)) {
        logInfo('Agents service build artifacts found');
      } else {
        logWarning('Agents service build artifacts not found (optional)');
      }
    });
  }

  // Test TypeScript compilation
  async testTypeScript() {
    await this.runTest('TypeScript Compilation', async () => {
      try {
        execSync('cd apps/web && pnpm type-check', { 
          stdio: 'pipe',
          timeout: 60000,
        });
        logInfo('Web app TypeScript compilation successful');
      } catch (error) {
        throw new Error('Web app TypeScript compilation failed');
      }
    });
  }

  // Test package dependencies
  async testDependencies() {
    await this.runTest('Package Dependencies', async () => {
      try {
        execSync('pnpm install --frozen-lockfile', { 
          stdio: 'pipe',
          timeout: 120000,
        });
        logInfo('All dependencies installed successfully');
      } catch (error) {
        throw new Error('Dependency installation failed');
      }
    });
  }

  // Test API endpoints
  async testAPIEndpoints() {
    const endpoints = [
      '/api/health',
      '/api/auth/session',
    ];

    for (const endpoint of endpoints) {
      await this.runWarningTest(`API Endpoint: ${endpoint}`, async () => {
        const response = await fetch(`${this.webAppUrl}${endpoint}`, {
          timeout: 5000,
        });

        // Some endpoints might return 401 for unauthenticated requests, which is OK
        if (response.status >= 500) {
          throw new Error(`Server error: ${response.status}`);
        }

        logInfo(`${endpoint}: ${response.status}`);
      });
    }
  }

  // Run all tests
  async runAllTests() {
    log('\n🚀 Starting Metamorphic Flux Integration Tests\n', 'cyan');

    await this.testEnvironment();
    await this.testDependencies();
    await this.testTypeScript();
    await this.testBuildArtifacts();
    await this.testWebAppHealth();
    await this.testDatabase();
    await this.testAIService();
    await this.testAgentsService();
    await this.testAPIEndpoints();

    // Print summary
    log('\n📊 Test Results Summary\n', 'cyan');
    log(`Total Tests: ${this.results.total}`, 'blue');
    logSuccess(`Passed: ${this.results.passed}`);
    logError(`Failed: ${this.results.failed}`);
    logWarning(`Warnings: ${this.results.warnings}`);

    const successRate = ((this.results.passed / this.results.total) * 100).toFixed(1);
    log(`\nSuccess Rate: ${successRate}%`, 'blue');

    if (this.results.failed === 0) {
      log('\n🎉 All critical tests passed! System is ready for deployment.', 'green');
      process.exit(0);
    } else {
      log('\n💥 Some tests failed. Please fix the issues before deployment.', 'red');
      process.exit(1);
    }
  }
}

// Run the tests
if (require.main === module) {
  const tester = new IntegrationTester();
  tester.runAllTests().catch(error => {
    logError(`Integration test suite failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = IntegrationTester;
