"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2";
exports.ids = ["vendor-chunks/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BrowserCookieAuthStorageAdapter: () => (/* binding */ BrowserCookieAuthStorageAdapter),\n/* harmony export */   CookieAuthStorageAdapter: () => (/* binding */ CookieAuthStorageAdapter),\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   createSupabaseClient: () => (/* binding */ createSupabaseClient),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parseCookies: () => (/* binding */ export_parseCookies),\n/* harmony export */   parseSupabaseCookie: () => (/* binding */ parseSupabaseCookie),\n/* harmony export */   serializeCookie: () => (/* binding */ export_serializeCookie),\n/* harmony export */   stringifySupabaseSession: () => (/* binding */ stringifySupabaseSession)\n/* harmony export */ });\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jose */ \"(ssr)/../../node_modules/.pnpm/jose@4.15.9/node_modules/jose/dist/node/esm/util/base64url.js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/index.js\");\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\n// ../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js\nvar require_cookie = __commonJS({\n  \"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js\"(exports) {\n    \"use strict\";\n    exports.parse = parse3;\n    exports.serialize = serialize3;\n    var __toString = Object.prototype.toString;\n    var fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n    function parse3(str, options) {\n      if (typeof str !== \"string\") {\n        throw new TypeError(\"argument str must be a string\");\n      }\n      var obj = {};\n      var opt = options || {};\n      var dec = opt.decode || decode;\n      var index = 0;\n      while (index < str.length) {\n        var eqIdx = str.indexOf(\"=\", index);\n        if (eqIdx === -1) {\n          break;\n        }\n        var endIdx = str.indexOf(\";\", index);\n        if (endIdx === -1) {\n          endIdx = str.length;\n        } else if (endIdx < eqIdx) {\n          index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n          continue;\n        }\n        var key = str.slice(index, eqIdx).trim();\n        if (void 0 === obj[key]) {\n          var val = str.slice(eqIdx + 1, endIdx).trim();\n          if (val.charCodeAt(0) === 34) {\n            val = val.slice(1, -1);\n          }\n          obj[key] = tryDecode(val, dec);\n        }\n        index = endIdx + 1;\n      }\n      return obj;\n    }\n    function serialize3(name, val, options) {\n      var opt = options || {};\n      var enc = opt.encode || encode;\n      if (typeof enc !== \"function\") {\n        throw new TypeError(\"option encode is invalid\");\n      }\n      if (!fieldContentRegExp.test(name)) {\n        throw new TypeError(\"argument name is invalid\");\n      }\n      var value = enc(val);\n      if (value && !fieldContentRegExp.test(value)) {\n        throw new TypeError(\"argument val is invalid\");\n      }\n      var str = name + \"=\" + value;\n      if (null != opt.maxAge) {\n        var maxAge = opt.maxAge - 0;\n        if (isNaN(maxAge) || !isFinite(maxAge)) {\n          throw new TypeError(\"option maxAge is invalid\");\n        }\n        str += \"; Max-Age=\" + Math.floor(maxAge);\n      }\n      if (opt.domain) {\n        if (!fieldContentRegExp.test(opt.domain)) {\n          throw new TypeError(\"option domain is invalid\");\n        }\n        str += \"; Domain=\" + opt.domain;\n      }\n      if (opt.path) {\n        if (!fieldContentRegExp.test(opt.path)) {\n          throw new TypeError(\"option path is invalid\");\n        }\n        str += \"; Path=\" + opt.path;\n      }\n      if (opt.expires) {\n        var expires = opt.expires;\n        if (!isDate(expires) || isNaN(expires.valueOf())) {\n          throw new TypeError(\"option expires is invalid\");\n        }\n        str += \"; Expires=\" + expires.toUTCString();\n      }\n      if (opt.httpOnly) {\n        str += \"; HttpOnly\";\n      }\n      if (opt.secure) {\n        str += \"; Secure\";\n      }\n      if (opt.priority) {\n        var priority = typeof opt.priority === \"string\" ? opt.priority.toLowerCase() : opt.priority;\n        switch (priority) {\n          case \"low\":\n            str += \"; Priority=Low\";\n            break;\n          case \"medium\":\n            str += \"; Priority=Medium\";\n            break;\n          case \"high\":\n            str += \"; Priority=High\";\n            break;\n          default:\n            throw new TypeError(\"option priority is invalid\");\n        }\n      }\n      if (opt.sameSite) {\n        var sameSite = typeof opt.sameSite === \"string\" ? opt.sameSite.toLowerCase() : opt.sameSite;\n        switch (sameSite) {\n          case true:\n            str += \"; SameSite=Strict\";\n            break;\n          case \"lax\":\n            str += \"; SameSite=Lax\";\n            break;\n          case \"strict\":\n            str += \"; SameSite=Strict\";\n            break;\n          case \"none\":\n            str += \"; SameSite=None\";\n            break;\n          default:\n            throw new TypeError(\"option sameSite is invalid\");\n        }\n      }\n      return str;\n    }\n    function decode(str) {\n      return str.indexOf(\"%\") !== -1 ? decodeURIComponent(str) : str;\n    }\n    function encode(val) {\n      return encodeURIComponent(val);\n    }\n    function isDate(val) {\n      return __toString.call(val) === \"[object Date]\" || val instanceof Date;\n    }\n    function tryDecode(str, decode2) {\n      try {\n        return decode2(str);\n      } catch (e) {\n        return str;\n      }\n    }\n  }\n});\n\n// src/browserCookieStorage.ts\nvar import_cookie2 = __toESM(require_cookie());\n\n// src/utils/cookies.ts\nvar import_cookie = __toESM(require_cookie());\n\nfunction parseSupabaseCookie(str) {\n  if (!str) {\n    return null;\n  }\n  try {\n    const session = JSON.parse(str);\n    if (!session) {\n      return null;\n    }\n    if (session.constructor.name === \"Object\") {\n      return session;\n    }\n    if (session.constructor.name !== \"Array\") {\n      throw new Error(`Unexpected format: ${session.constructor.name}`);\n    }\n    const [_header, payloadStr, _signature] = session[0].split(\".\");\n    const payload = jose__WEBPACK_IMPORTED_MODULE_0__.decode(payloadStr);\n    const decoder = new TextDecoder();\n    const { exp, sub, ...user } = JSON.parse(decoder.decode(payload));\n    return {\n      expires_at: exp,\n      expires_in: exp - Math.round(Date.now() / 1e3),\n      token_type: \"bearer\",\n      access_token: session[0],\n      refresh_token: session[1],\n      provider_token: session[2],\n      provider_refresh_token: session[3],\n      user: {\n        id: sub,\n        factors: session[4],\n        ...user\n      }\n    };\n  } catch (err) {\n    console.warn(\"Failed to parse cookie string:\", err);\n    return null;\n  }\n}\nfunction stringifySupabaseSession(session) {\n  var _a;\n  return JSON.stringify([\n    session.access_token,\n    session.refresh_token,\n    session.provider_token,\n    session.provider_refresh_token,\n    ((_a = session.user) == null ? void 0 : _a.factors) ?? null\n  ]);\n}\n\n// src/utils/helpers.ts\nfunction isBrowser() {\n  return typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n}\n\n// src/utils/constants.ts\nvar DEFAULT_COOKIE_OPTIONS = {\n  path: \"/\",\n  sameSite: \"lax\",\n  maxAge: 60 * 60 * 24 * 365 * 1e3\n};\n\n// src/chunker.ts\nfunction createChunkRegExp(chunkSize) {\n  return new RegExp(\".{1,\" + chunkSize + \"}\", \"g\");\n}\nvar MAX_CHUNK_SIZE = 3180;\nvar MAX_CHUNK_REGEXP = createChunkRegExp(MAX_CHUNK_SIZE);\nfunction createChunks(key, value, chunkSize) {\n  const re = chunkSize !== void 0 ? createChunkRegExp(chunkSize) : MAX_CHUNK_REGEXP;\n  const chunkCount = Math.ceil(value.length / (chunkSize ?? MAX_CHUNK_SIZE));\n  if (chunkCount === 1) {\n    return [{ name: key, value }];\n  }\n  const chunks = [];\n  const values = value.match(re);\n  values == null ? void 0 : values.forEach((value2, i) => {\n    const name = `${key}.${i}`;\n    chunks.push({ name, value: value2 });\n  });\n  return chunks;\n}\nfunction combineChunks(key, retrieveChunk = () => {\n  return null;\n}) {\n  let values = [];\n  for (let i = 0; ; i++) {\n    const chunkName = `${key}.${i}`;\n    const chunk = retrieveChunk(chunkName);\n    if (!chunk) {\n      break;\n    }\n    values.push(chunk);\n  }\n  return values.length ? values.join(\"\") : null;\n}\n\n// src/cookieAuthStorageAdapter.ts\nvar CookieAuthStorageAdapter = class {\n  constructor(cookieOptions) {\n    this.cookieOptions = {\n      ...DEFAULT_COOKIE_OPTIONS,\n      ...cookieOptions,\n      maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n    };\n  }\n  getItem(key) {\n    const value = this.getCookie(key);\n    if (key.endsWith(\"-code-verifier\") && value) {\n      return value;\n    }\n    if (value) {\n      return JSON.stringify(parseSupabaseCookie(value));\n    }\n    const chunks = combineChunks(key, (chunkName) => {\n      return this.getCookie(chunkName);\n    });\n    return chunks !== null ? JSON.stringify(parseSupabaseCookie(chunks)) : null;\n  }\n  setItem(key, value) {\n    if (key.endsWith(\"-code-verifier\")) {\n      this.setCookie(key, value);\n      return;\n    }\n    let session = JSON.parse(value);\n    const sessionStr = stringifySupabaseSession(session);\n    const sessionChunks = createChunks(key, sessionStr);\n    sessionChunks.forEach((sess) => {\n      this.setCookie(sess.name, sess.value);\n    });\n  }\n  removeItem(key) {\n    this._deleteSingleCookie(key);\n    this._deleteChunkedCookies(key);\n  }\n  _deleteSingleCookie(key) {\n    if (this.getCookie(key)) {\n      this.deleteCookie(key);\n    }\n  }\n  _deleteChunkedCookies(key, from = 0) {\n    for (let i = from; ; i++) {\n      const cookieName = `${key}.${i}`;\n      const value = this.getCookie(cookieName);\n      if (value === void 0) {\n        break;\n      }\n      this.deleteCookie(cookieName);\n    }\n  }\n};\n\n// src/browserCookieStorage.ts\nvar BrowserCookieAuthStorageAdapter = class extends CookieAuthStorageAdapter {\n  constructor(cookieOptions) {\n    super(cookieOptions);\n  }\n  getCookie(name) {\n    if (!isBrowser())\n      return null;\n    const cookies = (0, import_cookie2.parse)(document.cookie);\n    return cookies[name];\n  }\n  setCookie(name, value) {\n    if (!isBrowser())\n      return null;\n    document.cookie = (0, import_cookie2.serialize)(name, value, {\n      ...this.cookieOptions,\n      httpOnly: false\n    });\n  }\n  deleteCookie(name) {\n    if (!isBrowser())\n      return null;\n    document.cookie = (0, import_cookie2.serialize)(name, \"\", {\n      ...this.cookieOptions,\n      maxAge: 0,\n      httpOnly: false\n    });\n  }\n};\n\n// src/createClient.ts\n\nfunction createSupabaseClient(supabaseUrl, supabaseKey, options) {\n  var _a;\n  const browser = isBrowser();\n  return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseKey, {\n    ...options,\n    auth: {\n      flowType: \"pkce\",\n      autoRefreshToken: browser,\n      detectSessionInUrl: browser,\n      persistSession: true,\n      storage: options.auth.storage,\n      // fix this in supabase-js\n      ...((_a = options.auth) == null ? void 0 : _a.storageKey) ? {\n        storageKey: options.auth.storageKey\n      } : {}\n    }\n  });\n}\nvar export_parseCookies = import_cookie.parse;\nvar export_serializeCookie = import_cookie.serialize;\n\n/*! Bundled license information:\n\ncookie/index.js:\n  (*!\n   * cookie\n   * Copyright(c) 2012-2014 Roman Shtylman\n   * Copyright(c) 2015 Douglas Christopher Wilson\n   * MIT Licensed\n   *)\n*/\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+auth-helpers-shared@0.7.0_@supabase+supabase-js@2.50.2/node_modules/@supabase/auth-helpers-shared/dist/index.mjs\n");

/***/ })

};
;