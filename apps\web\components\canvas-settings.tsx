'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useCanvasSettings, useCanvasStore } from '@/lib/stores/canvas-store';
import {
  Settings,
  Grid3X3,
  Layers,
  Zap,
  Palette,
  Monitor,
  Moon,
  Sun,
  RotateCcw,
  Save,
  Download,
  Upload,
  X,
} from 'lucide-react';

interface CanvasSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

export function CanvasSettings({ isOpen, onClose, className = '' }: CanvasSettingsProps): React.JSX.Element {
  const settings = useCanvasSettings();
  const { updateSettings, resetSettings, exportCanvas, importCanvas } = useCanvasStore();
  const [localSettings, setLocalSettings] = useState(settings);

  if (!isOpen) return <div />;

  const handleSave = () => {
    updateSettings(localSettings);
    onClose();
  };

  const handleReset = () => {
    resetSettings();
    setLocalSettings(useCanvasStore.getState().settings);
  };

  const handleExport = () => {
    const data = exportCanvas();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `canvas-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          importCanvas(data);
          setLocalSettings(useCanvasStore.getState().settings);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      
      {/* Settings Panel */}
      <div className={`
        fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
        w-full max-w-2xl max-h-[80vh] overflow-y-auto
        bg-card border rounded-lg shadow-xl z-50
        ${className}
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-flux-500" />
            <h2 className="text-lg font-semibold">Canvas Settings</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Settings Content */}
        <div className="p-6 space-y-6">
          {/* Appearance Settings */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center">
              <Palette className="h-4 w-4 mr-2" />
              Appearance
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Theme</label>
                <div className="flex items-center space-x-1">
                  <Button
                    size="sm"
                    variant={localSettings.theme === 'light' ? 'default' : 'outline'}
                    onClick={() => setLocalSettings({ ...localSettings, theme: 'light' })}
                  >
                    <Sun className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant={localSettings.theme === 'dark' ? 'default' : 'outline'}
                    onClick={() => setLocalSettings({ ...localSettings, theme: 'dark' })}
                  >
                    <Moon className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant={localSettings.theme === 'auto' ? 'default' : 'outline'}
                    onClick={() => setLocalSettings({ ...localSettings, theme: 'auto' })}
                  >
                    <Monitor className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Grid Settings */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center">
              <Grid3X3 className="h-4 w-4 mr-2" />
              Grid & Snapping
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Show Grid</label>
                <Button
                  size="sm"
                  variant={localSettings.gridEnabled ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ ...localSettings, gridEnabled: !localSettings.gridEnabled })}
                >
                  {localSettings.gridEnabled ? 'On' : 'Off'}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Snap to Grid</label>
                <Button
                  size="sm"
                  variant={localSettings.snapToGrid ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ ...localSettings, snapToGrid: !localSettings.snapToGrid })}
                >
                  {localSettings.snapToGrid ? 'On' : 'Off'}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Grid Size</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="10"
                    max="50"
                    step="5"
                    value={localSettings.gridSize}
                    onChange={(e) => setLocalSettings({ ...localSettings, gridSize: parseInt(e.target.value) })}
                    className="w-20"
                  />
                  <span className="text-xs text-muted-foreground w-8">{localSettings.gridSize}px</span>
                </div>
              </div>
            </div>
          </div>

          {/* View Settings */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center">
              <Layers className="h-4 w-4 mr-2" />
              View Options
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Show Minimap</label>
                <Button
                  size="sm"
                  variant={localSettings.showMinimap ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ ...localSettings, showMinimap: !localSettings.showMinimap })}
                >
                  {localSettings.showMinimap ? 'On' : 'Off'}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Show Metrics</label>
                <Button
                  size="sm"
                  variant={localSettings.showMetrics ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ ...localSettings, showMetrics: !localSettings.showMetrics })}
                >
                  {localSettings.showMetrics ? 'On' : 'Off'}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Auto Save</label>
                <Button
                  size="sm"
                  variant={localSettings.autoSave ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ ...localSettings, autoSave: !localSettings.autoSave })}
                >
                  {localSettings.autoSave ? 'On' : 'Off'}
                </Button>
              </div>
            </div>
          </div>

          {/* Animation Settings */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center">
              <Zap className="h-4 w-4 mr-2" />
              Animations
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Enable Animations</label>
                <Button
                  size="sm"
                  variant={localSettings.animations.enabled ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ 
                    ...localSettings, 
                    animations: { ...localSettings.animations, enabled: !localSettings.animations.enabled }
                  })}
                >
                  {localSettings.animations.enabled ? 'On' : 'Off'}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Animation Speed</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="100"
                    max="1000"
                    step="50"
                    value={localSettings.animations.duration}
                    onChange={(e) => setLocalSettings({ 
                      ...localSettings, 
                      animations: { ...localSettings.animations, duration: parseInt(e.target.value) }
                    })}
                    className="w-20"
                  />
                  <span className="text-xs text-muted-foreground w-12">{localSettings.animations.duration}ms</span>
                </div>
              </div>
            </div>
          </div>

          {/* Performance Settings */}
          <div>
            <h3 className="text-sm font-medium text-foreground mb-3 flex items-center">
              <Monitor className="h-4 w-4 mr-2" />
              Performance
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Render Optimization</label>
                <Button
                  size="sm"
                  variant={localSettings.performance.renderOptimization ? 'default' : 'outline'}
                  onClick={() => setLocalSettings({ 
                    ...localSettings, 
                    performance: { ...localSettings.performance, renderOptimization: !localSettings.performance.renderOptimization }
                  })}
                >
                  {localSettings.performance.renderOptimization ? 'On' : 'Off'}
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <label className="text-sm text-muted-foreground">Max Nodes</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="50"
                    max="500"
                    step="25"
                    value={localSettings.performance.maxNodes}
                    onChange={(e) => setLocalSettings({ 
                      ...localSettings, 
                      performance: { ...localSettings.performance, maxNodes: parseInt(e.target.value) }
                    })}
                    className="w-20"
                  />
                  <span className="text-xs text-muted-foreground w-8">{localSettings.performance.maxNodes}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-muted/30">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" onClick={handleImport}>
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="flux-gradient">
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
