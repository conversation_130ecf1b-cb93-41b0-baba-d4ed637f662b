/**
 * @metamorphic-flux/optimizer
 * 
 * Quantum-enhanced budget optimization with Google Willow integration
 * and classical fallback algorithms.
 * 
 * Features:
 * - Google Willow quantum processor integration
 * - Multiple classical optimization algorithms
 * - Hybrid quantum-classical optimization
 * - Automatic algorithm selection
 * - Performance monitoring and comparison
 * - Enterprise-grade error handling
 */

// Main exports
export { QuantumOptimizer } from './quantum/optimizer';
export { WillowBridge } from './quantum/willow-bridge';
export { ClassicalSolver } from './classical/solver';

// Type exports
export type {
  OptimizationRequest,
  OptimizationResult,
  BudgetConstraint,
  ChannelConfig,
  OptimizationObjective,
  BudgetAllocation,
  WillowConfig,
  QuantumCircuitConfig,
  Matrix,
  Vector,
  OptimizationMetrics,
  PerformanceMetrics,
} from './types';

// Error exports
export {
  QuantumOptimizationError,
  WillowAPIError,
  ClassicalOptimizationError,
} from './types';

// Schema exports for validation
export { Schemas } from './types';

// Utility functions
export { createOptimizer, validateRequest, formatResult } from './utils';

// Constants
export const QUANTUM_CONSTANTS = {
  WILLOW_MAX_QUBITS: 105,
  DEFAULT_COHERENCE_TIME: 100, // microseconds
  DEFAULT_ERROR_THRESHOLD: 0.001,
  MAX_CIRCUIT_DEPTH: 1000,
  DEFAULT_SHOTS: 10000,
} as const;

export const OPTIMIZATION_ALGORITHMS = {
  QUANTUM: ['vqe', 'qaoa', 'quantum_annealing'] as const,
  CLASSICAL: ['sqp', 'genetic', 'pso', 'simulated_annealing', 'interior_point'] as const,
  HYBRID: ['quantum_classical', 'adaptive'] as const,
} as const;

// Version
export const VERSION = '1.0.0';

/**
 * Quick start function for simple optimization
 */
export async function optimizeBudget(
  totalBudget: number,
  channels: ChannelConfig[],
  objective: 'maximize_revenue' | 'maximize_conversions' | 'minimize_cpa' | 'maximize_roas' = 'maximize_revenue',
  willowConfig?: Partial<WillowConfig>
): Promise<OptimizationResult> {
  const optimizer = createOptimizer(willowConfig);
  
  const request: OptimizationRequest = {
    id: `quick_${Date.now()}`,
    totalBudget,
    channels,
    constraints: [
      {
        id: 'total_budget',
        type: 'total_budget',
        value: totalBudget,
        operator: '<=',
        priority: 10,
        flexible: false,
        tolerance: 0,
      },
    ],
    objective: {
      primary: objective,
      weights: { primary: 1.0, secondary: 0.0 },
      timeHorizon: 'monthly',
    },
    timeframe: {
      start: new Date(),
      end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    },
    preferences: {
      useQuantumOptimization: true,
      quantumFallbackThreshold: 0.95,
      maxIterations: 1000,
      convergenceThreshold: 0.001,
      riskTolerance: 'moderate',
    },
  };

  return await optimizer.optimize(request);
}

/**
 * Batch optimization for multiple scenarios
 */
export async function optimizeBatch(
  requests: OptimizationRequest[],
  willowConfig?: Partial<WillowConfig>
): Promise<OptimizationResult[]> {
  const optimizer = createOptimizer(willowConfig);
  const results: OptimizationResult[] = [];

  for (const request of requests) {
    try {
      const result = await optimizer.optimize(request);
      results.push(result);
    } catch (error) {
      console.error(`Batch optimization failed for request ${request.id}:`, error);
      // Continue with other requests
    }
  }

  await optimizer.cleanup();
  return results;
}

/**
 * Compare quantum vs classical performance
 */
export async function compareOptimizationMethods(
  request: OptimizationRequest,
  willowConfig?: Partial<WillowConfig>
): Promise<{
  quantum: OptimizationResult | null;
  classical: OptimizationResult;
  comparison: {
    quantumAdvantage: number;
    performanceDifference: number;
    recommendation: 'quantum' | 'classical' | 'hybrid';
  };
}> {
  const optimizer = createOptimizer(willowConfig);
  const classicalSolver = new ClassicalSolver();

  // Run classical optimization
  const classicalResult = await classicalSolver.optimize(request);

  // Attempt quantum optimization
  let quantumResult: OptimizationResult | null = null;
  try {
    const quantumRequest = { ...request, preferences: { ...request.preferences, useQuantumOptimization: true } };
    quantumResult = await optimizer.optimize(quantumRequest);
  } catch (error) {
    console.warn('Quantum optimization failed during comparison:', error);
  }

  // Calculate comparison metrics
  let quantumAdvantage = 0;
  let performanceDifference = 0;
  let recommendation: 'quantum' | 'classical' | 'hybrid' = 'classical';

  if (quantumResult) {
    quantumAdvantage = quantumResult.quantumMetrics?.quantumAdvantage || 0;
    
    const quantumObjective = quantumResult.optimizationMetrics.objectiveValue;
    const classicalObjective = classicalResult.optimizationMetrics.objectiveValue;
    
    performanceDifference = (quantumObjective - classicalObjective) / Math.abs(classicalObjective);
    
    if (performanceDifference > 0.1 && quantumAdvantage > 0.7) {
      recommendation = 'quantum';
    } else if (performanceDifference > 0.05 || quantumAdvantage > 0.5) {
      recommendation = 'hybrid';
    }
  }

  await optimizer.cleanup();

  return {
    quantum: quantumResult,
    classical: classicalResult,
    comparison: {
      quantumAdvantage,
      performanceDifference,
      recommendation,
    },
  };
}

/**
 * Real-time optimization monitoring
 */
export class OptimizationMonitor {
  private optimizer: QuantumOptimizer;
  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;

  constructor(willowConfig?: Partial<WillowConfig>) {
    this.optimizer = createOptimizer(willowConfig);
  }

  async startMonitoring(
    request: OptimizationRequest,
    callback: (result: OptimizationResult) => void,
    intervalMs: number = 60000 // 1 minute
  ): Promise<void> {
    if (this.isMonitoring) {
      throw new Error('Monitoring already active');
    }

    this.isMonitoring = true;
    
    const runOptimization = async () => {
      try {
        const result = await this.optimizer.optimize(request);
        callback(result);
      } catch (error) {
        console.error('Monitoring optimization failed:', error);
      }
    };

    // Run initial optimization
    await runOptimization();

    // Set up periodic optimization
    this.monitoringInterval = setInterval(runOptimization, intervalMs);
  }

  stopMonitoring(): void {
    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  async cleanup(): Promise<void> {
    this.stopMonitoring();
    await this.optimizer.cleanup();
  }
}

// Default export
export default {
  QuantumOptimizer,
  WillowBridge,
  ClassicalSolver,
  optimizeBudget,
  optimizeBatch,
  compareOptimizationMethods,
  OptimizationMonitor,
  QUANTUM_CONSTANTS,
  OPTIMIZATION_ALGORITHMS,
  VERSION,
};
