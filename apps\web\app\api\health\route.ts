import { NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

interface HealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime?: number;
  error?: string;
  details?: Record<string, any>;
}

async function checkDatabase(): Promise<HealthCheck> {
  const start = Date.now();

  try {
    const supabase = createServerClient();
    const { data, error } = await supabase
      .from('organizations')
      .select('count')
      .limit(1);

    const responseTime = Date.now() - start;

    if (error) {
      return {
        service: 'database',
        status: 'unhealthy',
        responseTime,
        error: error.message,
      };
    }

    return {
      service: 'database',
      status: responseTime < 500 ? 'healthy' : 'degraded',
      responseTime,
      details: { connected: true },
    };
  } catch (error) {
    return {
      service: 'database',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function checkGoogleAI(): Promise<HealthCheck> {
  const start = Date.now();

  try {
    if (!process.env.GOOGLE_AI_API_KEY) {
      return {
        service: 'google-ai',
        status: 'degraded',
        error: 'API key not configured',
      };
    }

    const apiKey = process.env.GOOGLE_AI_API_KEY;
    const isValidFormat = apiKey.startsWith('AIza') && apiKey.length === 39;

    const responseTime = Date.now() - start;

    return {
      service: 'google-ai',
      status: isValidFormat ? 'healthy' : 'degraded',
      responseTime,
      details: {
        apiKeyConfigured: true,
        validFormat: isValidFormat,
      },
    };
  } catch (error) {
    return {
      service: 'google-ai',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

async function checkAgentsService(): Promise<HealthCheck> {
  const start = Date.now();

  try {
    const agentsUrl = process.env.NEXT_PUBLIC_AGENTS_URL;

    if (!agentsUrl) {
      return {
        service: 'agents',
        status: 'degraded',
        error: 'Agents service URL not configured',
      };
    }

    // For now, just check if URL is configured
    // In production, you'd make an actual HTTP request
    const responseTime = Date.now() - start;

    return {
      service: 'agents',
      status: 'healthy',
      responseTime,
      details: {
        urlConfigured: true,
        url: agentsUrl,
      },
    };
  } catch (error) {
    return {
      service: 'agents',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function GET(): Promise<NextResponse> {
  const startTime = Date.now();

  try {
    // Run all health checks in parallel
    const [databaseCheck, googleAICheck, agentsCheck] = await Promise.all([
      checkDatabase(),
      checkGoogleAI(),
      checkAgentsService(),
    ]);

    const checks = [databaseCheck, googleAICheck, agentsCheck];

    // Determine overall status
    const unhealthyCount = checks.filter(check => check.status === 'unhealthy').length;
    const degradedCount = checks.filter(check => check.status === 'degraded').length;

    const overallStatus = unhealthyCount > 0 ? 'unhealthy' :
                         degradedCount > 0 ? 'degraded' : 'healthy';

    const summary = {
      total: checks.length,
      healthy: checks.filter(check => check.status === 'healthy').length,
      degraded: checks.filter(check => check.status === 'degraded').length,
      unhealthy: checks.filter(check => check.status === 'unhealthy').length,
    };

    const health = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      totalResponseTime: Date.now() - startTime,
      checks,
      summary,
      environment: process.env.NODE_ENV || 'development',
    };

    // Set appropriate HTTP status code
    const statusCode = overallStatus === 'healthy' ? 200 :
                      overallStatus === 'degraded' ? 200 : 503;

    return NextResponse.json(health, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown system error',
        uptime: process.uptime(),
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );
  }
}
