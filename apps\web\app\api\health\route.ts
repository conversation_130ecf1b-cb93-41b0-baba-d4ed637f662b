import { NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase/server';

export async function GET(): Promise<NextResponse> {
  try {
    const supabase = createServerClient();
    
    // Test database connection
    const { data, error } = await supabase
      .from('organizations')
      .select('count')
      .limit(1);

    if (error) {
      return NextResponse.json(
        { 
          status: 'error',
          message: 'Database connection failed',
          error: error.message 
        },
        { status: 500 }
      );
    }

    // Test AI service (if API key is configured)
    let aiStatus = 'not_configured';
    if (process.env.GOOGLE_AI_API_KEY) {
      aiStatus = 'configured';
      // Could add a test embedding call here if needed
    }

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: 'connected',
        ai: aiStatus,
        auth: 'enabled'
      },
      version: process.env.npm_package_version || '0.1.0'
    });

  } catch (error) {
    return NextResponse.json(
      { 
        status: 'error',
        message: 'Health check failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
