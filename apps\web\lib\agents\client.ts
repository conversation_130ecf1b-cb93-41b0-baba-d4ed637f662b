import type { 
  AgentContext, 
  CopyAgentInput, 
  DesignAgentInput, 
  AnalystAgentInput,
  AgentExecutionResult,
  WorkflowState 
} from '@metamorphic-flux/agents/src/types';

export class AgentsClient {
  private baseUrl: string;

  constructor(baseUrl: string = process.env.NEXT_PUBLIC_AGENTS_URL || 'http://localhost:3001') {
    this.baseUrl = baseUrl;
  }

  async executeWorkflow(context: AgentContext): Promise<WorkflowState> {
    const response = await fetch(`${this.baseUrl}/workflow/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(context),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Workflow execution failed: ${error.error || error.message}`);
    }

    return response.json();
  }

  async executeCopyAgent(input: CopyAgentInput, context: AgentContext): Promise<AgentExecutionResult> {
    return this.executeAgent('copy', input, context);
  }

  async executeDesignAgent(input: DesignAgentInput, context: AgentContext): Promise<AgentExecutionResult> {
    return this.executeAgent('design', input, context);
  }

  async executeAnalystAgent(input: AnalystAgentInput, context: AgentContext): Promise<AgentExecutionResult> {
    return this.executeAgent('analyst', input, context);
  }

  private async executeAgent(
    agentType: string, 
    input: any, 
    context: AgentContext
  ): Promise<AgentExecutionResult> {
    const response = await fetch(`${this.baseUrl}/agents/${agentType}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ input, context }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`${agentType} agent execution failed: ${error.error || error.message}`);
    }

    const result = await response.json();
    return result.result;
  }

  async getAgentCapabilities(agentType: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/agents/${agentType}/capabilities`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to get agent capabilities: ${error.error || error.message}`);
    }

    return response.json();
  }

  async getAgentStatus(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/agents/status`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Failed to get agent status: ${error.error || error.message}`);
    }

    return response.json();
  }

  async healthCheck(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/health`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(`Agents service health check failed: ${error.error || error.message}`);
    }

    return response.json();
  }
}

// Singleton instance
export const agentsClient = new AgentsClient();
