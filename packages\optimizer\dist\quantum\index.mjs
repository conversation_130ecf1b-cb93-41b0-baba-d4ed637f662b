// src/quantum/willow-bridge.ts
import axios from "axios";

// src/types/index.ts
import { z } from "zod";
var BudgetConstraintSchema = z.object({
  id: z.string(),
  type: z.enum(["total_budget", "channel_budget", "daily_budget", "cpa_target", "roas_target"]),
  value: z.number().positive(),
  operator: z.enum(["<=", ">=", "=", "<", ">"]),
  priority: z.number().min(1).max(10).default(5),
  flexible: z.boolean().default(false),
  tolerance: z.number().min(0).max(1).default(0.1)
});
var ChannelConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(["facebook", "google", "linkedin", "twitter", "tiktok", "email", "sms"]),
  minBudget: z.number().min(0),
  maxBudget: z.number().positive(),
  costModel: z.enum(["cpc", "cpm", "cpa", "cpv", "flat"]),
  baseRate: z.number().positive(),
  scalingFactor: z.number().positive().default(1),
  saturationPoint: z.number().positive().optional(),
  conversionRate: z.number().min(0).max(1),
  averageOrderValue: z.number().positive(),
  seasonality: z.array(z.number()).length(12).default(Array(12).fill(1)),
  competitiveIndex: z.number().min(0).max(10).default(5)
});
var OptimizationObjectiveSchema = z.object({
  primary: z.enum(["maximize_revenue", "maximize_conversions", "minimize_cpa", "maximize_roas"]),
  secondary: z.enum(["maximize_reach", "minimize_cost", "maximize_engagement"]).optional(),
  weights: z.object({
    primary: z.number().min(0).max(1).default(0.8),
    secondary: z.number().min(0).max(1).default(0.2)
  }),
  timeHorizon: z.enum(["daily", "weekly", "monthly", "quarterly"]).default("monthly")
});
var BudgetAllocationSchema = z.object({
  channelId: z.string(),
  allocatedBudget: z.number().min(0),
  expectedConversions: z.number().min(0),
  expectedRevenue: z.number().min(0),
  expectedCPA: z.number().positive(),
  expectedROAS: z.number().positive(),
  confidence: z.number().min(0).max(1),
  riskScore: z.number().min(0).max(10)
});
var OptimizationRequestSchema = z.object({
  id: z.string(),
  totalBudget: z.number().positive(),
  channels: z.array(ChannelConfigSchema),
  constraints: z.array(BudgetConstraintSchema),
  objective: OptimizationObjectiveSchema,
  timeframe: z.object({
    start: z.date(),
    end: z.date()
  }),
  historicalData: z.object({
    channelPerformance: z.record(z.string(), z.array(z.object({
      date: z.date(),
      spend: z.number().min(0),
      conversions: z.number().min(0),
      revenue: z.number().min(0)
    }))),
    marketConditions: z.array(z.object({
      date: z.date(),
      competitiveIndex: z.number().min(0).max(10),
      seasonalityFactor: z.number().positive(),
      economicIndicator: z.number()
    }))
  }).optional(),
  preferences: z.object({
    useQuantumOptimization: z.boolean().default(true),
    quantumFallbackThreshold: z.number().min(0).max(1).default(0.95),
    maxIterations: z.number().positive().default(1e3),
    convergenceThreshold: z.number().positive().default(1e-3),
    riskTolerance: z.enum(["conservative", "moderate", "aggressive"]).default("moderate")
  }).default({})
});
var OptimizationResultSchema = z.object({
  id: z.string(),
  requestId: z.string(),
  status: z.enum(["success", "partial", "failed"]),
  method: z.enum(["quantum", "classical", "hybrid"]),
  allocations: z.array(BudgetAllocationSchema),
  totalAllocated: z.number().min(0),
  expectedTotalRevenue: z.number().min(0),
  expectedTotalConversions: z.number().min(0),
  overallROAS: z.number().positive(),
  overallCPA: z.number().positive(),
  confidence: z.number().min(0).max(1),
  riskScore: z.number().min(0).max(10),
  optimizationMetrics: z.object({
    iterations: z.number().positive(),
    convergenceTime: z.number().positive(),
    objectiveValue: z.number(),
    constraintViolations: z.array(z.object({
      constraintId: z.string(),
      violation: z.number(),
      severity: z.enum(["low", "medium", "high"])
    }))
  }),
  quantumMetrics: z.object({
    quantumAdvantage: z.number().min(0).max(1).optional(),
    coherenceTime: z.number().positive().optional(),
    gateCount: z.number().positive().optional(),
    errorRate: z.number().min(0).max(1).optional(),
    willowChipUtilization: z.number().min(0).max(1).optional()
  }).optional(),
  recommendations: z.array(z.object({
    type: z.enum(["budget_increase", "channel_rebalance", "constraint_relaxation", "timing_adjustment"]),
    description: z.string(),
    impact: z.object({
      revenueChange: z.number(),
      costChange: z.number(),
      riskChange: z.number()
    }),
    priority: z.enum(["low", "medium", "high"])
  })),
  createdAt: z.date(),
  computeTime: z.number().positive()
});
var QuantumCircuitConfigSchema = z.object({
  qubits: z.number().positive().max(105),
  // Willow chip limit
  depth: z.number().positive(),
  gateSet: z.array(z.enum(["H", "X", "Y", "Z", "CNOT", "CZ", "RX", "RY", "RZ", "SWAP"])),
  errorCorrection: z.boolean().default(true),
  coherenceTime: z.number().positive().default(100),
  // microseconds
  fidelity: z.number().min(0).max(1).default(0.999)
});
var WillowConfigSchema = z.object({
  endpoint: z.string().url(),
  apiKey: z.string(),
  projectId: z.string(),
  region: z.enum(["us-central1", "europe-west1", "asia-east1"]).default("us-central1"),
  timeout: z.number().positive().default(3e4),
  retries: z.number().min(0).max(5).default(3),
  circuitConfig: QuantumCircuitConfigSchema
});
var QuantumOptimizationError = class extends Error {
  constructor(message, code, details) {
    super(message);
    this.code = code;
    this.details = details;
    this.name = "QuantumOptimizationError";
  }
};
var WillowAPIError = class extends Error {
  constructor(message, statusCode, response) {
    super(message);
    this.statusCode = statusCode;
    this.response = response;
    this.name = "WillowAPIError";
  }
};
var ClassicalOptimizationError = class extends Error {
  constructor(message, algorithm, details) {
    super(message);
    this.algorithm = algorithm;
    this.details = details;
    this.name = "ClassicalOptimizationError";
  }
};

// src/quantum/willow-bridge.ts
var WillowBridge = class {
  client;
  config;
  circuitCache = /* @__PURE__ */ new Map();
  constructor(config) {
    this.config = config;
    this.client = axios.create({
      baseURL: config.endpoint,
      timeout: config.timeout,
      headers: {
        "Authorization": `Bearer ${config.apiKey}`,
        "Content-Type": "application/json",
        "X-Goog-User-Project": config.projectId,
        "X-Quantum-Engine": "willow-v1"
      }
    });
    this.setupInterceptors();
  }
  setupInterceptors() {
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[Willow] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => Promise.reject(error)
    );
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        const willowError = new WillowAPIError(
          error.response?.data?.message || error.message,
          error.response?.status || 500,
          error.response?.data
        );
        return Promise.reject(willowError);
      }
    );
  }
  /**
   * Check Willow quantum processor availability and status
   */
  async getProcessorStatus() {
    try {
      const response = await this.client.get("/v1/processors/willow/status");
      return response.data;
    } catch (error) {
      throw new WillowAPIError(
        "Failed to get processor status",
        500,
        { originalError: error }
      );
    }
  }
  /**
   * Create a quantum circuit for budget optimization
   */
  async createOptimizationCircuit(request, circuitConfig) {
    const circuitKey = this.generateCircuitKey(request, circuitConfig);
    if (this.circuitCache.has(circuitKey)) {
      return this.circuitCache.get(circuitKey);
    }
    const circuit = this.buildQuantumCircuit(request, circuitConfig);
    try {
      const response = await this.client.post("/v1/circuits", {
        name: `budget-optimization-${request.id}`,
        circuit,
        config: circuitConfig,
        metadata: {
          channels: request.channels.length,
          constraints: request.constraints.length,
          totalBudget: request.totalBudget,
          objective: request.objective.primary
        }
      });
      const circuitId = response.data.circuitId;
      this.circuitCache.set(circuitKey, circuitId);
      return circuitId;
    } catch (error) {
      throw new WillowAPIError(
        "Failed to create quantum circuit",
        500,
        { originalError: error }
      );
    }
  }
  /**
   * Execute quantum optimization on Willow processor
   */
  async executeOptimization(circuitId, parameters) {
    try {
      const response = await this.client.post(`/v1/circuits/${circuitId}/execute`, {
        parameters,
        shots: 1e4,
        // Number of quantum measurements
        errorCorrection: this.config.circuitConfig.errorCorrection,
        optimization: {
          level: "aggressive",
          errorMitigation: true,
          readoutCorrection: true
        }
      });
      return this.processQuantumResult(response.data);
    } catch (error) {
      throw new WillowAPIError(
        "Failed to execute quantum optimization",
        500,
        { originalError: error }
      );
    }
  }
  /**
   * Run variational quantum eigensolver (VQE) for optimization
   */
  async runVQE(hamiltonian, initialParameters, maxIterations = 100) {
    try {
      const response = await this.client.post("/v1/algorithms/vqe", {
        hamiltonian,
        initialParameters,
        maxIterations,
        convergenceThreshold: 1e-6,
        optimizer: "COBYLA",
        ansatz: "hardware_efficient"
      });
      return response.data;
    } catch (error) {
      throw new WillowAPIError(
        "VQE execution failed",
        500,
        { originalError: error }
      );
    }
  }
  /**
   * Run Quantum Approximate Optimization Algorithm (QAOA)
   */
  async runQAOA(costFunction, mixingHamiltonian, layers = 3) {
    try {
      const response = await this.client.post("/v1/algorithms/qaoa", {
        costFunction,
        mixingHamiltonian,
        layers,
        optimizer: "NELDER_MEAD",
        shots: 8192
      });
      return response.data;
    } catch (error) {
      throw new WillowAPIError(
        "QAOA execution failed",
        500,
        { originalError: error }
      );
    }
  }
  /**
   * Get quantum processor metrics and utilization
   */
  async getProcessorMetrics() {
    try {
      const response = await this.client.get("/v1/processors/willow/metrics");
      return response.data;
    } catch (error) {
      throw new WillowAPIError(
        "Failed to get processor metrics",
        500,
        { originalError: error }
      );
    }
  }
  /**
   * Build quantum circuit for budget optimization problem
   */
  buildQuantumCircuit(request, config) {
    const numChannels = request.channels.length;
    const numQubits = Math.min(numChannels * 4, config.qubits);
    const circuit = {
      qubits: numQubits,
      gates: [],
      measurements: []
    };
    for (let i = 0; i < numQubits; i++) {
      circuit.gates.push({ type: "H", qubit: i });
    }
    this.encodeConstraints(circuit, request.constraints, numChannels);
    this.encodeObjective(circuit, request.objective, numChannels);
    this.addEntanglement(circuit, numChannels);
    for (let i = 0; i < numQubits; i++) {
      circuit.measurements.push({ qubit: i, classical_bit: i });
    }
    return circuit;
  }
  encodeConstraints(circuit, constraints, numChannels) {
    constraints.forEach((constraint, idx) => {
      if (constraint.type === "total_budget") {
        for (let i = 0; i < numChannels; i++) {
          const angle = constraint.value / 1e6 * Math.PI;
          circuit.gates.push({
            type: "RY",
            qubit: i * 4,
            parameter: angle
          });
        }
      }
    });
  }
  encodeObjective(circuit, objective, numChannels) {
    const weight = objective.weights.primary;
    for (let i = 0; i < numChannels; i++) {
      const phase = weight * Math.PI / 2;
      circuit.gates.push({
        type: "RZ",
        qubit: i * 4 + 1,
        parameter: phase
      });
    }
  }
  addEntanglement(circuit, numChannels) {
    for (let i = 0; i < numChannels - 1; i++) {
      circuit.gates.push({
        type: "CNOT",
        control: i * 4,
        target: (i + 1) * 4
      });
    }
  }
  processQuantumResult(rawResult) {
    const measurements = rawResult.measurements;
    const counts = rawResult.counts;
    const result = this.decodeMeasurements(measurements, counts);
    const quantumAdvantage = this.calculateQuantumAdvantage(rawResult);
    return {
      result: result.allocations,
      confidence: result.confidence,
      quantumAdvantage,
      metrics: {
        gateCount: rawResult.circuitMetrics.gateCount,
        depth: rawResult.circuitMetrics.depth,
        errorRate: rawResult.errorMetrics.averageErrorRate,
        coherenceTime: rawResult.coherenceMetrics.averageCoherenceTime,
        executionTime: rawResult.timing.executionTime
      }
    };
  }
  decodeMeasurements(measurements, counts) {
    const totalShots = Object.values(counts).reduce((a, b) => a + Number(b), 0);
    const allocations = [];
    let confidence = 0;
    let maxCount = 0;
    let bestOutcome = "";
    for (const [outcome, count] of Object.entries(counts)) {
      const countNum = Number(count);
      if (countNum > maxCount) {
        maxCount = countNum;
        bestOutcome = outcome;
      }
    }
    confidence = maxCount / totalShots;
    const binaryString = bestOutcome;
    const numChannels = binaryString.length / 4;
    for (let i = 0; i < numChannels; i++) {
      const channelBits = binaryString.slice(i * 4, (i + 1) * 4);
      const allocation = parseInt(channelBits, 2) / 15;
      allocations.push(allocation);
    }
    return { allocations, confidence };
  }
  calculateQuantumAdvantage(result) {
    const coherenceTime = result.coherenceMetrics.averageCoherenceTime;
    const errorRate = result.errorMetrics.averageErrorRate;
    const gateTime = result.timing.averageGateTime;
    const coherenceAdvantage = Math.min(coherenceTime / (gateTime * 100), 1);
    const errorAdvantage = Math.max(0, 1 - errorRate * 100);
    return (coherenceAdvantage + errorAdvantage) / 2;
  }
  generateCircuitKey(request, config) {
    const keyData = {
      channels: request.channels.length,
      constraints: request.constraints.length,
      objective: request.objective.primary,
      qubits: config.qubits,
      depth: config.depth
    };
    return Buffer.from(JSON.stringify(keyData)).toString("base64");
  }
  /**
   * Clean up resources and close connections
   */
  async cleanup() {
    this.circuitCache.clear();
  }
};

// src/classical/solver.ts
import { cloneDeep, sum } from "lodash";
var ClassicalSolver = class {
  maxIterations;
  convergenceThreshold;
  populationSize;
  constructor(options = {}) {
    this.maxIterations = options.maxIterations || 1e3;
    this.convergenceThreshold = options.convergenceThreshold || 1e-6;
    this.populationSize = options.populationSize || 100;
  }
  /**
   * Main optimization entry point
   */
  async optimize(request) {
    const startTime = Date.now();
    try {
      const algorithm = this.selectAlgorithm(request);
      console.log(`[Classical] Using ${algorithm} algorithm`);
      let result;
      switch (algorithm) {
        case "sqp":
          result = await this.sequentialQuadraticProgramming(request);
          break;
        case "genetic":
          result = await this.geneticAlgorithm(request);
          break;
        case "pso":
          result = await this.particleSwarmOptimization(request);
          break;
        case "simulated_annealing":
          result = await this.simulatedAnnealing(request);
          break;
        case "interior_point":
          result = await this.interiorPointMethod(request);
          break;
        default:
          throw new ClassicalOptimizationError(
            `Unknown algorithm: ${algorithm}`,
            algorithm
          );
      }
      result.computeTime = Date.now() - startTime;
      result.method = "classical";
      return result;
    } catch (error) {
      throw new ClassicalOptimizationError(
        `Classical optimization failed: ${error.message}`,
        "unknown",
        { originalError: error }
      );
    }
  }
  /**
   * Sequential Quadratic Programming (SQP)
   * Best for smooth, differentiable problems with equality/inequality constraints
   */
  async sequentialQuadraticProgramming(request) {
    const numChannels = request.channels.length;
    let x = this.getInitialSolution(request);
    let iteration = 0;
    let converged = false;
    const metrics = {
      objectiveValue: 0,
      constraintViolations: 0,
      feasibility: false,
      optimality: 0,
      convergenceRate: 0
    };
    while (iteration < this.maxIterations && !converged) {
      const gradient = this.calculateGradient(x, request);
      const hessian = this.calculateHessian(x, request);
      const direction = this.solveQPSubproblem(gradient, hessian, x, request);
      const stepSize = this.lineSearch(x, direction, request);
      const newX = x.map((xi, i) => xi + stepSize * direction[i]);
      const change = this.calculateNorm(newX.map((xi, i) => xi - x[i]));
      converged = change < this.convergenceThreshold;
      x = newX;
      iteration++;
      metrics.objectiveValue = this.evaluateObjective(x, request);
      metrics.constraintViolations = this.evaluateConstraintViolations(x, request);
    }
    metrics.feasibility = metrics.constraintViolations < 1e-6;
    metrics.optimality = converged ? 1 : 0.5;
    metrics.convergenceRate = iteration / this.maxIterations;
    return this.buildResult(request, x, metrics, iteration, "sqp");
  }
  /**
   * Genetic Algorithm
   * Best for discrete, combinatorial problems with complex constraints
   */
  async geneticAlgorithm(request) {
    const numChannels = request.channels.length;
    let population = this.initializePopulation(request, this.populationSize);
    let generation = 0;
    let bestSolution = population[0];
    let bestFitness = this.evaluateFitness(bestSolution, request);
    while (generation < this.maxIterations) {
      const fitness = population.map((individual) => this.evaluateFitness(individual, request));
      const maxFitnessIndex = fitness.indexOf(Math.max(...fitness));
      if (fitness[maxFitnessIndex] > bestFitness) {
        bestSolution = cloneDeep(population[maxFitnessIndex]);
        bestFitness = fitness[maxFitnessIndex];
      }
      const parents = this.tournamentSelection(population, fitness, this.populationSize);
      const offspring = this.crossoverAndMutation(parents, request);
      population = offspring;
      generation++;
    }
    const metrics = {
      objectiveValue: this.evaluateObjective(bestSolution, request),
      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),
      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,
      optimality: 0.8,
      // GA doesn't guarantee global optimum
      convergenceRate: generation / this.maxIterations
    };
    return this.buildResult(request, bestSolution, metrics, generation, "genetic");
  }
  /**
   * Particle Swarm Optimization
   * Best for continuous optimization with multiple local optima
   */
  async particleSwarmOptimization(request) {
    const numChannels = request.channels.length;
    const numParticles = this.populationSize;
    const particles = Array(numParticles).fill(null).map(() => ({
      position: this.getInitialSolution(request),
      velocity: Array(numChannels).fill(0).map(() => (Math.random() - 0.5) * 0.1),
      bestPosition: [],
      bestFitness: -Infinity
    }));
    let globalBestPosition = particles[0].position;
    let globalBestFitness = -Infinity;
    let iteration = 0;
    const w = 0.7;
    const c1 = 1.5;
    const c2 = 1.5;
    while (iteration < this.maxIterations) {
      for (const particle of particles) {
        const fitness = this.evaluateFitness(particle.position, request);
        if (fitness > particle.bestFitness) {
          particle.bestPosition = cloneDeep(particle.position);
          particle.bestFitness = fitness;
        }
        if (fitness > globalBestFitness) {
          globalBestPosition = cloneDeep(particle.position);
          globalBestFitness = fitness;
        }
        for (let i = 0; i < numChannels; i++) {
          const r1 = Math.random();
          const r2 = Math.random();
          particle.velocity[i] = w * particle.velocity[i] + c1 * r1 * (particle.bestPosition[i] - particle.position[i]) + c2 * r2 * (globalBestPosition[i] - particle.position[i]);
          particle.position[i] += particle.velocity[i];
          particle.position[i] = Math.max(0, Math.min(1, particle.position[i]));
        }
      }
      iteration++;
    }
    const metrics = {
      objectiveValue: this.evaluateObjective(globalBestPosition, request),
      constraintViolations: this.evaluateConstraintViolations(globalBestPosition, request),
      feasibility: this.evaluateConstraintViolations(globalBestPosition, request) < 1e-6,
      optimality: 0.85,
      convergenceRate: iteration / this.maxIterations
    };
    return this.buildResult(request, globalBestPosition, metrics, iteration, "pso");
  }
  /**
   * Simulated Annealing
   * Best for discrete optimization with many local optima
   */
  async simulatedAnnealing(request) {
    let currentSolution = this.getInitialSolution(request);
    let currentEnergy = -this.evaluateFitness(currentSolution, request);
    let bestSolution = cloneDeep(currentSolution);
    let bestEnergy = currentEnergy;
    const initialTemp = 1e3;
    const finalTemp = 0.01;
    const coolingRate = 0.95;
    let temperature = initialTemp;
    let iteration = 0;
    while (temperature > finalTemp && iteration < this.maxIterations) {
      const neighbor = this.generateNeighbor(currentSolution, request);
      const neighborEnergy = -this.evaluateFitness(neighbor, request);
      const deltaE = neighborEnergy - currentEnergy;
      if (deltaE < 0 || Math.random() < Math.exp(-deltaE / temperature)) {
        currentSolution = neighbor;
        currentEnergy = neighborEnergy;
        if (currentEnergy < bestEnergy) {
          bestSolution = cloneDeep(currentSolution);
          bestEnergy = currentEnergy;
        }
      }
      temperature *= coolingRate;
      iteration++;
    }
    const metrics = {
      objectiveValue: this.evaluateObjective(bestSolution, request),
      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),
      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,
      optimality: 0.75,
      convergenceRate: iteration / this.maxIterations
    };
    return this.buildResult(request, bestSolution, metrics, iteration, "simulated_annealing");
  }
  /**
   * Interior Point Method
   * Best for convex optimization with inequality constraints
   */
  async interiorPointMethod(request) {
    const numChannels = request.channels.length;
    let x = this.getInitialSolution(request);
    let mu = 1;
    let iteration = 0;
    let converged = false;
    while (iteration < this.maxIterations && !converged && mu > 1e-8) {
      const result = this.solveBarrierSubproblem(x, mu, request);
      x = result.solution;
      converged = result.converged;
      mu *= 0.1;
      iteration++;
    }
    const metrics = {
      objectiveValue: this.evaluateObjective(x, request),
      constraintViolations: this.evaluateConstraintViolations(x, request),
      feasibility: this.evaluateConstraintViolations(x, request) < 1e-6,
      optimality: converged ? 0.95 : 0.7,
      convergenceRate: iteration / this.maxIterations
    };
    return this.buildResult(request, x, metrics, iteration, "interior_point");
  }
  // Helper methods...
  selectAlgorithm(request) {
    const numChannels = request.channels.length;
    const numConstraints = request.constraints.length;
    const hasNonlinearConstraints = request.constraints.some(
      (c) => c.type === "roas_target" || c.type === "cpa_target"
    );
    if (numChannels <= 5 && !hasNonlinearConstraints) {
      return "sqp";
    } else if (numChannels > 20 || hasNonlinearConstraints) {
      return "genetic";
    } else if (request.objective.primary === "maximize_roas") {
      return "pso";
    } else {
      return "interior_point";
    }
  }
  getInitialSolution(request) {
    const numChannels = request.channels.length;
    return Array(numChannels).fill(1 / numChannels);
  }
  evaluateObjective(allocation, request) {
    let objective = 0;
    const totalBudget = request.totalBudget;
    allocation.forEach((weight, i) => {
      const channel = request.channels[i];
      const budget = weight * totalBudget;
      const conversions = this.calculateConversions(budget, channel);
      const revenue = conversions * channel.averageOrderValue;
      switch (request.objective.primary) {
        case "maximize_revenue":
          objective += revenue;
          break;
        case "maximize_conversions":
          objective += conversions;
          break;
        case "minimize_cpa":
          objective -= budget / Math.max(conversions, 1);
          break;
        case "maximize_roas":
          objective += revenue / Math.max(budget, 1);
          break;
      }
    });
    return objective;
  }
  evaluateFitness(allocation, request) {
    const objective = this.evaluateObjective(allocation, request);
    const violations = this.evaluateConstraintViolations(allocation, request);
    return objective - 1e3 * violations;
  }
  evaluateConstraintViolations(allocation, request) {
    let violations = 0;
    const totalBudget = request.totalBudget;
    const allocationSum = sum(allocation);
    violations += Math.abs(allocationSum - 1);
    request.constraints.forEach((constraint) => {
      switch (constraint.type) {
        case "total_budget":
          const totalSpend = allocationSum * totalBudget;
          if (constraint.operator === "<=" && totalSpend > constraint.value) {
            violations += (totalSpend - constraint.value) / constraint.value;
          }
          break;
      }
    });
    return violations;
  }
  calculateConversions(budget, channel) {
    const baseConversions = budget * channel.conversionRate / channel.baseRate;
    const saturationFactor = channel.saturationPoint ? Math.min(1, channel.saturationPoint / budget) : 1;
    return baseConversions * saturationFactor * channel.scalingFactor;
  }
  buildResult(request, allocation, metrics, iterations, algorithm) {
    const allocations = allocation.map((weight, i) => {
      const channel = request.channels[i];
      const budget = weight * request.totalBudget;
      const conversions = this.calculateConversions(budget, channel);
      const revenue = conversions * channel.averageOrderValue;
      return {
        channelId: channel.id,
        allocatedBudget: budget,
        expectedConversions: conversions,
        expectedRevenue: revenue,
        expectedCPA: budget / Math.max(conversions, 1),
        expectedROAS: revenue / Math.max(budget, 1),
        confidence: 0.8,
        // Classical methods have good confidence
        riskScore: 3
        // Moderate risk
      };
    });
    return {
      id: `result_${Date.now()}`,
      requestId: request.id,
      status: metrics.feasibility ? "success" : "partial",
      method: "classical",
      allocations,
      totalAllocated: sum(allocations.map((a) => a.allocatedBudget)),
      expectedTotalRevenue: sum(allocations.map((a) => a.expectedRevenue)),
      expectedTotalConversions: sum(allocations.map((a) => a.expectedConversions)),
      overallROAS: sum(allocations.map((a) => a.expectedRevenue)) / Math.max(sum(allocations.map((a) => a.allocatedBudget)), 1),
      overallCPA: sum(allocations.map((a) => a.allocatedBudget)) / Math.max(sum(allocations.map((a) => a.expectedConversions)), 1),
      confidence: metrics.optimality,
      riskScore: metrics.feasibility ? 2 : 6,
      optimizationMetrics: {
        iterations,
        convergenceTime: 0,
        // Will be set by caller
        objectiveValue: metrics.objectiveValue,
        constraintViolations: []
      },
      recommendations: [],
      createdAt: /* @__PURE__ */ new Date(),
      computeTime: 0
      // Will be set by caller
    };
  }
  // Additional helper methods for specific algorithms...
  calculateGradient(x, request) {
    const h = 1e-8;
    const gradient = new Array(x.length);
    for (let i = 0; i < x.length; i++) {
      const xPlus = [...x];
      const xMinus = [...x];
      xPlus[i] += h;
      xMinus[i] -= h;
      gradient[i] = (this.evaluateObjective(xPlus, request) - this.evaluateObjective(xMinus, request)) / (2 * h);
    }
    return gradient;
  }
  calculateHessian(x, request) {
    const h = 1e-6;
    const n = x.length;
    const hessian = Array(n).fill(null).map(() => Array(n).fill(0));
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        const xPP = [...x];
        xPP[i] += h;
        xPP[j] += h;
        const xPM = [...x];
        xPM[i] += h;
        xPM[j] -= h;
        const xMP = [...x];
        xMP[i] -= h;
        xMP[j] += h;
        const xMM = [...x];
        xMM[i] -= h;
        xMM[j] -= h;
        hessian[i][j] = (this.evaluateObjective(xPP, request) - this.evaluateObjective(xPM, request) - this.evaluateObjective(xMP, request) + this.evaluateObjective(xMM, request)) / (4 * h * h);
      }
    }
    return hessian;
  }
  solveQPSubproblem(gradient, hessian, x, request) {
    const n = x.length;
    const direction = new Array(n).fill(0);
    for (let i = 0; i < n; i++) {
      direction[i] = -gradient[i];
    }
    return direction;
  }
  lineSearch(x, direction, request) {
    let alpha = 1;
    const c1 = 1e-4;
    const rho = 0.5;
    const f0 = this.evaluateObjective(x, request);
    const grad0 = this.calculateGradient(x, request);
    const directionalDerivative = sum(grad0.map((g, i) => g * direction[i]));
    while (alpha > 1e-8) {
      const newX = x.map((xi, i) => xi + alpha * direction[i]);
      const f1 = this.evaluateObjective(newX, request);
      if (f1 >= f0 + c1 * alpha * directionalDerivative) {
        return alpha;
      }
      alpha *= rho;
    }
    return alpha;
  }
  calculateNorm(vector) {
    return Math.sqrt(sum(vector.map((v) => v * v)));
  }
  initializePopulation(request, size) {
    const numChannels = request.channels.length;
    const population = [];
    for (let i = 0; i < size; i++) {
      const individual = Array(numChannels).fill(0).map(() => Math.random());
      const total = sum(individual);
      population.push(individual.map((x) => x / total));
    }
    return population;
  }
  tournamentSelection(population, fitness, size) {
    const selected = [];
    const tournamentSize = 3;
    for (let i = 0; i < size; i++) {
      let best = Math.floor(Math.random() * population.length);
      for (let j = 1; j < tournamentSize; j++) {
        const candidate = Math.floor(Math.random() * population.length);
        if (fitness[candidate] > fitness[best]) {
          best = candidate;
        }
      }
      selected.push(cloneDeep(population[best]));
    }
    return selected;
  }
  crossoverAndMutation(parents, request) {
    const offspring = [];
    const mutationRate = 0.1;
    const crossoverRate = 0.8;
    for (let i = 0; i < parents.length; i += 2) {
      let child1 = cloneDeep(parents[i]);
      let child2 = cloneDeep(parents[i + 1] || parents[i]);
      if (Math.random() < crossoverRate) {
        const crossoverPoint = Math.floor(Math.random() * child1.length);
        for (let j = crossoverPoint; j < child1.length; j++) {
          [child1[j], child2[j]] = [child2[j], child1[j]];
        }
      }
      if (Math.random() < mutationRate) {
        const mutationPoint = Math.floor(Math.random() * child1.length);
        child1[mutationPoint] += (Math.random() - 0.5) * 0.1;
        child1[mutationPoint] = Math.max(0, child1[mutationPoint]);
      }
      const total1 = sum(child1);
      const total2 = sum(child2);
      child1 = child1.map((x) => x / total1);
      child2 = child2.map((x) => x / total2);
      offspring.push(child1, child2);
    }
    return offspring.slice(0, parents.length);
  }
  generateNeighbor(solution, request) {
    const neighbor = cloneDeep(solution);
    const perturbationSize = 0.05;
    const numPerturbations = Math.random() < 0.5 ? 1 : 2;
    for (let i = 0; i < numPerturbations; i++) {
      const index = Math.floor(Math.random() * neighbor.length);
      neighbor[index] += (Math.random() - 0.5) * perturbationSize;
      neighbor[index] = Math.max(0, neighbor[index]);
    }
    const total = sum(neighbor);
    return neighbor.map((x) => x / total);
  }
  solveBarrierSubproblem(x, mu, request) {
    let currentX = cloneDeep(x);
    let converged = false;
    const maxInnerIterations = 50;
    for (let iter = 0; iter < maxInnerIterations; iter++) {
      const gradient = this.calculateBarrierGradient(currentX, mu, request);
      const stepSize = this.lineSearch(currentX, gradient.map((g) => -g), request);
      const newX = currentX.map((xi, i) => xi - stepSize * gradient[i]);
      const total = sum(newX);
      const normalizedX = newX.map((xi) => Math.max(1e-8, xi / total));
      const change = this.calculateNorm(normalizedX.map((xi, i) => xi - currentX[i]));
      if (change < this.convergenceThreshold) {
        converged = true;
        break;
      }
      currentX = normalizedX;
    }
    return { solution: currentX, converged };
  }
  calculateBarrierGradient(x, mu, request) {
    const objectiveGradient = this.calculateGradient(x, request);
    const barrierGradient = new Array(x.length);
    for (let i = 0; i < x.length; i++) {
      barrierGradient[i] = objectiveGradient[i] - mu / Math.max(x[i], 1e-8);
    }
    return barrierGradient;
  }
};

// src/quantum/optimizer.ts
var QuantumOptimizer = class {
  willowBridge;
  classicalSolver;
  config;
  performanceHistory = /* @__PURE__ */ new Map();
  constructor(config) {
    this.config = config;
    this.willowBridge = new WillowBridge(config);
    this.classicalSolver = new ClassicalSolver({
      maxIterations: 1e3,
      convergenceThreshold: 1e-6,
      populationSize: 100
    });
  }
  /**
   * Main optimization entry point
   */
  async optimize(request) {
    const startTime = Date.now();
    try {
      const strategy = await this.selectOptimizationStrategy(request);
      console.log(`[Quantum] Using ${strategy} optimization strategy`);
      let result;
      switch (strategy) {
        case "quantum":
          result = await this.quantumOptimization(request);
          break;
        case "classical":
          result = await this.classicalOptimization(request);
          break;
        case "hybrid":
          result = await this.hybridOptimization(request);
          break;
        default:
          throw new QuantumOptimizationError(
            `Unknown optimization strategy: ${strategy}`,
            "INVALID_STRATEGY"
          );
      }
      this.updatePerformanceHistory(strategy, result.computeTime);
      result.recommendations.push(...this.generateQuantumRecommendations(result));
      return result;
    } catch (error) {
      console.error("[Quantum] Optimization failed, falling back to classical:", error);
      const fallbackResult = await this.classicalOptimization(request);
      fallbackResult.status = "partial";
      fallbackResult.recommendations.unshift({
        type: "budget_increase",
        description: "Quantum optimization failed. Consider upgrading to ensure quantum advantage.",
        impact: {
          revenueChange: 0,
          costChange: 0,
          riskChange: 1
        },
        priority: "medium"
      });
      return fallbackResult;
    }
  }
  /**
   * Pure quantum optimization using Willow
   */
  async quantumOptimization(request) {
    console.log("[Quantum] Starting quantum optimization on Willow");
    const status = await this.willowBridge.getProcessorStatus();
    if (!status.available) {
      throw new QuantumOptimizationError(
        "Willow processor not available",
        "PROCESSOR_UNAVAILABLE",
        { status }
      );
    }
    const circuitId = await this.willowBridge.createOptimizationCircuit(
      request,
      this.config.circuitConfig
    );
    const parameters = this.prepareQuantumParameters(request);
    const quantumResult = await this.willowBridge.executeOptimization(circuitId, parameters);
    const allocations = this.convertQuantumToBudgetAllocations(
      quantumResult.result,
      request
    );
    const result = {
      id: `quantum_${Date.now()}`,
      requestId: request.id,
      status: "success",
      method: "quantum",
      allocations,
      totalAllocated: allocations.reduce((sum2, a) => sum2 + a.allocatedBudget, 0),
      expectedTotalRevenue: allocations.reduce((sum2, a) => sum2 + a.expectedRevenue, 0),
      expectedTotalConversions: allocations.reduce((sum2, a) => sum2 + a.expectedConversions, 0),
      overallROAS: 0,
      // Will be calculated
      overallCPA: 0,
      // Will be calculated
      confidence: quantumResult.confidence,
      riskScore: this.calculateQuantumRiskScore(quantumResult),
      optimizationMetrics: {
        iterations: 1,
        // Quantum is single-shot
        convergenceTime: quantumResult.metrics.executionTime,
        objectiveValue: this.calculateObjectiveValue(allocations, request),
        constraintViolations: []
      },
      quantumMetrics: {
        quantumAdvantage: quantumResult.quantumAdvantage,
        coherenceTime: quantumResult.metrics.coherenceTime,
        gateCount: quantumResult.metrics.gateCount,
        errorRate: quantumResult.metrics.errorRate,
        willowChipUtilization: status.qubits / 105
        // Willow has 105 qubits
      },
      recommendations: [],
      createdAt: /* @__PURE__ */ new Date(),
      computeTime: quantumResult.metrics.executionTime
    };
    result.overallROAS = result.expectedTotalRevenue / Math.max(result.totalAllocated, 1);
    result.overallCPA = result.totalAllocated / Math.max(result.expectedTotalConversions, 1);
    return result;
  }
  /**
   * Classical optimization fallback
   */
  async classicalOptimization(request) {
    console.log("[Quantum] Using classical optimization fallback");
    return await this.classicalSolver.optimize(request);
  }
  /**
   * Hybrid quantum-classical optimization
   */
  async hybridOptimization(request) {
    console.log("[Quantum] Starting hybrid optimization");
    try {
      const quantumResult = await this.quantumOptimization(request);
      const refinedRequest = this.createRefinementRequest(request, quantumResult);
      const classicalResult = await this.classicalSolver.optimize(refinedRequest);
      const hybridResult = this.combineResults(quantumResult, classicalResult);
      hybridResult.method = "hybrid";
      return hybridResult;
    } catch (error) {
      console.warn("[Quantum] Hybrid optimization failed, using classical only:", error);
      return await this.classicalOptimization(request);
    }
  }
  /**
   * Select the best optimization strategy based on problem characteristics
   */
  async selectOptimizationStrategy(request) {
    const numChannels = request.channels.length;
    const numConstraints = request.constraints.length;
    const totalBudget = request.totalBudget;
    if (!request.preferences.useQuantumOptimization) {
      return "classical";
    }
    try {
      const status = await this.willowBridge.getProcessorStatus();
      if (!status.available || status.queueLength > 10) {
        return "classical";
      }
      const quantumAdvantageScore = this.calculateQuantumAdvantageScore(request);
      if (quantumAdvantageScore > 0.8) {
        return "quantum";
      } else if (quantumAdvantageScore > 0.5) {
        return "hybrid";
      } else {
        return "classical";
      }
    } catch (error) {
      console.warn("[Quantum] Cannot access Willow, using classical:", error);
      return "classical";
    }
  }
  /**
   * Calculate quantum advantage score for the problem
   */
  calculateQuantumAdvantageScore(request) {
    let score = 0;
    const numChannels = request.channels.length;
    if (numChannels >= 10) score += 0.3;
    if (numChannels >= 20) score += 0.2;
    const complexConstraints = request.constraints.filter(
      (c) => c.type === "roas_target" || c.type === "cpa_target"
    ).length;
    score += Math.min(complexConstraints * 0.15, 0.3);
    if (request.objective.primary === "maximize_roas") score += 0.2;
    if (request.objective.secondary) score += 0.1;
    const quantumHistory = this.performanceHistory.get("quantum") || [];
    const classicalHistory = this.performanceHistory.get("classical") || [];
    if (quantumHistory.length > 0 && classicalHistory.length > 0) {
      const avgQuantumTime = quantumHistory.reduce((a, b) => a + b) / quantumHistory.length;
      const avgClassicalTime = classicalHistory.reduce((a, b) => a + b) / classicalHistory.length;
      if (avgQuantumTime < avgClassicalTime) score += 0.2;
    }
    return Math.min(score, 1);
  }
  /**
   * Prepare parameters for quantum circuit
   */
  prepareQuantumParameters(request) {
    const parameters = {};
    parameters.totalBudget = request.totalBudget / 1e6;
    request.channels.forEach((channel, i) => {
      parameters[`channel_${i}_min`] = channel.minBudget / request.totalBudget;
      parameters[`channel_${i}_max`] = channel.maxBudget / request.totalBudget;
      parameters[`channel_${i}_conversion_rate`] = channel.conversionRate;
      parameters[`channel_${i}_aov`] = channel.averageOrderValue / 1e3;
    });
    parameters.primary_weight = request.objective.weights.primary;
    parameters.secondary_weight = request.objective.weights.secondary || 0;
    return parameters;
  }
  /**
   * Convert quantum measurement results to budget allocations
   */
  convertQuantumToBudgetAllocations(quantumResult, request) {
    const allocations = [];
    const totalBudget = request.totalBudget;
    const sum2 = quantumResult.reduce((a, b) => a + b, 0);
    const normalizedWeights = quantumResult.map((w) => w / sum2);
    request.channels.forEach((channel, i) => {
      const weight = normalizedWeights[i] || 0;
      const budget = weight * totalBudget;
      const constrainedBudget = Math.max(
        channel.minBudget,
        Math.min(channel.maxBudget, budget)
      );
      const conversions = this.calculateExpectedConversions(constrainedBudget, channel);
      const revenue = conversions * channel.averageOrderValue;
      allocations.push({
        channelId: channel.id,
        allocatedBudget: constrainedBudget,
        expectedConversions: conversions,
        expectedRevenue: revenue,
        expectedCPA: constrainedBudget / Math.max(conversions, 1),
        expectedROAS: revenue / Math.max(constrainedBudget, 1),
        confidence: 0.9,
        // Quantum typically has high confidence
        riskScore: 2
        // Quantum optimization is generally low risk
      });
    });
    return allocations;
  }
  /**
   * Calculate expected conversions for a channel given budget
   */
  calculateExpectedConversions(budget, channel) {
    const baseConversions = budget * channel.conversionRate / channel.baseRate;
    let saturationFactor = 1;
    if (channel.saturationPoint && budget > channel.saturationPoint) {
      saturationFactor = Math.sqrt(channel.saturationPoint / budget);
    }
    return baseConversions * saturationFactor * channel.scalingFactor;
  }
  /**
   * Calculate quantum-specific risk score
   */
  calculateQuantumRiskScore(quantumResult) {
    let riskScore = 2;
    if (quantumResult.metrics.errorRate > 0.01) {
      riskScore += 2;
    }
    if (quantumResult.metrics.coherenceTime < 50) {
      riskScore += 1;
    }
    if (quantumResult.quantumAdvantage > 0.8) {
      riskScore -= 1;
    }
    return Math.max(1, Math.min(10, riskScore));
  }
  /**
   * Calculate objective value for allocations
   */
  calculateObjectiveValue(allocations, request) {
    let value = 0;
    switch (request.objective.primary) {
      case "maximize_revenue":
        value = allocations.reduce((sum2, a) => sum2 + a.expectedRevenue, 0);
        break;
      case "maximize_conversions":
        value = allocations.reduce((sum2, a) => sum2 + a.expectedConversions, 0);
        break;
      case "minimize_cpa":
        const totalCPA = allocations.reduce((sum2, a) => sum2 + a.expectedCPA, 0) / allocations.length;
        value = -totalCPA;
        break;
      case "maximize_roas":
        const totalROAS = allocations.reduce((sum2, a) => sum2 + a.expectedROAS, 0) / allocations.length;
        value = totalROAS;
        break;
    }
    return value;
  }
  /**
   * Generate quantum-specific recommendations
   */
  generateQuantumRecommendations(result) {
    const recommendations = [];
    if (result.quantumMetrics?.quantumAdvantage && result.quantumMetrics.quantumAdvantage > 0.8) {
      recommendations.push({
        type: "budget_increase",
        description: "High quantum advantage detected. Consider increasing budget to maximize quantum benefits.",
        impact: {
          revenueChange: result.expectedTotalRevenue * 0.15,
          costChange: result.totalAllocated * 0.1,
          riskChange: -0.5
        },
        priority: "high"
      });
    }
    if (result.quantumMetrics?.errorRate && result.quantumMetrics.errorRate > 0.01) {
      recommendations.push({
        type: "timing_adjustment",
        description: "Higher than optimal quantum error rate. Consider running optimization during off-peak hours.",
        impact: {
          revenueChange: result.expectedTotalRevenue * 0.05,
          costChange: 0,
          riskChange: -1
        },
        priority: "medium"
      });
    }
    return recommendations;
  }
  /**
   * Create refinement request for hybrid optimization
   */
  createRefinementRequest(original, quantumResult) {
    const refinedRequest = { ...original };
    quantumResult.allocations.forEach((allocation, i) => {
      const tolerance = 0.2;
      const minBudget = allocation.allocatedBudget * (1 - tolerance);
      const maxBudget = allocation.allocatedBudget * (1 + tolerance);
      refinedRequest.constraints.push({
        id: `quantum_constraint_${i}`,
        type: "channel_budget",
        value: maxBudget,
        operator: "<=",
        priority: 7,
        flexible: true,
        tolerance: 0.1
      });
    });
    return refinedRequest;
  }
  /**
   * Combine quantum and classical results
   */
  combineResults(quantumResult, classicalResult) {
    const combined = { ...quantumResult };
    if (classicalResult.optimizationMetrics.objectiveValue > quantumResult.optimizationMetrics.objectiveValue) {
      combined.allocations = classicalResult.allocations;
      combined.totalAllocated = classicalResult.totalAllocated;
      combined.expectedTotalRevenue = classicalResult.expectedTotalRevenue;
      combined.expectedTotalConversions = classicalResult.expectedTotalConversions;
      combined.overallROAS = classicalResult.overallROAS;
      combined.overallCPA = classicalResult.overallCPA;
    }
    combined.confidence = (quantumResult.confidence + classicalResult.confidence) / 2;
    combined.riskScore = Math.min(quantumResult.riskScore, classicalResult.riskScore);
    return combined;
  }
  /**
   * Update performance history for algorithm selection
   */
  updatePerformanceHistory(strategy, computeTime) {
    if (!this.performanceHistory.has(strategy)) {
      this.performanceHistory.set(strategy, []);
    }
    const history = this.performanceHistory.get(strategy);
    history.push(computeTime);
    if (history.length > 10) {
      history.shift();
    }
  }
  /**
   * Clean up resources
   */
  async cleanup() {
    await this.willowBridge.cleanup();
    this.performanceHistory.clear();
  }
};

// src/quantum/index.ts
var QUANTUM_ALGORITHMS = {
  VQE: "Variational Quantum Eigensolver",
  QAOA: "Quantum Approximate Optimization Algorithm",
  QUANTUM_ANNEALING: "Quantum Annealing",
  GROVER: "Grover Search Algorithm",
  SHOR: "Shor Factoring Algorithm"
};
var WILLOW_SPECS = {
  MAX_QUBITS: 105,
  COHERENCE_TIME: 100,
  // microseconds
  ERROR_RATE: 1e-3,
  GATE_TIME: 0.1,
  // microseconds
  READOUT_FIDELITY: 0.999,
  TWO_QUBIT_GATE_FIDELITY: 0.995
};
var defaultExport = {
  QuantumOptimizer,
  WillowBridge,
  QUANTUM_ALGORITHMS,
  WILLOW_SPECS
};
var quantum_default = defaultExport;
export {
  QUANTUM_ALGORITHMS,
  QuantumOptimizationError,
  QuantumOptimizer,
  WILLOW_SPECS,
  WillowAPIError,
  WillowBridge,
  quantum_default as default
};
//# sourceMappingURL=index.mjs.map