'use client';

import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Zap, BarChart3, Target, Users, Settings, LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function DashboardPage(): React.JSX.Element {
  const { user, profile, loading, signOut, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [loading, isAuthenticated, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-flux-500"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 rounded-full bg-flux-500/20 flex items-center justify-center">
                <Zap className="h-4 w-4 text-flux-500" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-foreground">
                  Metamorphic Flux
                </h1>
                <p className="text-sm text-muted-foreground">
                  AI Marketing Dashboard
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-foreground">
                  {profile?.full_name || user?.email}
                </p>
                <p className="text-xs text-muted-foreground">
                  {profile?.subscription_tier || 'Free'} Plan
                </p>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={signOut}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-foreground mb-2">
            Welcome back, {profile?.full_name?.split(' ')[0] || 'there'}!
          </h2>
          <p className="text-muted-foreground">
            Ready to transform your marketing with AI? Let's get started.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">API Credits</p>
                <p className="text-2xl font-bold text-foreground">
                  {profile?.api_credits?.toLocaleString() || '1,000'}
                </p>
              </div>
              <div className="w-10 h-10 rounded-full bg-flux-500/20 flex items-center justify-center">
                <Zap className="h-5 w-5 text-flux-500" />
              </div>
            </div>
          </div>

          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Campaigns</p>
                <p className="text-2xl font-bold text-foreground">0</p>
              </div>
              <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                <Target className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Impressions</p>
                <p className="text-2xl font-bold text-foreground">0</p>
              </div>
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <BarChart3 className="h-5 w-5 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-card border border-border rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Personas</p>
                <p className="text-2xl font-bold text-foreground">0</p>
              </div>
              <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                <Users className="h-5 w-5 text-purple-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Getting Started */}
        <div className="bg-card border border-border rounded-lg p-8">
          <h3 className="text-xl font-bold text-foreground mb-4">
            🚀 Getting Started with Metamorphic Flux
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 rounded-full bg-flux-500 text-white text-xs flex items-center justify-center font-bold">
                  1
                </div>
                <div>
                  <h4 className="font-medium text-foreground">Create Your First Persona</h4>
                  <p className="text-sm text-muted-foreground">
                    Define your target audience with AI-powered persona generation
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 rounded-full bg-flux-500 text-white text-xs flex items-center justify-center font-bold">
                  2
                </div>
                <div>
                  <h4 className="font-medium text-foreground">Launch a Campaign</h4>
                  <p className="text-sm text-muted-foreground">
                    Set up your first AI-optimized marketing campaign
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 rounded-full bg-flux-500 text-white text-xs flex items-center justify-center font-bold">
                  3
                </div>
                <div>
                  <h4 className="font-medium text-foreground">Watch the Magic</h4>
                  <p className="text-sm text-muted-foreground">
                    Let our AI agents optimize your creative and budget in real-time
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <Button className="w-full flux-gradient">
                Create First Persona
              </Button>
              
              <Button variant="outline" className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Configure Integrations
              </Button>
              
              <Button variant="outline" className="w-full">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Documentation
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
