'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { CampaignCanvas } from '@/components/campaign-canvas';
import { AgentStatus } from '@/components/agent-status';
import { PerformanceMetrics } from '@/components/performance-metrics';
import { FluxCanvas } from '@/components/flux-canvas';
import {
  Zap,
  BarChart3,
  Target,
  Users,
  Settings,
  LogOut,
  Plus,
  Play,
  Layers,
  Activity,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { useRouter } from 'next/navigation';

type DashboardView = 'overview' | 'canvas' | 'analytics' | 'agents';

export default function DashboardPage(): React.JSX.Element {
  const { user, profile, loading, signOut, isAuthenticated } = useAuth();
  const router = useRouter();
  const [currentView, setCurrentView] = useState<DashboardView>('overview');
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [loading, isAuthenticated, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="relative">
          <FluxCanvas intensity="medium" className="w-32 h-32" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-flux-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <div />; // Will redirect in useEffect
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const renderMainContent = () => {
    switch (currentView) {
      case 'canvas':
        return (
          <div className="h-full">
            <CampaignCanvas campaignId="demo-campaign" />
          </div>
        );

      case 'analytics':
        return (
          <div className="space-y-6">
            <PerformanceMetrics campaignId="demo-campaign" />
          </div>
        );

      case 'agents':
        return (
          <div className="space-y-6">
            <AgentStatus showDetails={true} />
          </div>
        );

      default:
        return (
          <div className="space-y-8">
            {/* Welcome Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-flux-500/10 via-flux-600/5 to-flux-700/10 border border-flux-500/20 rounded-xl p-8">
              <FluxCanvas intensity="low" className="opacity-20" />
              <div className="relative z-10">
                <h2 className="text-3xl font-bold text-foreground mb-2">
                  Welcome back, {profile?.full_name?.split(' ')[0] || 'there'}!
                </h2>
                <p className="text-muted-foreground mb-6">
                  Your AI marketing agents are ready to transform campaigns in real-time.
                </p>
                <div className="flex items-center space-x-4">
                  <Button
                    className="flux-gradient"
                    onClick={() => setCurrentView('canvas')}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Launch Campaign Canvas
                  </Button>
                  <Button variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Campaign
                  </Button>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">API Credits</p>
                    <p className="text-2xl font-bold text-foreground">
                      {profile?.api_credits?.toLocaleString() || '1,000'}
                    </p>
                  </div>
                  <div className="w-10 h-10 rounded-full bg-flux-500/20 flex items-center justify-center">
                    <Zap className="h-5 w-5 text-flux-500" />
                  </div>
                </div>
              </div>

              <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Active Campaigns</p>
                    <p className="text-2xl font-bold text-foreground">3</p>
                  </div>
                  <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                    <Target className="h-5 w-5 text-green-500" />
                  </div>
                </div>
              </div>

              <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Impressions</p>
                    <p className="text-2xl font-bold text-foreground">2.4M</p>
                  </div>
                  <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                    <BarChart3 className="h-5 w-5 text-blue-500" />
                  </div>
                </div>
              </div>

              <div className="bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 transition-all duration-300 hover:scale-105">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Personas</p>
                    <p className="text-2xl font-bold text-foreground">8</p>
                  </div>
                  <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center">
                    <Users className="h-5 w-5 text-purple-500" />
                  </div>
                </div>
              </div>
            </div>

            {/* Agent Status Overview */}
            <div className="bg-card/30 backdrop-blur-sm border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Agent Swarm Status</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentView('agents')}
                >
                  View Details
                </Button>
              </div>
              <AgentStatus />
            </div>

            {/* Recent Performance */}
            <div className="bg-card/30 backdrop-blur-sm border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Performance Overview</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentView('analytics')}
                >
                  View Analytics
                </Button>
              </div>
              <PerformanceMetrics campaignId="demo-campaign" />
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen bg-background ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Header */}
      {!isFullscreen && (
        <header className="border-b border-border bg-card/80 backdrop-blur-sm sticky top-0 z-40">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-8 h-8 rounded-full bg-flux-500/20 flex items-center justify-center">
                  <Zap className="h-4 w-4 text-flux-500" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-foreground">
                    Metamorphic Flux
                  </h1>
                  <p className="text-sm text-muted-foreground">
                    AI Marketing Dashboard
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Navigation */}
                <nav className="flex items-center space-x-1">
                  <Button
                    variant={currentView === 'overview' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrentView('overview')}
                  >
                    <Activity className="h-4 w-4 mr-2" />
                    Overview
                  </Button>
                  <Button
                    variant={currentView === 'canvas' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrentView('canvas')}
                  >
                    <Layers className="h-4 w-4 mr-2" />
                    Canvas
                  </Button>
                  <Button
                    variant={currentView === 'analytics' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrentView('analytics')}
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Analytics
                  </Button>
                  <Button
                    variant={currentView === 'agents' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setCurrentView('agents')}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Agents
                  </Button>
                </nav>

                <div className="text-right">
                  <p className="text-sm font-medium text-foreground">
                    {profile?.full_name || user?.email}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {profile?.subscription_tier || 'Free'} Plan
                  </p>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={signOut}
                  className="flex items-center space-x-2"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Sign Out</span>
                </Button>
              </div>
            </div>
          </div>
        </header>
      )}

      {/* Main Content */}
      <main className={`${isFullscreen ? 'h-screen' : 'container mx-auto px-4 py-8'}`}>
        {currentView === 'canvas' && (
          <div className="absolute top-4 right-4 z-50">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        )}

        <div className={isFullscreen ? 'h-full' : ''}>
          {renderMainContent()}
        </div>
      </main>
    </div>
  );
}
