{"name": "@metamorphic-flux/optimizer", "version": "1.0.0", "description": "Quantum-enhanced budget optimization with Google Willow integration and classical fallback", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist"}, "keywords": ["quantum", "optimization", "budget", "marketing", "willow", "quantum-computing"], "author": "Metamorphic Flux", "license": "MIT", "dependencies": {"axios": "^1.6.0", "zod": "^3.22.0", "mathjs": "^12.0.0", "ml-matrix": "^6.10.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/lodash": "^4.14.200", "@types/node": "^20.0.0", "tsup": "^8.0.0", "typescript": "^5.3.0", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "eslint": "^8.55.0"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./quantum": {"import": "./dist/quantum/index.js", "require": "./dist/quantum/index.cjs", "types": "./dist/quantum/index.d.ts"}, "./classical": {"import": "./dist/classical/index.js", "require": "./dist/classical/index.cjs", "types": "./dist/classical/index.d.ts"}}, "files": ["dist", "README.md"], "publishConfig": {"access": "restricted"}}