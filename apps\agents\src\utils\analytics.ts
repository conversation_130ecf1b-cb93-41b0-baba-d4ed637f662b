import { createClient } from '@supabase/supabase-js';
import { logger } from './logger';

export interface PerformanceQuery {
  campaignId: string;
  timeframe: string;
  metrics: string[];
  creativeIds?: string[];
}

export interface HistoricalQuery {
  campaignId: string;
  timeframe: string;
  metrics: string[];
}

export interface BenchmarkQuery {
  industry: string;
  channels: string[];
  metrics: string[];
}

export interface PerformanceDataPoint {
  date: string;
  metrics: Record<string, number>;
  creative_id?: string;
  channel?: string;
}

export class AnalyticsService {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  async getCampaignPerformance(query: PerformanceQuery): Promise<PerformanceDataPoint[]> {
    try {
      logger.info('Fetching campaign performance data', {
        campaignId: query.campaignId,
        timeframe: query.timeframe,
        metrics: query.metrics,
      });

      const { startDate, endDate } = this.parseTimeframe(query.timeframe);

      let supabaseQuery = this.supabase
        .from('campaign_logs')
        .select('*')
        .eq('campaign_id', query.campaignId)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: true });

      if (query.creativeIds && query.creativeIds.length > 0) {
        supabaseQuery = supabaseQuery.in('creative_id', query.creativeIds);
      }

      const { data, error } = await supabaseQuery;

      if (error) {
        throw new Error(`Failed to fetch performance data: ${error.message}`);
      }

      // Aggregate data by date and calculate metrics
      const aggregatedData = this.aggregatePerformanceData(data || [], query.metrics);

      logger.info('Campaign performance data fetched successfully', {
        dataPoints: aggregatedData.length,
        campaignId: query.campaignId,
      });

      return aggregatedData;

    } catch (error) {
      logger.error('Failed to fetch campaign performance data', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId: query.campaignId,
      });
      throw error;
    }
  }

  async getHistoricalData(query: HistoricalQuery): Promise<PerformanceDataPoint[]> {
    try {
      const { startDate, endDate } = this.parseTimeframe(query.timeframe);

      const { data, error } = await this.supabase
        .from('campaign_logs')
        .select('*')
        .eq('campaign_id', query.campaignId)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: true });

      if (error) {
        throw new Error(`Failed to fetch historical data: ${error.message}`);
      }

      return this.aggregatePerformanceData(data || [], query.metrics);

    } catch (error) {
      logger.error('Failed to fetch historical data', {
        error: error instanceof Error ? error.message : 'Unknown error',
        campaignId: query.campaignId,
      });
      throw error;
    }
  }

  async getBenchmarkData(query: BenchmarkQuery): Promise<Record<string, number>> {
    try {
      // In a real implementation, this would fetch industry benchmark data
      // For now, we'll return mock benchmark data
      const benchmarks: Record<string, number> = {
        ctr: 0.05,           // 5% click-through rate
        cpc: 1.50,           // $1.50 cost per click
        cpm: 10.00,          // $10 cost per mille
        conversion_rate: 0.02, // 2% conversion rate
        cpa: 75.00,          // $75 cost per acquisition
        roas: 4.0,           // 4:1 return on ad spend
        engagement_rate: 0.08, // 8% engagement rate
        reach: 10000,        // Average reach
        impressions: 50000,  // Average impressions
        frequency: 2.5,      // Average frequency
      };

      // Apply industry and channel modifiers
      const modifiers = this.getBenchmarkModifiers(query.industry, query.channels);
      
      const adjustedBenchmarks: Record<string, number> = {};
      query.metrics.forEach(metric => {
        if (benchmarks[metric]) {
          adjustedBenchmarks[metric] = benchmarks[metric] * (modifiers[metric] || 1.0);
        }
      });

      logger.info('Benchmark data retrieved', {
        industry: query.industry,
        channels: query.channels,
        metrics: query.metrics,
      });

      return adjustedBenchmarks;

    } catch (error) {
      logger.error('Failed to fetch benchmark data', {
        error: error instanceof Error ? error.message : 'Unknown error',
        industry: query.industry,
      });
      throw error;
    }
  }

  private parseTimeframe(timeframe: string): { startDate: Date; endDate: Date } {
    const endDate = new Date();
    const startDate = new Date();

    switch (timeframe) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 7); // Default to 7 days
    }

    return { startDate, endDate };
  }

  private aggregatePerformanceData(
    rawData: any[], 
    requestedMetrics: string[]
  ): PerformanceDataPoint[] {
    // Group data by date
    const groupedByDate: Record<string, any[]> = {};
    
    rawData.forEach(item => {
      const date = new Date(item.timestamp).toISOString().split('T')[0];
      if (date && !groupedByDate[date]) {
        groupedByDate[date] = [];
      }
      if (date && groupedByDate[date]) {
        groupedByDate[date].push(item);
      }
    });

    // Calculate metrics for each date
    const aggregatedData: PerformanceDataPoint[] = [];

    Object.entries(groupedByDate).forEach(([date, items]) => {
      const metrics = this.calculateMetrics(items, requestedMetrics);
      aggregatedData.push({
        date,
        metrics,
      });
    });

    return aggregatedData.sort((a, b) => a.date.localeCompare(b.date));
  }

  private calculateMetrics(items: any[], requestedMetrics: string[]): Record<string, number> {
    const metrics: Record<string, number> = {};

    // Count events by type
    const eventCounts: Record<string, number> = {};
    let totalCost = 0;
    let totalRevenue = 0;

    items.forEach(item => {
      const eventType = item.event_type;
      eventCounts[eventType] = (eventCounts[eventType] || 0) + 1;
      
      if (item.metrics?.cost) {
        totalCost += parseFloat(item.metrics.cost);
      }
      if (item.metrics?.revenue) {
        totalRevenue += parseFloat(item.metrics.revenue);
      }
    });

    // Calculate requested metrics
    requestedMetrics.forEach(metric => {
      switch (metric) {
        case 'impressions':
          metrics[metric] = eventCounts['impression'] || 0;
          break;
        case 'clicks':
          metrics[metric] = eventCounts['click'] || 0;
          break;
        case 'conversions':
          metrics[metric] = eventCounts['conversion'] || 0;
          break;
        case 'ctr':
          const impressions = eventCounts['impression'] || 0;
          const clicks = eventCounts['click'] || 0;
          metrics[metric] = impressions > 0 ? clicks / impressions : 0;
          break;
        case 'cpc':
          const totalClicks = eventCounts['click'] || 0;
          metrics[metric] = totalClicks > 0 ? totalCost / totalClicks : 0;
          break;
        case 'cpm':
          const totalImpressions = eventCounts['impression'] || 0;
          metrics[metric] = totalImpressions > 0 ? (totalCost / totalImpressions) * 1000 : 0;
          break;
        case 'conversion_rate':
          const totalClicksForConversion = eventCounts['click'] || 0;
          const conversions = eventCounts['conversion'] || 0;
          metrics[metric] = totalClicksForConversion > 0 ? conversions / totalClicksForConversion : 0;
          break;
        case 'cpa':
          const totalConversions = eventCounts['conversion'] || 0;
          metrics[metric] = totalConversions > 0 ? totalCost / totalConversions : 0;
          break;
        case 'roas':
          metrics[metric] = totalCost > 0 ? totalRevenue / totalCost : 0;
          break;
        case 'cost':
          metrics[metric] = totalCost;
          break;
        case 'revenue':
          metrics[metric] = totalRevenue;
          break;
        default:
          // For other metrics, try to extract from event data
          const values = items
            .map(item => item.metrics?.[metric])
            .filter(val => val !== undefined && val !== null)
            .map(val => parseFloat(val));
          
          if (values.length > 0) {
            metrics[metric] = values.reduce((sum, val) => sum + val, 0) / values.length;
          } else {
            metrics[metric] = 0;
          }
      }
    });

    return metrics;
  }

  private getBenchmarkModifiers(
    industry: string, 
    channels: string[]
  ): Record<string, number> {
    // Industry modifiers
    const industryModifiers: Record<string, Record<string, number>> = {
      'technology': {
        ctr: 1.2,
        cpc: 1.5,
        conversion_rate: 1.1,
      },
      'ecommerce': {
        ctr: 1.0,
        cpc: 1.0,
        conversion_rate: 1.3,
      },
      'finance': {
        ctr: 0.8,
        cpc: 2.0,
        conversion_rate: 0.7,
      },
      'healthcare': {
        ctr: 0.9,
        cpc: 1.8,
        conversion_rate: 0.8,
      },
    };

    // Channel modifiers
    const channelModifiers: Record<string, Record<string, number>> = {
      'facebook': {
        ctr: 1.0,
        cpc: 1.0,
      },
      'google': {
        ctr: 1.2,
        cpc: 1.3,
      },
      'linkedin': {
        ctr: 0.8,
        cpc: 2.5,
      },
      'instagram': {
        ctr: 1.1,
        cpc: 0.9,
      },
    };

    // Combine modifiers
    const modifiers: Record<string, number> = {};
    
    // Apply industry modifiers
    const industryMods = industryModifiers[industry] || {};
    Object.entries(industryMods).forEach(([metric, modifier]) => {
      modifiers[metric] = modifier;
    });

    // Apply channel modifiers (average if multiple channels)
    if (channels.length > 0) {
      const channelMods: Record<string, number[]> = {};
      
      channels.forEach(channel => {
        const mods = channelModifiers[channel] || {};
        Object.entries(mods).forEach(([metric, modifier]) => {
          if (!channelMods[metric]) {
            channelMods[metric] = [];
          }
          channelMods[metric].push(modifier);
        });
      });

      Object.entries(channelMods).forEach(([metric, modifierArray]) => {
        const avgModifier = modifierArray.reduce((sum, mod) => sum + mod, 0) / modifierArray.length;
        modifiers[metric] = (modifiers[metric] || 1.0) * avgModifier;
      });
    }

    return modifiers;
  }
}
