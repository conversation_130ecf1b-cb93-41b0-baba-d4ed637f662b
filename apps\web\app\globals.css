@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Metamorphic Flux custom styles */
@layer components {
  .flux-gradient {
    @apply bg-gradient-to-r from-flux-500 via-flux-600 to-flux-700;
  }
  
  .flux-text-gradient {
    @apply bg-gradient-to-r from-flux-500 to-flux-700 bg-clip-text text-transparent;
  }
  
  .flux-glow {
    @apply shadow-lg shadow-flux-500/25;
  }
  
  .flux-morph-container {
    @apply relative overflow-hidden rounded-lg;
  }
  
  .flux-morph-container::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-flux-500/20 via-transparent to-flux-700/20;
    animation: flux-morph 3s ease-in-out infinite;
  }
}

/* Flux Canvas Animations */
@keyframes flux-morph {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: scale(1.02) rotate(1deg);
    filter: hue-rotate(90deg);
  }
  50% {
    transform: scale(1.05) rotate(0deg);
    filter: hue-rotate(180deg);
  }
  75% {
    transform: scale(1.02) rotate(-1deg);
    filter: hue-rotate(270deg);
  }
}

@keyframes flux-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes flux-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(217, 91, 60, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(217, 91, 60, 0.6);
  }
}

/* Enhanced Flux Gradient Utilities */
.flux-gradient {
  background: linear-gradient(135deg, hsl(var(--flux-500)), hsl(var(--flux-600)));
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.flux-gradient-text {
  background: linear-gradient(135deg, hsl(var(--flux-500)), hsl(var(--flux-600)));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Canvas Node Animations */
.flux-node {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.flux-node:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.flux-node.processing {
  animation: flux-pulse 2s ease-in-out infinite;
}

.flux-node.complete {
  animation: flux-glow 1s ease-in-out;
}

/* Performance Metrics Animations */
.metric-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Agent Status Indicators */
.agent-status-ready {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.agent-status-busy {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  animation: flux-pulse 1.5s ease-in-out infinite;
}

.agent-status-error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

/* Backdrop Blur Utilities */
.backdrop-blur-flux {
  backdrop-filter: blur(12px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.05);
}

.dark .backdrop-blur-flux {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Custom Scrollbar */
.flux-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.flux-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.flux-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--flux-500));
  border-radius: 4px;
}

.flux-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--flux-600));
}

/* Loading States */
.flux-loading {
  position: relative;
  overflow: hidden;
}

.flux-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(217, 91, 60, 0.2),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
