import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { z } from 'zod';
import { logger } from '@/utils/logger';
import type { 
  AgentType, 
  AgentConfig, 
  AgentContext, 
  AgentOutput, 
  AgentExecutionResult 
} from '@/types';

export abstract class BaseAgent {
  protected model: ChatGoogleGenerativeAI;
  protected config: AgentConfig;

  constructor(config: AgentConfig) {
    this.config = config;
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
      throw new Error('GOOGLE_AI_API_KEY environment variable is required');
    }
    this.model = new ChatGoogleGenerativeAI({
      modelName: config.model,
      temperature: config.temperature,
      maxOutputTokens: config.maxTokens,
      apiKey,
    });
  }

  abstract get inputSchema(): z.ZodSchema<any>;
  abstract get outputSchema(): z.ZodSchema<any>;
  
  protected abstract processInput(input: any, context: AgentContext): Promise<AgentOutput>;

  async execute(input: any, context: AgentContext): Promise<AgentExecutionResult> {
    const startTime = Date.now();
    const executionId = context.executionId;

    try {
      logger.info(`Starting ${this.config.type} agent execution`, {
        agentType: this.config.type,
        executionId,
        campaignId: context.campaignId,
      });

      // Validate input
      const validatedInput = this.inputSchema.parse(input);

      // Process the request
      const output = await this.processInput(validatedInput, context);

      // Validate output
      const validatedOutput = this.outputSchema.parse(output);

      const duration = Date.now() - startTime;

      logger.info(`Completed ${this.config.type} agent execution`, {
        agentType: this.config.type,
        executionId,
        duration,
        confidence: validatedOutput.confidence,
      });

      return {
        agentType: this.config.type,
        executionId,
        status: 'completed',
        input: validatedInput,
        output: validatedOutput,
        duration,
        metadata: {
          modelUsed: this.config.model,
          temperature: this.config.temperature,
        },
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error(`Failed ${this.config.type} agent execution`, {
        agentType: this.config.type,
        executionId,
        error: errorMessage,
        duration,
      });

      return {
        agentType: this.config.type,
        executionId,
        status: 'failed',
        input,
        output: {
          success: false,
          data: {},
          confidence: 0,
          reasoning: 'Agent execution failed',
          errors: [errorMessage],
        },
        duration,
        error: errorMessage,
      };
    }
  }

  protected async callModel(
    systemPrompt: string, 
    userPrompt: string,
    context?: AgentContext
  ): Promise<string> {
    try {
      const messages = [
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ];

      const response = await this.model.invoke(messages);
      return response.content as string;

    } catch (error) {
      logger.error(`Model call failed for ${this.config.type} agent`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        executionId: context?.executionId,
      });
      throw error;
    }
  }

  protected parseJsonResponse(response: string): any {
    try {
      // Try to extract JSON from response if it's wrapped in markdown or other text
      const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*\})\s*```/);
      const jsonString = jsonMatch?.[1] ?? response.trim();

      return JSON.parse(jsonString);
    } catch (error) {
      logger.error(`Failed to parse JSON response`, {
        response: response.substring(0, 200),
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error(`Invalid JSON response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  protected calculateConfidence(
    factors: {
      inputQuality?: number;
      outputCoherence?: number;
      alignmentWithObjectives?: number;
      technicalCorrectness?: number;
    }
  ): number {
    const weights = {
      inputQuality: 0.2,
      outputCoherence: 0.3,
      alignmentWithObjectives: 0.3,
      technicalCorrectness: 0.2,
    };

    let totalScore = 0;
    let totalWeight = 0;

    Object.entries(factors).forEach(([key, value]) => {
      if (value !== undefined && key in weights) {
        totalScore += value * weights[key as keyof typeof weights];
        totalWeight += weights[key as keyof typeof weights];
      }
    });

    return totalWeight > 0 ? Math.min(Math.max(totalScore / totalWeight, 0), 1) : 0.5;
  }

  protected formatPersonaContext(personas: any[]): string {
    return personas.map(persona => {
      const demographics = persona.demographics ? 
        Object.entries(persona.demographics).map(([k, v]) => `${k}: ${v}`).join(', ') : '';
      const interests = persona.interests ? persona.interests.join(', ') : '';
      
      return `
Persona: ${persona.name}
Description: ${persona.description || 'N/A'}
Demographics: ${demographics}
Interests: ${interests}
      `.trim();
    }).join('\n\n');
  }

  protected formatCampaignContext(campaign: any): string {
    const objectives = campaign.objectives ? 
      Object.entries(campaign.objectives).map(([k, v]) => `${k}: ${v}`).join(', ') : '';
    
    return `
Campaign: ${campaign.name}
Description: ${campaign.description || 'N/A'}
Objectives: ${objectives}
Budget: $${campaign.budget_total || 'N/A'}
Channels: ${campaign.channels ? campaign.channels.join(', ') : 'N/A'}
    `.trim();
  }
}
