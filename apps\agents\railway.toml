[build]
builder = "NIXPACKS"
buildCommand = "pnpm install && pnpm build"

[deploy]
startCommand = "node dist/index.js"
healthcheckPath = "/health"
healthcheckTimeout = 30
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 3

[env]
NODE_ENV = "production"
PORT = "3001"

[scaling]
minReplicas = 1
maxReplicas = 3

[networking]
serviceDomain = "metamorphic-flux-agents"

[monitoring]
enabled = true

[resources]
memory = "1GB"
cpu = "1vCPU"
