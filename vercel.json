{"version": 2, "name": "metamorphic-flux", "builds": [{"src": "apps/web/package.json", "use": "@vercel/next", "config": {"projectSettings": {"framework": "nextjs"}}}], "routes": [{"src": "/api/(.*)", "dest": "apps/web/api/$1"}, {"src": "/(.*)", "dest": "apps/web/$1"}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "functions": {"apps/web/app/api/*/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/login", "destination": "/auth/signin", "permanent": true}, {"source": "/register", "destination": "/auth/signup", "permanent": true}], "rewrites": [{"source": "/health", "destination": "/api/health"}]}