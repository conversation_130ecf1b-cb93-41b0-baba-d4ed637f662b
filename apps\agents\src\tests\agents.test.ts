import { describe, it, expect, beforeEach } from 'vitest';
import { CopyAgent } from '@/agents/copy';
import { DesignAgent } from '@/agents/design';
import { AnalystAgent } from '@/agents/analyst';
import type { AgentContext, CopyAgentInput, DesignAgentInput, AnalystAgentInput } from '@/types';

describe('Agent Tests', () => {
  let mockContext: AgentContext;

  beforeEach(() => {
    mockContext = {
      campaignId: 'test-campaign-123',
      executionId: 'test-execution-123',
      userId: 'test-user-123',
      organizationId: 'test-org-123',
      campaign: {
        id: 'test-campaign-123',
        name: 'Test Campaign',
        description: 'A test campaign for AI marketing',
        objectives: {
          primary: 'brand_awareness',
          secondary: 'lead_generation',
        },
        channels: ['facebook', 'google'],
      },
      personas: [
        {
          id: 'persona-1',
          name: 'Tech-Savvy Millennials',
          description: 'Young professionals interested in technology',
          demographics: {
            age_range: '25-35',
            income: '50k-100k',
            education: 'college',
          },
          interests: ['technology', 'innovation', 'productivity'],
        },
      ],
      existingCreatives: [],
      objectives: {
        primary: 'brand_awareness',
        keywords: ['AI', 'marketing', 'automation'],
      },
      constraints: {
        brandGuidelines: 'Professional, innovative, trustworthy',
      },
      metadata: {},
    };
  });

  describe('CopyAgent', () => {
    it('should generate copy successfully', async () => {
      // Skip if no API key (for CI/CD)
      if (!process.env.GOOGLE_AI_API_KEY) {
        console.log('Skipping CopyAgent test - no API key');
        return;
      }

      const copyAgent = new CopyAgent();
      const input: CopyAgentInput = {
        persona: mockContext.personas[0]!,
        objective: 'brand_awareness',
        tone: 'innovative',
        format: 'headline',
        constraints: {
          maxLength: 60,
          keywords: ['AI', 'marketing'],
        },
      };

      const result = await copyAgent.execute(input, mockContext);

      expect(result.status).toBe('completed');
      expect(result.output.success).toBe(true);
      expect(result.output.confidence).toBeGreaterThan(0);
      expect(result.output.data.headline).toBeDefined();
      expect(typeof result.output.data.headline).toBe('string');
      expect(result.duration).toBeGreaterThan(0);
    }, 30000); // 30 second timeout for API calls

    it('should validate input schema', () => {
      const copyAgent = new CopyAgent();
      const invalidInput = {
        // Missing required fields
        objective: 'brand_awareness',
      };

      expect(() => {
        copyAgent.inputSchema.parse(invalidInput);
      }).toThrow();
    });
  });

  describe('DesignAgent', () => {
    it('should generate design concept successfully', async () => {
      // Skip if no API key
      if (!process.env.GOOGLE_AI_API_KEY) {
        console.log('Skipping DesignAgent test - no API key');
        return;
      }

      const designAgent = new DesignAgent();
      const input: DesignAgentInput = {
        copy: {
          headline: 'Transform Your Marketing with AI',
          description: 'Discover the power of AI-driven campaigns',
          cta: 'Get Started',
        },
        style: 'modern tech',
        format: 'image',
        dimensions: {
          width: 1200,
          height: 628,
        },
        constraints: {
          colorScheme: 'blue gradient',
        },
      };

      const result = await designAgent.execute(input, mockContext);

      expect(result.status).toBe('completed');
      expect(result.output.success).toBe(true);
      expect(result.output.confidence).toBeGreaterThan(0);
      expect(result.output.data.visual_concept).toBeDefined();
      expect(result.output.data.image_prompt).toBeDefined();
      expect(typeof result.output.data.visual_concept).toBe('string');
    }, 30000);

    it('should validate input schema', () => {
      const designAgent = new DesignAgent();
      const invalidInput = {
        copy: {
          headline: 'Test',
        },
        // Missing required fields
      };

      expect(() => {
        designAgent.inputSchema.parse(invalidInput);
      }).toThrow();
    });
  });

  describe('AnalystAgent', () => {
    it('should analyze performance data successfully', async () => {
      // Skip if no API key
      if (!process.env.GOOGLE_AI_API_KEY) {
        console.log('Skipping AnalystAgent test - no API key');
        return;
      }

      const analystAgent = new AnalystAgent();
      const input: AnalystAgentInput = {
        timeframe: '7d',
        metrics: ['ctr', 'cpc', 'conversion_rate'],
        campaignId: mockContext.campaignId,
      };

      const result = await analystAgent.execute(input, mockContext);

      expect(result.status).toBe('completed');
      expect(result.output.success).toBe(true);
      expect(result.output.confidence).toBeGreaterThan(0);
      expect(result.output.data.performance_summary).toBeDefined();
      expect(result.output.data.optimization_recommendations).toBeDefined();
      expect(Array.isArray(result.output.data.optimization_recommendations)).toBe(true);
    }, 30000);

    it('should validate input schema', () => {
      const analystAgent = new AnalystAgent();
      const invalidInput = {
        timeframe: '7d',
        // Missing required fields
      };

      expect(() => {
        analystAgent.inputSchema.parse(invalidInput);
      }).toThrow();
    });
  });

  describe('Agent Output Validation', () => {
    it('should validate agent output schema', () => {
      const copyAgent = new CopyAgent();
      
      const validOutput = {
        success: true,
        data: {
          headline: 'Test Headline',
          confidence: 0.85,
        },
        confidence: 0.85,
        reasoning: 'Generated based on persona analysis',
      };

      expect(() => {
        copyAgent.outputSchema.parse(validOutput);
      }).not.toThrow();

      const invalidOutput = {
        success: true,
        // Missing required fields
      };

      expect(() => {
        copyAgent.outputSchema.parse(invalidOutput);
      }).toThrow();
    });
  });
});
