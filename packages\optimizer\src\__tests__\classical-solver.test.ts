import { describe, it, expect, beforeEach } from 'vitest';
import { ClassicalSolver } from '../classical/solver';
import {
  OptimizationRequest,
  ChannelConfig,
  BudgetConstraint,
  OptimizationObjective,
} from '../types';

describe('ClassicalSolver', () => {
  let solver: ClassicalSolver;
  let testRequest: OptimizationRequest;

  beforeEach(() => {
    solver = new ClassicalSolver({
      maxIterations: 500,
      convergenceThreshold: 1e-6,
      populationSize: 50,
    });

    const channels: ChannelConfig[] = [
      {
        id: 'google',
        name: 'Google Ads',
        type: 'google',
        minBudget: 1000,
        maxBudget: 40000,
        costModel: 'cpc',
        baseRate: 2.5,
        scalingFactor: 1.0,
        conversionRate: 0.04,
        averageOrderValue: 80,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 7,
      },
      {
        id: 'facebook',
        name: 'Facebook Ads',
        type: 'facebook',
        minBudget: 800,
        maxBudget: 35000,
        costModel: 'cpm',
        baseRate: 12.0,
        scalingFactor: 1.1,
        conversionRate: 0.035,
        averageOrderValue: 75,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 6,
      },
      {
        id: 'email',
        name: 'Email Marketing',
        type: 'email',
        minBudget: 200,
        maxBudget: 8000,
        costModel: 'flat',
        baseRate: 0.1,
        scalingFactor: 1.2,
        conversionRate: 0.06,
        averageOrderValue: 65,
        seasonality: Array(12).fill(1.0),
        competitiveIndex: 3,
      },
    ];

    const constraints: BudgetConstraint[] = [
      {
        id: 'total_budget',
        type: 'total_budget',
        value: 50000,
        operator: '<=',
        priority: 10,
        flexible: false,
        tolerance: 0,
      },
      {
        id: 'min_roas',
        type: 'roas_target',
        value: 3.5,
        operator: '>=',
        priority: 8,
        flexible: true,
        tolerance: 0.1,
      },
    ];

    const objective: OptimizationObjective = {
      primary: 'maximize_revenue',
      weights: { primary: 1.0, secondary: 0.0 },
      timeHorizon: 'monthly',
    };

    testRequest = {
      id: 'test-classical-1',
      totalBudget: 50000,
      channels,
      constraints,
      objective,
      timeframe: {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31'),
      },
      preferences: {
        useQuantumOptimization: false,
        maxIterations: 500,
        convergenceThreshold: 1e-6,
        riskTolerance: 'moderate',
      },
    };
  });

  describe('Algorithm Selection', () => {
    it('should select appropriate algorithm for small problems', async () => {
      const smallRequest = {
        ...testRequest,
        channels: testRequest.channels.slice(0, 2),
      };

      const result = await solver.optimize(smallRequest);
      expect(result.status).toBe('success');
      expect(result.method).toBe('classical');
    });

    it('should handle large problems with genetic algorithm', async () => {
      const largeChannels = Array(25).fill(null).map((_, i) => ({
        ...testRequest.channels[0],
        id: `channel_${i}`,
        name: `Channel ${i}`,
      }));

      const largeRequest = {
        ...testRequest,
        channels: largeChannels,
        totalBudget: 500000,
      };

      const result = await solver.optimize(largeRequest);
      expect(result.status).toBe('success');
      expect(result.allocations).toHaveLength(25);
    });
  });

  describe('Sequential Quadratic Programming', () => {
    it('should solve smooth optimization problems', async () => {
      // Force SQP by using small problem with linear constraints
      const sqpRequest = {
        ...testRequest,
        channels: testRequest.channels.slice(0, 2),
        constraints: testRequest.constraints.filter(c => c.type === 'total_budget'),
      };

      const result = await solver.optimize(sqpRequest);
      
      expect(result.status).toBe('success');
      expect(result.allocations).toHaveLength(2);
      expect(result.optimizationMetrics.feasibility).toBe(true);
      expect(result.optimizationMetrics.optimality).toBeGreaterThan(0.8);
    });
  });

  describe('Genetic Algorithm', () => {
    it('should handle complex constraint problems', async () => {
      const complexRequest = {
        ...testRequest,
        constraints: [
          ...testRequest.constraints,
          {
            id: 'max_cpa',
            type: 'cpa_target' as const,
            value: 20,
            operator: '<=' as const,
            priority: 7,
            flexible: true,
            tolerance: 0.15,
          },
        ],
      };

      const result = await solver.optimize(complexRequest);
      
      expect(result.status).toBe('success');
      expect(result.optimizationMetrics.constraintViolations).toBeLessThan(1e-3);
    });

    it('should maintain population diversity', async () => {
      const result = await solver.optimize(testRequest);
      
      // Check that allocations are reasonable and diverse
      const allocations = result.allocations;
      const weights = allocations.map(a => a.allocatedBudget / result.totalAllocated);
      
      // No single channel should dominate completely (unless optimal)
      const maxWeight = Math.max(...weights);
      expect(maxWeight).toBeLessThan(0.9);
      
      // All channels should get some allocation
      weights.forEach(weight => {
        expect(weight).toBeGreaterThan(0.01);
      });
    });
  });

  describe('Particle Swarm Optimization', () => {
    it('should optimize continuous variables effectively', async () => {
      const psoRequest = {
        ...testRequest,
        objective: {
          primary: 'maximize_roas' as const,
          weights: { primary: 1.0, secondary: 0.0 },
          timeHorizon: 'monthly' as const,
        },
      };

      const result = await solver.optimize(psoRequest);
      
      expect(result.status).toBe('success');
      expect(result.overallROAS).toBeGreaterThan(3.0);
    });
  });

  describe('Simulated Annealing', () => {
    it('should escape local optima', async () => {
      const result = await solver.optimize(testRequest);
      
      expect(result.status).toBe('success');
      expect(result.optimizationMetrics.objectiveValue).toBeGreaterThan(0);
    });
  });

  describe('Interior Point Method', () => {
    it('should handle inequality constraints', async () => {
      const constrainedRequest = {
        ...testRequest,
        constraints: [
          ...testRequest.constraints,
          {
            id: 'channel_min_google',
            type: 'channel_budget' as const,
            value: 5000,
            operator: '>=' as const,
            priority: 6,
            flexible: false,
            tolerance: 0,
          },
        ],
      };

      const result = await solver.optimize(constrainedRequest);
      
      expect(result.status).toBe('success');
      const googleAllocation = result.allocations.find(a => a.channelId === 'google');
      expect(googleAllocation?.allocatedBudget).toBeGreaterThanOrEqual(4900); // Small tolerance
    });
  });

  describe('Constraint Handling', () => {
    it('should respect budget constraints', async () => {
      const result = await solver.optimize(testRequest);
      
      expect(result.totalAllocated).toBeLessThanOrEqual(testRequest.totalBudget * 1.01);
      
      result.allocations.forEach((allocation, i) => {
        const channel = testRequest.channels[i];
        expect(allocation.allocatedBudget).toBeGreaterThanOrEqual(channel.minBudget * 0.99);
        expect(allocation.allocatedBudget).toBeLessThanOrEqual(channel.maxBudget * 1.01);
      });
    });

    it('should handle conflicting constraints gracefully', async () => {
      const conflictingRequest = {
        ...testRequest,
        totalBudget: 5000, // Too small for minimum budgets
      };

      const result = await solver.optimize(conflictingRequest);
      
      // Should still produce a result, possibly with constraint violations
      expect(result).toBeDefined();
      expect(result.status).toMatch(/success|partial/);
    });
  });

  describe('Objective Functions', () => {
    it('should maximize revenue correctly', async () => {
      const result = await solver.optimize(testRequest);
      
      expect(result.expectedTotalRevenue).toBeGreaterThan(0);
      expect(result.optimizationMetrics.objectiveValue).toBeGreaterThan(0);
    });

    it('should maximize conversions when specified', async () => {
      const conversionRequest = {
        ...testRequest,
        objective: {
          primary: 'maximize_conversions' as const,
          weights: { primary: 1.0, secondary: 0.0 },
          timeHorizon: 'monthly' as const,
        },
      };

      const result = await solver.optimize(conversionRequest);
      
      expect(result.expectedTotalConversions).toBeGreaterThan(0);
      expect(result.status).toBe('success');
    });

    it('should minimize CPA when specified', async () => {
      const cpaRequest = {
        ...testRequest,
        objective: {
          primary: 'minimize_cpa' as const,
          weights: { primary: 1.0, secondary: 0.0 },
          timeHorizon: 'monthly' as const,
        },
      };

      const result = await solver.optimize(cpaRequest);
      
      expect(result.overallCPA).toBeGreaterThan(0);
      expect(result.status).toBe('success');
    });

    it('should maximize ROAS when specified', async () => {
      const roasRequest = {
        ...testRequest,
        objective: {
          primary: 'maximize_roas' as const,
          weights: { primary: 1.0, secondary: 0.0 },
          timeHorizon: 'monthly' as const,
        },
      };

      const result = await solver.optimize(roasRequest);
      
      expect(result.overallROAS).toBeGreaterThan(1);
      expect(result.status).toBe('success');
    });
  });

  describe('Performance Metrics', () => {
    it('should provide meaningful optimization metrics', async () => {
      const result = await solver.optimize(testRequest);
      
      expect(result.optimizationMetrics.iterations).toBeGreaterThan(0);
      expect(result.optimizationMetrics.iterations).toBeLessThanOrEqual(500);
      expect(result.optimizationMetrics.objectiveValue).toBeDefined();
      expect(result.optimizationMetrics.feasibility).toBeDefined();
      expect(result.optimizationMetrics.optimality).toBeGreaterThan(0);
      expect(result.optimizationMetrics.optimality).toBeLessThanOrEqual(1);
    });

    it('should track convergence properly', async () => {
      const result = await solver.optimize(testRequest);
      
      expect(result.optimizationMetrics.convergenceRate).toBeGreaterThan(0);
      expect(result.optimizationMetrics.convergenceRate).toBeLessThanOrEqual(1);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid input gracefully', async () => {
      const invalidRequest = {
        ...testRequest,
        channels: [], // No channels
      };

      await expect(solver.optimize(invalidRequest)).rejects.toThrow();
    });

    it('should handle numerical instability', async () => {
      const unstableRequest = {
        ...testRequest,
        totalBudget: 0.01, // Very small budget
        channels: testRequest.channels.map(c => ({
          ...c,
          minBudget: 0.001,
          maxBudget: 0.005,
        })),
      };

      const result = await solver.optimize(unstableRequest);
      expect(result).toBeDefined();
    });
  });

  describe('Scalability', () => {
    it('should handle medium-sized problems efficiently', async () => {
      const mediumChannels = Array(15).fill(null).map((_, i) => ({
        ...testRequest.channels[0],
        id: `channel_${i}`,
        name: `Channel ${i}`,
        minBudget: 500 + i * 100,
        maxBudget: 5000 + i * 1000,
      }));

      const mediumRequest = {
        ...testRequest,
        channels: mediumChannels,
        totalBudget: 150000,
      };

      const startTime = Date.now();
      const result = await solver.optimize(mediumRequest);
      const endTime = Date.now();

      expect(result.status).toBe('success');
      expect(result.allocations).toHaveLength(15);
      expect(endTime - startTime).toBeLessThan(30000); // Should complete within 30 seconds
    });
  });

  describe('Deterministic Behavior', () => {
    it('should produce consistent results for deterministic algorithms', async () => {
      // Use SQP which should be deterministic
      const deterministicRequest = {
        ...testRequest,
        channels: testRequest.channels.slice(0, 2),
        constraints: testRequest.constraints.filter(c => c.type === 'total_budget'),
      };

      const result1 = await solver.optimize(deterministicRequest);
      const result2 = await solver.optimize(deterministicRequest);

      // Results should be very similar (allowing for small numerical differences)
      expect(Math.abs(result1.expectedTotalRevenue - result2.expectedTotalRevenue))
        .toBeLessThan(result1.expectedTotalRevenue * 0.01);
    });
  });
});
