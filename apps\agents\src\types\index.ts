import { z } from 'zod';

// Base agent types
export type AgentType = 'copy' | 'design' | 'analyst' | 'buyer' | 'ethics' | 'qa';

export interface AgentConfig {
  name: string;
  type: AgentType;
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
}

// Campaign and creative types
export const PersonaSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  demographics: z.record(z.any()).optional(),
  psychographics: z.record(z.any()).optional(),
  behaviors: z.record(z.any()).optional(),
  interests: z.array(z.string()).optional(),
  embedding: z.array(z.number()).optional(),
});

export const CreativeSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['image', 'video', 'text', 'carousel', 'story']),
  copy_headline: z.string().optional(),
  copy_description: z.string().optional(),
  copy_text: z.string().optional(),
  copy_cta: z.string().optional(),
  content_url: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  embedding: z.array(z.number()).optional(),
});

export const CampaignSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  objectives: z.record(z.any()).optional(),
  target_personas: z.array(z.string()).optional(),
  budget_total: z.number().optional(),
  budget_daily: z.number().optional(),
  channels: z.array(z.enum(['facebook', 'instagram', 'google', 'tiktok', 'linkedin', 'email', 'push'])).optional(),
});

export type Persona = z.infer<typeof PersonaSchema>;
export type Creative = z.infer<typeof CreativeSchema>;
export type Campaign = z.infer<typeof CampaignSchema>;

// Agent execution context
export interface AgentContext {
  campaignId: string;
  creativeId?: string;
  executionId: string;
  userId: string;
  organizationId: string;
  campaign: Campaign;
  personas: Persona[];
  existingCreatives: Creative[];
  objectives: Record<string, any>;
  constraints: Record<string, any>;
  metadata: Record<string, any>;
}

// Agent input/output schemas
export const CopyAgentInputSchema = z.object({
  persona: PersonaSchema,
  objective: z.string(),
  tone: z.string(),
  format: z.enum(['headline', 'description', 'cta', 'full_copy']),
  constraints: z.object({
    maxLength: z.number().optional(),
    keywords: z.array(z.string()).optional(),
    brandGuidelines: z.string().optional(),
  }).optional(),
});

export const DesignAgentInputSchema = z.object({
  copy: z.object({
    headline: z.string().optional(),
    description: z.string().optional(),
    cta: z.string().optional(),
  }),
  style: z.string(),
  format: z.enum(['image', 'video', 'carousel']),
  dimensions: z.object({
    width: z.number(),
    height: z.number(),
  }),
  constraints: z.object({
    colorScheme: z.string().optional(),
    brandColors: z.array(z.string()).optional(),
    logoUrl: z.string().optional(),
  }).optional(),
});

export const AnalystAgentInputSchema = z.object({
  timeframe: z.string(),
  metrics: z.array(z.string()),
  campaignId: z.string(),
  creativeIds: z.array(z.string()).optional(),
});

export const BuyerAgentInputSchema = z.object({
  campaign: CampaignSchema,
  budget: z.number(),
  channels: z.array(z.string()),
  targetAudience: z.record(z.any()),
  bidStrategy: z.enum(['maximize_clicks', 'maximize_conversions', 'target_cpa', 'target_roas']),
});

export const EthicsAgentInputSchema = z.object({
  creative: CreativeSchema,
  targetAudience: PersonaSchema,
  regulations: z.array(z.string()).optional(),
  brandGuidelines: z.string().optional(),
});

export const QAAgentInputSchema = z.object({
  creative: CreativeSchema,
  campaign: CampaignSchema,
  testCriteria: z.array(z.string()),
  qualityThresholds: z.record(z.number()).optional(),
});

// Agent output schemas
export const AgentOutputSchema = z.object({
  success: z.boolean(),
  data: z.record(z.any()),
  confidence: z.number().min(0).max(1),
  reasoning: z.string(),
  suggestions: z.array(z.string()).optional(),
  errors: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

export type CopyAgentInput = z.infer<typeof CopyAgentInputSchema>;
export type DesignAgentInput = z.infer<typeof DesignAgentInputSchema>;
export type AnalystAgentInput = z.infer<typeof AnalystAgentInputSchema>;
export type BuyerAgentInput = z.infer<typeof BuyerAgentInputSchema>;
export type EthicsAgentInput = z.infer<typeof EthicsAgentInputSchema>;
export type QAAgentInput = z.infer<typeof QAAgentInputSchema>;
export type AgentOutput = z.infer<typeof AgentOutputSchema>;

// Workflow state
export interface WorkflowState {
  campaignId: string;
  executionId: string;
  currentStep: string;
  context: AgentContext;
  results: Record<AgentType, AgentOutput | null>;
  errors: string[];
  startTime: Date;
  endTime?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

// Agent execution result
export interface AgentExecutionResult {
  agentType: AgentType;
  executionId: string;
  status: 'completed' | 'failed';
  input: any;
  output: AgentOutput;
  duration: number;
  error?: string;
  metadata?: Record<string, any>;
}
