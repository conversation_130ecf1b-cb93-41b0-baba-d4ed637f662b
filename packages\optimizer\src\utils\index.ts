import { QuantumOptimizer } from '../quantum/optimizer';
import {
  OptimizationRequest,
  OptimizationR<PERSON>ult,
  WillowConfig,
  Schemas,
  QuantumOptimizationError,
} from '../types';

/**
 * Utility functions for the optimizer package
 */

/**
 * Create a configured optimizer instance
 */
export function createOptimizer(config?: Partial<WillowConfig>): QuantumOptimizer {
  const defaultConfig: WillowConfig = {
    endpoint: process.env.WILLOW_ENDPOINT || 'https://quantum.googleapis.com',
    apiKey: process.env.WILLOW_API_KEY || '',
    projectId: process.env.GOOGLE_CLOUD_PROJECT || '',
    region: 'us-central1',
    timeout: 30000,
    retries: 3,
    circuitConfig: {
      qubits: 20,
      depth: 100,
      gateSet: ['H', 'X', 'Y', 'Z', 'CNOT', 'CZ', 'RX', 'RY', 'RZ'],
      errorCorrection: true,
      coherenceTime: 100,
      fidelity: 0.999,
    },
  };

  const mergedConfig = { ...defaultConfig, ...config };
  
  // Validate required fields
  if (!mergedConfig.apiKey) {
    throw new QuantumOptimizationError(
      'Willow API key is required',
      'MISSING_API_KEY'
    );
  }

  if (!mergedConfig.projectId) {
    throw new QuantumOptimizationError(
      'Google Cloud project ID is required',
      'MISSING_PROJECT_ID'
    );
  }

  return new QuantumOptimizer(mergedConfig);
}

/**
 * Validate optimization request
 */
export function validateRequest(request: OptimizationRequest): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Use Zod schema validation
    Schemas.OptimizationRequest.parse(request);
  } catch (error: any) {
    if (error.errors) {
      errors.push(...error.errors.map((e: any) => `${e.path.join('.')}: ${e.message}`));
    } else {
      errors.push(error.message);
    }
  }

  // Additional business logic validation
  if (request.channels.length === 0) {
    errors.push('At least one channel is required');
  }

  if (request.channels.length > 50) {
    warnings.push('Large number of channels may impact performance');
  }

  // Check budget constraints
  const totalMinBudget = request.channels.reduce((sum, c) => sum + c.minBudget, 0);
  if (totalMinBudget > request.totalBudget) {
    errors.push('Total minimum budget exceeds available budget');
  }

  // Check for conflicting constraints
  const budgetConstraints = request.constraints.filter(c => c.type === 'total_budget');
  if (budgetConstraints.length > 1) {
    warnings.push('Multiple total budget constraints detected');
  }

  // Validate timeframe
  if (request.timeframe.end <= request.timeframe.start) {
    errors.push('End date must be after start date');
  }

  const timeframeDays = (request.timeframe.end.getTime() - request.timeframe.start.getTime()) / (1000 * 60 * 60 * 24);
  if (timeframeDays > 365) {
    warnings.push('Long timeframes may reduce optimization accuracy');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Format optimization result for display
 */
export function formatResult(result: OptimizationResult): {
  summary: string;
  allocations: Array<{
    channel: string;
    budget: string;
    percentage: string;
    expectedROAS: string;
    expectedCPA: string;
    confidence: string;
  }>;
  metrics: {
    totalBudget: string;
    expectedRevenue: string;
    overallROAS: string;
    overallCPA: string;
    confidence: string;
    riskScore: string;
    method: string;
    computeTime: string;
  };
  recommendations: string[];
} {
  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  
  const formatPercentage = (value: number) => 
    new Intl.NumberFormat('en-US', { style: 'percent', minimumFractionDigits: 1 }).format(value);

  const summary = `${result.method.toUpperCase()} optimization ${result.status} with ${formatCurrency(result.expectedTotalRevenue)} expected revenue and ${formatPercentage(result.confidence)} confidence.`;

  const allocations = result.allocations.map(allocation => ({
    channel: allocation.channelId,
    budget: formatCurrency(allocation.allocatedBudget),
    percentage: formatPercentage(allocation.allocatedBudget / result.totalAllocated),
    expectedROAS: allocation.expectedROAS.toFixed(2) + 'x',
    expectedCPA: formatCurrency(allocation.expectedCPA),
    confidence: formatPercentage(allocation.confidence),
  }));

  const metrics = {
    totalBudget: formatCurrency(result.totalAllocated),
    expectedRevenue: formatCurrency(result.expectedTotalRevenue),
    overallROAS: result.overallROAS.toFixed(2) + 'x',
    overallCPA: formatCurrency(result.overallCPA),
    confidence: formatPercentage(result.confidence),
    riskScore: `${result.riskScore}/10`,
    method: result.method.toUpperCase(),
    computeTime: `${result.computeTime}ms`,
  };

  const recommendations = result.recommendations.map(rec => 
    `${rec.priority.toUpperCase()}: ${rec.description}`
  );

  return {
    summary,
    allocations,
    metrics,
    recommendations,
  };
}

/**
 * Calculate portfolio risk metrics
 */
export function calculatePortfolioRisk(result: OptimizationResult): {
  diversificationScore: number;
  concentrationRisk: number;
  volatilityScore: number;
  overallRisk: number;
} {
  const allocations = result.allocations;
  const weights = allocations.map(a => a.allocatedBudget / result.totalAllocated);

  // Diversification score (higher is better)
  const diversificationScore = 1 - calculateHerfindahlIndex(weights);

  // Concentration risk (percentage in largest allocation)
  const concentrationRisk = Math.max(...weights);

  // Volatility score based on risk scores
  const avgRiskScore = allocations.reduce((sum, a) => sum + a.riskScore, 0) / allocations.length;
  const volatilityScore = avgRiskScore / 10;

  // Overall risk (0-1, lower is better)
  const overallRisk = (concentrationRisk + volatilityScore + (1 - diversificationScore)) / 3;

  return {
    diversificationScore,
    concentrationRisk,
    volatilityScore,
    overallRisk,
  };
}

/**
 * Calculate Herfindahl-Hirschman Index for concentration
 */
function calculateHerfindahlIndex(weights: number[]): number {
  return weights.reduce((sum, weight) => sum + weight * weight, 0);
}

/**
 * Generate optimization insights
 */
export function generateInsights(result: OptimizationResult): {
  topPerformers: string[];
  underperformers: string[];
  opportunities: string[];
  risks: string[];
} {
  const allocations = result.allocations;
  
  // Sort by ROAS
  const sortedByROAS = [...allocations].sort((a, b) => b.expectedROAS - a.expectedROAS);
  
  const topPerformers = sortedByROAS
    .slice(0, Math.ceil(allocations.length * 0.3))
    .map(a => a.channelId);

  const underperformers = sortedByROAS
    .slice(-Math.ceil(allocations.length * 0.3))
    .filter(a => a.expectedROAS < result.overallROAS)
    .map(a => a.channelId);

  const opportunities: string[] = [];
  const risks: string[] = [];

  allocations.forEach(allocation => {
    if (allocation.confidence < 0.7) {
      risks.push(`Low confidence in ${allocation.channelId} allocation`);
    }
    
    if (allocation.riskScore > 7) {
      risks.push(`High risk detected for ${allocation.channelId}`);
    }
    
    if (allocation.expectedROAS > result.overallROAS * 1.5) {
      opportunities.push(`Consider increasing budget for high-performing ${allocation.channelId}`);
    }
    
    if (allocation.allocatedBudget / result.totalAllocated > 0.5) {
      risks.push(`High concentration risk in ${allocation.channelId}`);
    }
  });

  return {
    topPerformers,
    underperformers,
    opportunities,
    risks,
  };
}

/**
 * Export optimization result to various formats
 */
export function exportResult(result: OptimizationResult, format: 'json' | 'csv' | 'xlsx'): string | Buffer {
  switch (format) {
    case 'json':
      return JSON.stringify(result, null, 2);
    
    case 'csv':
      const headers = ['Channel', 'Budget', 'Expected Revenue', 'Expected Conversions', 'CPA', 'ROAS', 'Confidence', 'Risk Score'];
      const rows = result.allocations.map(a => [
        a.channelId,
        a.allocatedBudget.toString(),
        a.expectedRevenue.toString(),
        a.expectedConversions.toString(),
        a.expectedCPA.toString(),
        a.expectedROAS.toString(),
        a.confidence.toString(),
        a.riskScore.toString(),
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    
    case 'xlsx':
      // In a real implementation, you'd use a library like xlsx
      throw new Error('XLSX export not implemented in this example');
    
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
}

/**
 * Compare two optimization results
 */
export function compareResults(result1: OptimizationResult, result2: OptimizationResult): {
  revenueDifference: number;
  roasDifference: number;
  cpaDifference: number;
  confidenceDifference: number;
  riskDifference: number;
  betterResult: 'result1' | 'result2' | 'similar';
  summary: string;
} {
  const revenueDifference = result1.expectedTotalRevenue - result2.expectedTotalRevenue;
  const roasDifference = result1.overallROAS - result2.overallROAS;
  const cpaDifference = result1.overallCPA - result2.overallCPA;
  const confidenceDifference = result1.confidence - result2.confidence;
  const riskDifference = result1.riskScore - result2.riskScore;

  let betterResult: 'result1' | 'result2' | 'similar' = 'similar';
  let score1 = 0;
  let score2 = 0;

  // Score based on multiple criteria
  if (revenueDifference > 0) score1++; else if (revenueDifference < 0) score2++;
  if (roasDifference > 0) score1++; else if (roasDifference < 0) score2++;
  if (cpaDifference < 0) score1++; else if (cpaDifference > 0) score2++; // Lower CPA is better
  if (confidenceDifference > 0) score1++; else if (confidenceDifference < 0) score2++;
  if (riskDifference < 0) score1++; else if (riskDifference > 0) score2++; // Lower risk is better

  if (score1 > score2) betterResult = 'result1';
  else if (score2 > score1) betterResult = 'result2';

  const summary = `Result comparison: ${betterResult === 'similar' ? 'Results are similar' : 
    betterResult === 'result1' ? 'First result is better' : 'Second result is better'} 
    (Revenue: ${revenueDifference > 0 ? '+' : ''}${revenueDifference.toFixed(0)}, 
    ROAS: ${roasDifference > 0 ? '+' : ''}${roasDifference.toFixed(2)})`;

  return {
    revenueDifference,
    roasDifference,
    cpaDifference,
    confidenceDifference,
    riskDifference,
    betterResult,
    summary,
  };
}
