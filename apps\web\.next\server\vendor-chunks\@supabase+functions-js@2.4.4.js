"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+functions-js@2.4.4";
exports.ids = ["vendor-chunks/@supabase+functions-js@2.4.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js":
/*!********************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionsClient: () => (/* binding */ FunctionsClient)\n/* harmony export */ });\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helper */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\nclass FunctionsClient {\n    constructor(url, { headers = {}, customFetch, region = _types__WEBPACK_IMPORTED_MODULE_0__.FunctionRegion.Any, } = {}) {\n        this.url = url;\n        this.headers = headers;\n        this.region = region;\n        this.fetch = (0,_helper__WEBPACK_IMPORTED_MODULE_1__.resolveFetch)(customFetch);\n    }\n    /**\n     * Updates the authorization header\n     * @param token - the new jwt token sent in the authorisation header\n     */\n    setAuth(token) {\n        this.headers.Authorization = `Bearer ${token}`;\n    }\n    /**\n     * Invokes a function\n     * @param functionName - The name of the Function to invoke.\n     * @param options - Options for invoking the Function.\n     */\n    invoke(functionName, options = {}) {\n        var _a;\n        return __awaiter(this, void 0, void 0, function* () {\n            try {\n                const { headers, method, body: functionArgs } = options;\n                let _headers = {};\n                let { region } = options;\n                if (!region) {\n                    region = this.region;\n                }\n                if (region && region !== 'any') {\n                    _headers['x-region'] = region;\n                }\n                let body;\n                if (functionArgs &&\n                    ((headers && !Object.prototype.hasOwnProperty.call(headers, 'Content-Type')) || !headers)) {\n                    if ((typeof Blob !== 'undefined' && functionArgs instanceof Blob) ||\n                        functionArgs instanceof ArrayBuffer) {\n                        // will work for File as File inherits Blob\n                        // also works for ArrayBuffer as it is the same underlying structure as a Blob\n                        _headers['Content-Type'] = 'application/octet-stream';\n                        body = functionArgs;\n                    }\n                    else if (typeof functionArgs === 'string') {\n                        // plain string\n                        _headers['Content-Type'] = 'text/plain';\n                        body = functionArgs;\n                    }\n                    else if (typeof FormData !== 'undefined' && functionArgs instanceof FormData) {\n                        // don't set content-type headers\n                        // Request will automatically add the right boundary value\n                        body = functionArgs;\n                    }\n                    else {\n                        // default, assume this is JSON\n                        _headers['Content-Type'] = 'application/json';\n                        body = JSON.stringify(functionArgs);\n                    }\n                }\n                const response = yield this.fetch(`${this.url}/${functionName}`, {\n                    method: method || 'POST',\n                    // headers priority is (high to low):\n                    // 1. invoke-level headers\n                    // 2. client-level headers\n                    // 3. default Content-Type header\n                    headers: Object.assign(Object.assign(Object.assign({}, _headers), this.headers), headers),\n                    body,\n                }).catch((fetchError) => {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsFetchError(fetchError);\n                });\n                const isRelayError = response.headers.get('x-relay-error');\n                if (isRelayError && isRelayError === 'true') {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsRelayError(response);\n                }\n                if (!response.ok) {\n                    throw new _types__WEBPACK_IMPORTED_MODULE_0__.FunctionsHttpError(response);\n                }\n                let responseType = ((_a = response.headers.get('Content-Type')) !== null && _a !== void 0 ? _a : 'text/plain').split(';')[0].trim();\n                let data;\n                if (responseType === 'application/json') {\n                    data = yield response.json();\n                }\n                else if (responseType === 'application/octet-stream') {\n                    data = yield response.blob();\n                }\n                else if (responseType === 'text/event-stream') {\n                    data = response;\n                }\n                else if (responseType === 'multipart/form-data') {\n                    data = yield response.formData();\n                }\n                else {\n                    // default to text\n                    data = yield response.text();\n                }\n                return { data, error: null };\n            }\n            catch (error) {\n                return { data: null, error };\n            }\n        });\n    }\n}\n//# sourceMappingURL=FunctionsClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js":
/*!***********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch)\n/* harmony export */ });\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (...args) => Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/../../node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch }) => fetch(...args));\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\n//# sourceMappingURL=helper.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStmdW5jdGlvbnMtanNAMi40LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9mdW5jdGlvbnMtanMvZGlzdC9tb2R1bGUvaGVscGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsbU9BQThCLFNBQVMsZ0JBQWdCO0FBQ3JGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJlZHBhbmRhXFxEZXNrdG9wXFxNZXRhbW9ycGhpYyBmbHV4XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2UrZnVuY3Rpb25zLWpzQDIuNC40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcZnVuY3Rpb25zLWpzXFxkaXN0XFxtb2R1bGVcXGhlbHBlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgcmVzb2x2ZUZldGNoID0gKGN1c3RvbUZldGNoKSA9PiB7XG4gICAgbGV0IF9mZXRjaDtcbiAgICBpZiAoY3VzdG9tRmV0Y2gpIHtcbiAgICAgICAgX2ZldGNoID0gY3VzdG9tRmV0Y2g7XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGVvZiBmZXRjaCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgX2ZldGNoID0gKC4uLmFyZ3MpID0+IGltcG9ydCgnQHN1cGFiYXNlL25vZGUtZmV0Y2gnKS50aGVuKCh7IGRlZmF1bHQ6IGZldGNoIH0pID0+IGZldGNoKC4uLmFyZ3MpKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIF9mZXRjaCA9IGZldGNoO1xuICAgIH1cbiAgICByZXR1cm4gKC4uLmFyZ3MpID0+IF9mZXRjaCguLi5hcmdzKTtcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWxwZXIuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/helper.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js":
/*!**********************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FunctionRegion: () => (/* binding */ FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* binding */ FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* binding */ FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* binding */ FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* binding */ FunctionsRelayError)\n/* harmony export */ });\nclass FunctionsError extends Error {\n    constructor(message, name = 'FunctionsError', context) {\n        super(message);\n        this.name = name;\n        this.context = context;\n    }\n}\nclass FunctionsFetchError extends FunctionsError {\n    constructor(context) {\n        super('Failed to send a request to the Edge Function', 'FunctionsFetchError', context);\n    }\n}\nclass FunctionsRelayError extends FunctionsError {\n    constructor(context) {\n        super('Relay Error invoking the Edge Function', 'FunctionsRelayError', context);\n    }\n}\nclass FunctionsHttpError extends FunctionsError {\n    constructor(context) {\n        super('Edge Function returned a non-2xx status code', 'FunctionsHttpError', context);\n    }\n}\n// Define the enum for the 'region' property\nvar FunctionRegion;\n(function (FunctionRegion) {\n    FunctionRegion[\"Any\"] = \"any\";\n    FunctionRegion[\"ApNortheast1\"] = \"ap-northeast-1\";\n    FunctionRegion[\"ApNortheast2\"] = \"ap-northeast-2\";\n    FunctionRegion[\"ApSouth1\"] = \"ap-south-1\";\n    FunctionRegion[\"ApSoutheast1\"] = \"ap-southeast-1\";\n    FunctionRegion[\"ApSoutheast2\"] = \"ap-southeast-2\";\n    FunctionRegion[\"CaCentral1\"] = \"ca-central-1\";\n    FunctionRegion[\"EuCentral1\"] = \"eu-central-1\";\n    FunctionRegion[\"EuWest1\"] = \"eu-west-1\";\n    FunctionRegion[\"EuWest2\"] = \"eu-west-2\";\n    FunctionRegion[\"EuWest3\"] = \"eu-west-3\";\n    FunctionRegion[\"SaEast1\"] = \"sa-east-1\";\n    FunctionRegion[\"UsEast1\"] = \"us-east-1\";\n    FunctionRegion[\"UsWest1\"] = \"us-west-1\";\n    FunctionRegion[\"UsWest2\"] = \"us-west-2\";\n})(FunctionRegion || (FunctionRegion = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js\n");

/***/ })

};
;