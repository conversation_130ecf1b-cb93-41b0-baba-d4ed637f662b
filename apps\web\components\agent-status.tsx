'use client';

import { useState, useEffect } from 'react';
import { useAgents } from '@/hooks/use-agents';
import { Button } from '@/components/ui/button';
import { 
  Brain, 
  Zap, 
  Palette, 
  BarChart3, 
  Shield, 
  CheckCircle,
  Activity,
  AlertCircle,
  Clock,
  Cpu,
  Sparkles
} from 'lucide-react';

interface AgentStatusProps {
  className?: string;
  showDetails?: boolean;
}

interface AgentInfo {
  type: string;
  name: string;
  icon: React.ComponentType<any>;
  status: 'ready' | 'busy' | 'error' | 'offline';
  lastExecution?: Date;
  executionCount: number;
  averageTime: number;
  confidence: number;
}

export function AgentStatus({ className, showDetails = false }: AgentStatusProps): React.JSX.Element {
  const { agentStatus, healthStatus, copyAgent, designAgent, analystAgent } = useAgents();
  const [agents, setAgents] = useState<AgentInfo[]>([]);

  useEffect(() => {
    const agentInfo: AgentInfo[] = [
      {
        type: 'copy',
        name: 'Copy Agent',
        icon: Brain,
        status: copyAgent.isLoading ? 'busy' : 'ready',
        executionCount: 42,
        averageTime: 1250,
        confidence: 0.89,
      },
      {
        type: 'design',
        name: 'Design Agent',
        icon: Palette,
        status: designAgent.isLoading ? 'busy' : 'ready',
        executionCount: 38,
        averageTime: 3500,
        confidence: 0.92,
      },
      {
        type: 'analyst',
        name: 'Analyst Agent',
        icon: BarChart3,
        status: analystAgent.isLoading ? 'busy' : 'ready',
        executionCount: 156,
        averageTime: 850,
        confidence: 0.94,
      },
      {
        type: 'buyer',
        name: 'Buyer Agent',
        icon: Zap,
        status: 'ready',
        executionCount: 23,
        averageTime: 2100,
        confidence: 0.87,
      },
      {
        type: 'ethics',
        name: 'Ethics Agent',
        icon: Shield,
        status: 'ready',
        executionCount: 67,
        averageTime: 650,
        confidence: 0.96,
      },
      {
        type: 'qa',
        name: 'QA Agent',
        icon: CheckCircle,
        status: 'ready',
        executionCount: 89,
        averageTime: 450,
        confidence: 0.91,
      },
    ];

    setAgents(agentInfo);
  }, [copyAgent.isLoading, designAgent.isLoading, analystAgent.isLoading]);

  const getStatusIcon = (status: AgentInfo['status']) => {
    switch (status) {
      case 'busy': return <Activity className="h-4 w-4 text-yellow-500 animate-pulse" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'offline': return <Clock className="h-4 w-4 text-gray-500" />;
      default: return <CheckCircle className="h-4 w-4 text-green-500" />;
    }
  };

  const getStatusColor = (status: AgentInfo['status']) => {
    switch (status) {
      case 'busy': return 'border-yellow-500/50 bg-yellow-500/10';
      case 'error': return 'border-red-500/50 bg-red-500/10';
      case 'offline': return 'border-gray-500/50 bg-gray-500/10';
      default: return 'border-green-500/50 bg-green-500/10';
    }
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatConfidence = (confidence: number): string => {
    return `${(confidence * 100).toFixed(0)}%`;
  };

  if (!showDetails) {
    // Compact view
    return (
      <div className={`flex items-center space-x-2 ${className ?? ''}`}>
        <div className="flex items-center space-x-1">
          {agents.slice(0, 3).map(agent => {
            const Icon = agent.icon;
            return (
              <div
                key={agent.type}
                className={`w-8 h-8 rounded-full border flex items-center justify-center ${getStatusColor(agent.status)}`}
                title={`${agent.name}: ${agent.status}`}
              >
                <Icon className="h-4 w-4" />
              </div>
            );
          })}
          {agents.length > 3 && (
            <div className="w-8 h-8 rounded-full border border-gray-500/50 bg-gray-500/10 flex items-center justify-center text-xs">
              +{agents.length - 3}
            </div>
          )}
        </div>
        
        <div className="text-sm">
          <span className="text-muted-foreground">Agents:</span>
          <span className="ml-1 text-green-500 font-medium">
            {agents.filter(a => a.status === 'ready').length}/{agents.length} Ready
          </span>
        </div>
      </div>
    );
  }

  // Detailed view
  return (
    <div className={`space-y-4 ${className ?? ''}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center">
          <Cpu className="h-5 w-5 mr-2 text-flux-500" />
          Agent Swarm Status
        </h3>
        
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            healthStatus?.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'
          }`} />
          <span className="text-sm text-muted-foreground">
            {healthStatus?.status === 'healthy' ? 'All Systems Operational' : 'System Issues Detected'}
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {agents.map(agent => {
          const Icon = agent.icon;
          
          return (
            <div
              key={agent.type}
              className={`p-4 rounded-lg border backdrop-blur-sm transition-all duration-300 hover:scale-105 ${getStatusColor(agent.status)}`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Icon className="h-5 w-5 text-flux-500" />
                  <span className="font-medium">{agent.name}</span>
                </div>
                {getStatusIcon(agent.status)}
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Executions:</span>
                  <span className="font-medium">{agent.executionCount}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Avg Time:</span>
                  <span className="font-medium">{formatTime(agent.averageTime)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Confidence:</span>
                  <span className="font-medium text-green-500">
                    {formatConfidence(agent.confidence)}
                  </span>
                </div>
              </div>

              {agent.status === 'busy' && (
                <div className="mt-3">
                  <div className="flex items-center space-x-2 text-xs text-yellow-600">
                    <Sparkles className="h-3 w-3 animate-pulse" />
                    <span>Processing...</span>
                  </div>
                  <div className="mt-1 w-full bg-gray-200 rounded-full h-1">
                    <div className="bg-yellow-500 h-1 rounded-full animate-pulse" style={{ width: '60%' }} />
                  </div>
                </div>
              )}

              <Button
                variant="ghost"
                size="sm"
                className="w-full mt-3 text-xs"
                disabled={agent.status === 'busy'}
              >
                {agent.status === 'busy' ? 'Processing...' : 'Test Agent'}
              </Button>
            </div>
          );
        })}
      </div>

      {/* System Metrics */}
      <div className="bg-card/50 backdrop-blur-sm border rounded-lg p-4">
        <h4 className="font-medium mb-3 flex items-center">
          <Activity className="h-4 w-4 mr-2" />
          System Metrics
        </h4>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-muted-foreground block">Total Executions</span>
            <span className="text-xl font-bold text-flux-500">
              {agents.reduce((sum, agent) => sum + agent.executionCount, 0)}
            </span>
          </div>
          
          <div>
            <span className="text-muted-foreground block">Avg Response Time</span>
            <span className="text-xl font-bold text-green-500">
              {formatTime(agents.reduce((sum, agent) => sum + agent.averageTime, 0) / agents.length)}
            </span>
          </div>
          
          <div>
            <span className="text-muted-foreground block">Success Rate</span>
            <span className="text-xl font-bold text-green-500">99.2%</span>
          </div>
          
          <div>
            <span className="text-muted-foreground block">Active Agents</span>
            <span className="text-xl font-bold text-flux-500">
              {agents.filter(a => a.status === 'ready' || a.status === 'busy').length}/{agents.length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
