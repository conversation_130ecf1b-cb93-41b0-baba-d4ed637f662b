import type { <PERSON>ada<PERSON> } from 'next';
import { GeistSans } from 'geist/font/sans';
import { Geist<PERSON>ono } from 'geist/font/mono';
import './globals.css';
import { Providers } from './providers';
import { cn } from '@/lib/utils';

export const metadata: Metadata = {
  title: 'Metamorphic Flux™ - AI Marketing Engine',
  description: 'Dynamically shape-shift campaign creative, channels, and spend with AI-powered marketing automation.',
  keywords: ['AI marketing', 'dynamic creative optimization', 'campaign automation', 'marketing engine'],
  authors: [{ name: '<PERSON> (Digital Mind Pulse)' }],
  creator: 'Metamorphic Flux',
  publisher: 'Metamorphic Flux',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL ?? 'http://localhost:3000'),
  openGraph: {
    title: 'Metamorphic Flux™ - AI Marketing Engine',
    description: 'Dynamically shape-shift campaign creative, channels, and spend with AI-powered marketing automation.',
    url: '/',
    siteName: 'Metamorphic Flux',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Metamorphic Flux™ - AI Marketing Engine',
    description: 'Dynamically shape-shift campaign creative, channels, and spend with AI-powered marketing automation.',
    creator: '@metamorphicflux',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps): JSX.Element {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          GeistSans.variable,
          GeistMono.variable
        )}
      >
        <Providers>
          <div className="relative flex min-h-screen flex-col">
            <div className="flex-1">{children}</div>
          </div>
        </Providers>
      </body>
    </html>
  );
}
