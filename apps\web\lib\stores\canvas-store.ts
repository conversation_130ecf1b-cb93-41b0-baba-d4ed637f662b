import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Canvas Node Types
export interface CanvasNode {
  id: string;
  type: 'persona' | 'creative' | 'channel' | 'metric' | 'agent' | 'workflow';
  title: string;
  subtitle?: string;
  status: 'idle' | 'processing' | 'complete' | 'error' | 'warning';
  position: { x: number; y: number };
  size: { width: number; height: number };
  connections: string[];
  data?: Record<string, any>;
  metadata?: {
    createdAt: Date;
    updatedAt: Date;
    version: number;
  };
}

// Canvas Connection Types
export interface CanvasConnection {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'data' | 'workflow' | 'dependency';
  status: 'active' | 'inactive' | 'error';
  animated?: boolean;
  style?: {
    color?: string;
    width?: number;
    dashArray?: string;
  };
}

// Canvas Viewport
export interface CanvasViewport {
  x: number;
  y: number;
  zoom: number;
  bounds: {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
  };
}

// Canvas Settings
export interface CanvasSettings {
  theme: 'light' | 'dark' | 'auto';
  gridEnabled: boolean;
  snapToGrid: boolean;
  gridSize: number;
  showMinimap: boolean;
  showMetrics: boolean;
  autoSave: boolean;
  animations: {
    enabled: boolean;
    duration: number;
    easing: string;
  };
  performance: {
    maxNodes: number;
    maxConnections: number;
    renderOptimization: boolean;
  };
}

// Canvas State Interface
interface CanvasState {
  // Core State
  nodes: CanvasNode[];
  connections: CanvasConnection[];
  selectedNodes: string[];
  selectedConnections: string[];
  viewport: CanvasViewport;
  settings: CanvasSettings;
  
  // UI State
  isPlaying: boolean;
  isPanning: boolean;
  isDragging: boolean;
  isSelecting: boolean;
  showContextMenu: boolean;
  contextMenuPosition: { x: number; y: number } | null;
  
  // History State
  history: {
    past: Array<{ nodes: CanvasNode[]; connections: CanvasConnection[] }>;
    present: { nodes: CanvasNode[]; connections: CanvasConnection[] };
    future: Array<{ nodes: CanvasNode[]; connections: CanvasConnection[] }>;
  };
  
  // Performance State
  metrics: {
    fps: number;
    nodeCount: number;
    connectionCount: number;
    renderTime: number;
  };
}

// Canvas Actions Interface
interface CanvasActions {
  // Node Management
  addNode: (node: Omit<CanvasNode, 'id' | 'metadata'>) => void;
  updateNode: (id: string, updates: Partial<CanvasNode>) => void;
  removeNode: (id: string) => void;
  duplicateNode: (id: string) => void;
  moveNode: (id: string, position: { x: number; y: number }) => void;
  
  // Connection Management
  addConnection: (connection: Omit<CanvasConnection, 'id'>) => void;
  updateConnection: (id: string, updates: Partial<CanvasConnection>) => void;
  removeConnection: (id: string) => void;
  
  // Selection Management
  selectNode: (id: string, multi?: boolean) => void;
  selectConnection: (id: string, multi?: boolean) => void;
  clearSelection: () => void;
  selectAll: () => void;
  
  // Viewport Management
  setViewport: (viewport: Partial<CanvasViewport>) => void;
  zoomIn: () => void;
  zoomOut: () => void;
  zoomToFit: () => void;
  resetViewport: () => void;
  
  // Workflow Management
  startWorkflow: () => void;
  stopWorkflow: () => void;
  pauseWorkflow: () => void;
  resumeWorkflow: () => void;
  
  // History Management
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;
  
  // Settings Management
  updateSettings: (settings: Partial<CanvasSettings>) => void;
  resetSettings: () => void;
  
  // Utility Actions
  exportCanvas: () => string;
  importCanvas: (data: string) => void;
  clearCanvas: () => void;
  updateMetrics: (metrics: Partial<CanvasState['metrics']>) => void;
}

// Default Settings
const defaultSettings: CanvasSettings = {
  theme: 'auto',
  gridEnabled: true,
  snapToGrid: true,
  gridSize: 20,
  showMinimap: false,
  showMetrics: false,
  autoSave: true,
  animations: {
    enabled: true,
    duration: 300,
    easing: 'ease-out',
  },
  performance: {
    maxNodes: 100,
    maxConnections: 200,
    renderOptimization: true,
  },
};

// Default Viewport
const defaultViewport: CanvasViewport = {
  x: 0,
  y: 0,
  zoom: 1,
  bounds: {
    minX: -5000,
    minY: -5000,
    maxX: 5000,
    maxY: 5000,
  },
};

// Create Canvas Store
export const useCanvasStore = create<CanvasState & CanvasActions>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial State
        nodes: [],
        connections: [],
        selectedNodes: [],
        selectedConnections: [],
        viewport: defaultViewport,
        settings: defaultSettings,
        isPlaying: false,
        isPanning: false,
        isDragging: false,
        isSelecting: false,
        showContextMenu: false,
        contextMenuPosition: null,
        history: {
          past: [],
          present: { nodes: [], connections: [] },
          future: [],
        },
        metrics: {
          fps: 60,
          nodeCount: 0,
          connectionCount: 0,
          renderTime: 0,
        },

        // Node Management Actions
        addNode: (nodeData) =>
          set((state) => {
            const node: CanvasNode = {
              ...nodeData,
              id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              metadata: {
                createdAt: new Date(),
                updatedAt: new Date(),
                version: 1,
              },
            };
            state.nodes.push(node);
            state.metrics.nodeCount = state.nodes.length;
          }),

        updateNode: (id, updates) =>
          set((state) => {
            const nodeIndex = state.nodes.findIndex((n) => n.id === id);
            if (nodeIndex !== -1) {
              Object.assign(state.nodes[nodeIndex], updates);
              if (state.nodes[nodeIndex].metadata) {
                state.nodes[nodeIndex].metadata!.updatedAt = new Date();
                state.nodes[nodeIndex].metadata!.version += 1;
              }
            }
          }),

        removeNode: (id) =>
          set((state) => {
            state.nodes = state.nodes.filter((n) => n.id !== id);
            state.connections = state.connections.filter(
              (c) => c.sourceId !== id && c.targetId !== id
            );
            state.selectedNodes = state.selectedNodes.filter((nId) => nId !== id);
            state.metrics.nodeCount = state.nodes.length;
            state.metrics.connectionCount = state.connections.length;
          }),

        duplicateNode: (id) =>
          set((state) => {
            const node = state.nodes.find((n) => n.id === id);
            if (node) {
              const duplicatedNode: CanvasNode = {
                ...node,
                id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                title: `${node.title} (Copy)`,
                position: {
                  x: node.position.x + 50,
                  y: node.position.y + 50,
                },
                connections: [],
                metadata: {
                  createdAt: new Date(),
                  updatedAt: new Date(),
                  version: 1,
                },
              };
              state.nodes.push(duplicatedNode);
              state.metrics.nodeCount = state.nodes.length;
            }
          }),

        moveNode: (id, position) =>
          set((state) => {
            const nodeIndex = state.nodes.findIndex((n) => n.id === id);
            if (nodeIndex !== -1) {
              state.nodes[nodeIndex].position = position;
              if (state.nodes[nodeIndex].metadata) {
                state.nodes[nodeIndex].metadata!.updatedAt = new Date();
              }
            }
          }),

        // Connection Management Actions
        addConnection: (connectionData) =>
          set((state) => {
            const connection: CanvasConnection = {
              ...connectionData,
              id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            };
            state.connections.push(connection);
            state.metrics.connectionCount = state.connections.length;
          }),

        updateConnection: (id, updates) =>
          set((state) => {
            const connectionIndex = state.connections.findIndex((c) => c.id === id);
            if (connectionIndex !== -1) {
              Object.assign(state.connections[connectionIndex], updates);
            }
          }),

        removeConnection: (id) =>
          set((state) => {
            state.connections = state.connections.filter((c) => c.id !== id);
            state.selectedConnections = state.selectedConnections.filter((cId) => cId !== id);
            state.metrics.connectionCount = state.connections.length;
          }),

        // Selection Management Actions
        selectNode: (id, multi = false) =>
          set((state) => {
            if (multi) {
              if (state.selectedNodes.includes(id)) {
                state.selectedNodes = state.selectedNodes.filter((nId) => nId !== id);
              } else {
                state.selectedNodes.push(id);
              }
            } else {
              state.selectedNodes = [id];
            }
          }),

        selectConnection: (id, multi = false) =>
          set((state) => {
            if (multi) {
              if (state.selectedConnections.includes(id)) {
                state.selectedConnections = state.selectedConnections.filter((cId) => cId !== id);
              } else {
                state.selectedConnections.push(id);
              }
            } else {
              state.selectedConnections = [id];
            }
          }),

        clearSelection: () =>
          set((state) => {
            state.selectedNodes = [];
            state.selectedConnections = [];
          }),

        selectAll: () =>
          set((state) => {
            state.selectedNodes = state.nodes.map((n) => n.id);
            state.selectedConnections = state.connections.map((c) => c.id);
          }),

        // Viewport Management Actions
        setViewport: (viewport) =>
          set((state) => {
            Object.assign(state.viewport, viewport);
          }),

        zoomIn: () =>
          set((state) => {
            state.viewport.zoom = Math.min(state.viewport.zoom * 1.2, 3);
          }),

        zoomOut: () =>
          set((state) => {
            state.viewport.zoom = Math.max(state.viewport.zoom / 1.2, 0.1);
          }),

        zoomToFit: () =>
          set((state) => {
            if (state.nodes.length === 0) return;
            
            const bounds = state.nodes.reduce(
              (acc, node) => ({
                minX: Math.min(acc.minX, node.position.x),
                minY: Math.min(acc.minY, node.position.y),
                maxX: Math.max(acc.maxX, node.position.x + node.size.width),
                maxY: Math.max(acc.maxY, node.position.y + node.size.height),
              }),
              { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity }
            );
            
            const padding = 50;
            const width = bounds.maxX - bounds.minX + padding * 2;
            const height = bounds.maxY - bounds.minY + padding * 2;
            
            // Calculate zoom to fit
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            const zoom = Math.min(viewportWidth / width, viewportHeight / height, 1);
            
            state.viewport = {
              ...state.viewport,
              x: -(bounds.minX - padding),
              y: -(bounds.minY - padding),
              zoom,
            };
          }),

        resetViewport: () =>
          set((state) => {
            state.viewport = defaultViewport;
          }),

        // Workflow Management Actions
        startWorkflow: () =>
          set((state) => {
            state.isPlaying = true;
          }),

        stopWorkflow: () =>
          set((state) => {
            state.isPlaying = false;
          }),

        pauseWorkflow: () =>
          set((state) => {
            state.isPlaying = false;
          }),

        resumeWorkflow: () =>
          set((state) => {
            state.isPlaying = true;
          }),

        // History Management Actions
        undo: () =>
          set((state) => {
            if (state.history.past.length > 0) {
              const previous = state.history.past[state.history.past.length - 1];
              state.history.future.unshift(state.history.present);
              state.history.present = previous;
              state.history.past.pop();
              
              state.nodes = previous.nodes;
              state.connections = previous.connections;
            }
          }),

        redo: () =>
          set((state) => {
            if (state.history.future.length > 0) {
              const next = state.history.future[0];
              state.history.past.push(state.history.present);
              state.history.present = next;
              state.history.future.shift();
              
              state.nodes = next.nodes;
              state.connections = next.connections;
            }
          }),

        saveToHistory: () =>
          set((state) => {
            state.history.past.push(state.history.present);
            state.history.present = {
              nodes: [...state.nodes],
              connections: [...state.connections],
            };
            state.history.future = [];
            
            // Limit history size
            if (state.history.past.length > 50) {
              state.history.past.shift();
            }
          }),

        // Settings Management Actions
        updateSettings: (settings) =>
          set((state) => {
            Object.assign(state.settings, settings);
          }),

        resetSettings: () =>
          set((state) => {
            state.settings = defaultSettings;
          }),

        // Utility Actions
        exportCanvas: () => {
          const state = get();
          return JSON.stringify({
            nodes: state.nodes,
            connections: state.connections,
            viewport: state.viewport,
            settings: state.settings,
            exportedAt: new Date().toISOString(),
            version: '1.0.0',
          });
        },

        importCanvas: (data) =>
          set((state) => {
            try {
              const imported = JSON.parse(data);
              state.nodes = imported.nodes || [];
              state.connections = imported.connections || [];
              state.viewport = imported.viewport || defaultViewport;
              state.settings = { ...defaultSettings, ...imported.settings };
              state.metrics.nodeCount = state.nodes.length;
              state.metrics.connectionCount = state.connections.length;
            } catch (error) {
              console.error('Failed to import canvas data:', error);
            }
          }),

        clearCanvas: () =>
          set((state) => {
            state.nodes = [];
            state.connections = [];
            state.selectedNodes = [];
            state.selectedConnections = [];
            state.metrics.nodeCount = 0;
            state.metrics.connectionCount = 0;
          }),

        updateMetrics: (metrics) =>
          set((state) => {
            Object.assign(state.metrics, metrics);
          }),
      })),
      {
        name: 'canvas-store',
        partialize: (state) => ({
          nodes: state.nodes,
          connections: state.connections,
          viewport: state.viewport,
          settings: state.settings,
        }),
      }
    ),
    {
      name: 'canvas-store',
    }
  )
);

// Selector hooks for performance
export const useCanvasNodes = () => useCanvasStore((state) => state.nodes);
export const useCanvasConnections = () => useCanvasStore((state) => state.connections);
export const useCanvasSelection = () => useCanvasStore((state) => ({
  selectedNodes: state.selectedNodes,
  selectedConnections: state.selectedConnections,
}));
export const useCanvasViewport = () => useCanvasStore((state) => state.viewport);
export const useCanvasSettings = () => useCanvasStore((state) => state.settings);
export const useCanvasMetrics = () => useCanvasStore((state) => state.metrics);
export const useCanvasWorkflow = () => useCanvasStore((state) => ({
  isPlaying: state.isPlaying,
  startWorkflow: state.startWorkflow,
  stopWorkflow: state.stopWorkflow,
  pauseWorkflow: state.pauseWorkflow,
  resumeWorkflow: state.resumeWorkflow,
}));
