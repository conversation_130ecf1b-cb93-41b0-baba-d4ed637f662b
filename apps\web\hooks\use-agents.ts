'use client';

import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { agentsClient } from '@/lib/agents/client';
import { toast } from '@/hooks/use-toast';
import type {
  AgentContext,
  CopyAgentInput,
  DesignAgentInput,
  AnalystAgentInput,
  AgentExecutionResult,
  WorkflowState
} from '@/types/agents';

export function useAgents() {
  const [isExecuting, setIsExecuting] = useState(false);

  // Agent status query
  const { data: agentStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['agent-status'],
    queryFn: () => agentsClient.getAgentStatus(),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Health check query
  const { data: healthStatus } = useQuery({
    queryKey: ['agents-health'],
    queryFn: () => agentsClient.healthCheck(),
    refetchInterval: 60000, // Refetch every minute
  });

  // Workflow execution mutation
  const workflowMutation = useMutation({
    mutationFn: (context: AgentContext) => agentsClient.executeWorkflow(context),
    onSuccess: (result) => {
      toast({
        title: 'Workflow completed',
        description: `Execution ${result.executionId} finished with status: ${result.status}`,
      });
    },
    onError: (error) => {
      toast({
        title: 'Workflow failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    },
  });

  // Copy agent mutation
  const copyMutation = useMutation({
    mutationFn: ({ input, context }: { input: CopyAgentInput; context: AgentContext }) =>
      agentsClient.executeCopyAgent(input, context),
    onSuccess: () => {
      toast({
        title: 'Copy generated',
        description: 'AI copy generation completed successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Copy generation failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    },
  });

  // Design agent mutation
  const designMutation = useMutation({
    mutationFn: ({ input, context }: { input: DesignAgentInput; context: AgentContext }) =>
      agentsClient.executeDesignAgent(input, context),
    onSuccess: () => {
      toast({
        title: 'Design generated',
        description: 'AI design generation completed successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Design generation failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    },
  });

  // Analyst agent mutation
  const analystMutation = useMutation({
    mutationFn: ({ input, context }: { input: AnalystAgentInput; context: AgentContext }) =>
      agentsClient.executeAnalystAgent(input, context),
    onSuccess: () => {
      toast({
        title: 'Analysis completed',
        description: 'AI performance analysis completed successfully',
      });
    },
    onError: (error) => {
      toast({
        title: 'Analysis failed',
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: 'destructive',
      });
    },
  });

  // Convenience methods
  const executeWorkflow = useCallback(async (context: AgentContext): Promise<WorkflowState | null> => {
    setIsExecuting(true);
    try {
      const result = await workflowMutation.mutateAsync(context);
      return result;
    } catch (error) {
      console.error('Workflow execution failed:', error);
      return null;
    } finally {
      setIsExecuting(false);
    }
  }, [workflowMutation]);

  const generateCopy = useCallback(async (
    input: CopyAgentInput, 
    context: AgentContext
  ): Promise<AgentExecutionResult | null> => {
    try {
      const result = await copyMutation.mutateAsync({ input, context });
      return result;
    } catch (error) {
      console.error('Copy generation failed:', error);
      return null;
    }
  }, [copyMutation]);

  const generateDesign = useCallback(async (
    input: DesignAgentInput, 
    context: AgentContext
  ): Promise<AgentExecutionResult | null> => {
    try {
      const result = await designMutation.mutateAsync({ input, context });
      return result;
    } catch (error) {
      console.error('Design generation failed:', error);
      return null;
    }
  }, [designMutation]);

  const analyzePerformance = useCallback(async (
    input: AnalystAgentInput, 
    context: AgentContext
  ): Promise<AgentExecutionResult | null> => {
    try {
      const result = await analystMutation.mutateAsync({ input, context });
      return result;
    } catch (error) {
      console.error('Performance analysis failed:', error);
      return null;
    }
  }, [analystMutation]);

  return {
    // Status
    agentStatus,
    healthStatus,
    statusLoading,
    isExecuting: isExecuting || workflowMutation.isPending,
    
    // Individual agent states
    copyAgent: {
      isLoading: copyMutation.isPending,
      error: copyMutation.error,
      data: copyMutation.data,
      execute: generateCopy,
    },
    
    designAgent: {
      isLoading: designMutation.isPending,
      error: designMutation.error,
      data: designMutation.data,
      execute: generateDesign,
    },
    
    analystAgent: {
      isLoading: analystMutation.isPending,
      error: analystMutation.error,
      data: analystMutation.data,
      execute: analyzePerformance,
    },
    
    // Workflow
    workflow: {
      isLoading: workflowMutation.isPending,
      error: workflowMutation.error,
      data: workflowMutation.data,
      execute: executeWorkflow,
    },
    
    // Utility methods
    isAnyAgentBusy: () => 
      copyMutation.isPending || 
      designMutation.isPending || 
      analystMutation.isPending || 
      workflowMutation.isPending,
      
    areAgentsHealthy: () => 
      healthStatus?.status === 'healthy' && 
      agentStatus?.agents?.every((agent: any) => agent.status === 'ready'),
  };
}
