{"name": "@metamorphic-flux/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.48.0", "@tailwindcss/oxide": "^4.0.0", "@tanstack/react-query": "^5.62.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.0.0", "geist": "^1.4.2", "gsap": "^3.12.8", "lucide-react": "^0.468.0", "next": "^15.3.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.5.5", "tailwindcss": "^4.0.0", "zustand": "^5.0.2"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.81.5", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-config-next": "^15.3.0", "postcss": "^8.5.1", "typescript": "^5.7.2"}}