# 🔮 Metamorphic Flux™

> AI-powered marketing engine that dynamically shape-shifts campaign creative, channels, and spend in real-time.

## 🚀 Features

- **Real-time DCO**: < 200ms idea-to-impression latency
- **AI Agent Swarm**: 6 specialized micro-agents (Co<PERSON>, Design, Analyst, Buyer, Ethics, QA)
- **Quantum Optimization**: Budget allocation via Google Willow with classical fallback
- **Multi-channel Orchestration**: Email, push, ads via Temporal workflows
- **Vector-powered Personalization**: Supabase pgvector for audience insights

## 🛠 Tech Stack

- **Frontend**: React 19, Next.js 15.3, Tailwind CSS 4 (Oxide)
- **State Management**: Zustand + React Query 5
- **Backend**: Supabase (Postgres 16) + pgvector 0.9
- **AI**: Gemini 2.5 Flash, Imagen 4, Veo 3
- **Agents**: LangGraph v0.5.4
- **Workflows**: Temporal.io Cloud 2025
- **Infrastructure**: Vercel Edge, Cloudflare R2

## 📦 Project Structure

```
metamorphic-flux/
├── apps/
│   ├── web/                 # Next.js 15.3 frontend
│   └── agents/              # LangGraph agent swarm
├── packages/
│   ├── ui/                  # Shared UI components
│   ├── optimizer/           # Quantum budget optimizer
│   └── shared/              # Shared utilities
└── docs/                    # Documentation
```

## 🏃‍♂️ Quick Start

### Prerequisites

- Node.js 20+
- PNPM 9+
- Supabase CLI
- Docker (for local development)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/metamorphic-flux.git
   cd metamorphic-flux
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your API keys
   ```

4. **Start Supabase locally**
   ```bash
   npx supabase start
   ```

5. **Run database migrations**
   ```bash
   npx supabase db push
   ```

6. **Start development server**
   ```bash
   pnpm dev
   ```

Visit [http://localhost:3000](http://localhost:3000) to see the app.

## 🧪 Development

### Available Scripts

- `pnpm dev` - Start development servers
- `pnpm build` - Build all packages
- `pnpm test` - Run tests
- `pnpm test:e2e` - Run end-to-end tests
- `pnpm lint` - Lint code
- `pnpm type-check` - Type check

### Testing

```bash
# Unit tests
pnpm test

# E2E tests
pnpm test:e2e

# Coverage report
pnpm test:coverage
```

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect to Vercel**
   ```bash
   npx vercel
   ```

2. **Set environment variables in Vercel dashboard**

3. **Deploy**
   ```bash
   npx vercel --prod
   ```

### Manual Deployment

1. **Build the project**
   ```bash
   pnpm build
   ```

2. **Deploy to your preferred platform**

## 📊 Performance Targets

- **Real-time DCO**: < 200ms latency
- **CTR Uplift**: ≥ 25% vs static control
- **Scale**: ≥ 10M impressions/month
- **Cost**: < $200/month at launch

## 🔧 Configuration

### Supabase Setup

1. Create a new Supabase project
2. Enable pgvector extension
3. Run the provided SQL migrations
4. Configure Row Level Security

### Google AI Setup

1. Enable Gemini API in Google Cloud Console
2. Create API key with appropriate permissions
3. Configure Imagen 4 and Veo 3 access

### Temporal Setup

1. Create Temporal Cloud account
2. Configure namespace and certificates
3. Deploy workflow definitions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/metamorphic-flux)
- 📖 Docs: [docs.metamorphic-flux.com](https://docs.metamorphic-flux.com)

---

Built with ❤️ by the Metamorphic Flux team
