import { z } from 'zod';
import { BaseAgent } from './base';
import { 
  DesignAgentInputSchema, 
  AgentOutputSchema,
  type DesignAgentInput,
  type AgentOutput,
  type AgentContext,
  type AgentConfig 
} from '@/types';
import { ImageGenerationService } from '@/utils/image-generation';

export class DesignAgent extends BaseAgent {
  private imageService: ImageGenerationService;

  constructor() {
    const config: AgentConfig = {
      name: 'Design Agent',
      type: 'design',
      model: 'gemini-1.5-flash',
      temperature: 0.6,
      maxTokens: 2048,
      systemPrompt: `You are an expert creative director specializing in AI-powered visual design for marketing campaigns. Your role is to create compelling visual concepts and generate detailed prompts for image/video generation.

Key responsibilities:
- Analyze copy and campaign context to create visual concepts
- Generate detailed prompts for Imagen 4 and Veo 3
- Ensure visual consistency with brand guidelines
- Optimize designs for specific channels and formats
- Consider accessibility and user experience

Always respond with valid JSON containing:
- visual_concept: Overall creative concept and direction
- image_prompt: Detailed prompt for Imagen 4 generation
- video_prompt: Detailed prompt for Veo 3 generation (if video format)
- style_guide: Visual style recommendations
- technical_specs: Technical requirements and specifications
- accessibility_notes: Accessibility considerations
- confidence: Confidence score (0-1)
- reasoning: Explanation of design decisions`,
    };
    super(config);
    this.imageService = new ImageGenerationService();
  }

  get inputSchema() {
    return DesignAgentInputSchema;
  }

  get outputSchema() {
    return AgentOutputSchema;
  }

  protected async processInput(input: DesignAgentInput, context: AgentContext): Promise<AgentOutput> {
    const { copy, style, format, dimensions, constraints } = input;

    // Build context-aware prompt
    const campaignContext = this.formatCampaignContext(context.campaign);
    const copyContext = this.formatCopyContext(copy);
    const constraintsText = this.formatDesignConstraints(constraints);
    const personaContext = this.formatPersonaContext(context.personas);

    const userPrompt = `
Create visual design concept for the following context:

CAMPAIGN CONTEXT:
${campaignContext}

TARGET PERSONAS:
${personaContext}

COPY CONTENT:
${copyContext}

DESIGN REQUIREMENTS:
- Style: ${style}
- Format: ${format}
- Dimensions: ${dimensions.width}x${dimensions.height}px

${constraintsText}

TECHNICAL REQUIREMENTS:
- Optimize for ${context.campaign.channels?.join(', ') || 'digital channels'}
- Ensure mobile responsiveness
- Consider accessibility standards (WCAG 2.1)
- Maintain brand consistency

Please create a comprehensive visual concept and generate detailed prompts for AI image/video generation.

Respond with JSON in this exact format:
{
  "visual_concept": "Overall creative concept and direction",
  "image_prompt": "Detailed prompt for Imagen 4 generation",
  "video_prompt": "Detailed prompt for Veo 3 generation (if video)",
  "style_guide": "Visual style recommendations",
  "technical_specs": "Technical requirements and specifications",
  "accessibility_notes": "Accessibility considerations",
  "confidence": 0.85,
  "reasoning": "Explanation of design decisions"
}`;

    try {
      const response = await this.callModel(this.config.systemPrompt, userPrompt, context);
      const parsedResponse = this.parseJsonResponse(response);

      // Generate actual image if requested
      let generatedImageUrl: string | undefined;
      if (format === 'image' && parsedResponse.image_prompt) {
        try {
          generatedImageUrl = await this.imageService.generateImage({
            prompt: parsedResponse.image_prompt,
            width: dimensions.width,
            height: dimensions.height,
            style: style,
            quality: 'high',
          });
        } catch (error) {
          console.warn('Image generation failed, continuing without generated image:', error);
        }
      }

      // Calculate confidence based on various factors
      const confidence = this.calculateConfidence({
        inputQuality: this.assessInputQuality(input),
        outputCoherence: this.assessOutputCoherence(parsedResponse),
        alignmentWithObjectives: this.assessDesignAlignment(parsedResponse, context),
        technicalCorrectness: this.assessTechnicalCorrectness(parsedResponse, format),
      });

      return {
        success: true,
        data: {
          visual_concept: parsedResponse.visual_concept,
          image_prompt: parsedResponse.image_prompt,
          video_prompt: parsedResponse.video_prompt,
          style_guide: parsedResponse.style_guide,
          technical_specs: parsedResponse.technical_specs,
          accessibility_notes: parsedResponse.accessibility_notes,
          generated_image_url: generatedImageUrl,
          format,
          style,
          dimensions,
        },
        confidence,
        reasoning: parsedResponse.reasoning || 'Design concept created based on copy analysis and campaign objectives',
        suggestions: this.generateDesignSuggestions(parsedResponse, input),
        metadata: {
          copy_analyzed: copy,
          style,
          format,
          dimensions,
          constraints,
          image_generated: !!generatedImageUrl,
        },
      };

    } catch (error) {
      throw new Error(`Design generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private formatCopyContext(copy: any): string {
    const parts = [];
    if (copy.headline) parts.push(`Headline: ${copy.headline}`);
    if (copy.description) parts.push(`Description: ${copy.description}`);
    if (copy.cta) parts.push(`CTA: ${copy.cta}`);
    
    return parts.join('\n');
  }

  private formatDesignConstraints(constraints?: any): string {
    if (!constraints) return '';

    const parts = [];
    if (constraints.colorScheme) {
      parts.push(`Color scheme: ${constraints.colorScheme}`);
    }
    if (constraints.brandColors?.length) {
      parts.push(`Brand colors: ${constraints.brandColors.join(', ')}`);
    }
    if (constraints.logoUrl) {
      parts.push(`Logo URL: ${constraints.logoUrl}`);
    }

    return parts.length > 0 ? `\nDESIGN CONSTRAINTS:\n${parts.join('\n')}` : '';
  }

  private assessInputQuality(input: DesignAgentInput): number {
    let score = 0.5; // Base score

    // Check copy completeness
    if (input.copy.headline) score += 0.15;
    if (input.copy.description) score += 0.15;
    if (input.copy.cta) score += 0.1;

    // Check style specification
    if (input.style && input.style.length > 5) score += 0.1;

    // Check constraints
    if (input.constraints) score += 0.1;

    return Math.min(score, 1);
  }

  private assessOutputCoherence(output: any): number {
    let score = 0.5;

    // Check if required fields are present and non-empty
    const requiredFields = ['visual_concept', 'image_prompt', 'reasoning'];
    requiredFields.forEach(field => {
      if (output[field] && typeof output[field] === 'string' && output[field].length > 20) {
        score += 0.15;
      }
    });

    // Check for technical fields
    if (output.technical_specs) score += 0.1;
    if (output.accessibility_notes) score += 0.1;

    return Math.min(score, 1);
  }

  private assessDesignAlignment(output: any, context: AgentContext): number {
    let score = 0.5;

    // Check if design concept mentions campaign objectives
    const conceptLower = output.visual_concept?.toLowerCase() || '';
    const objectivesText = JSON.stringify(context.campaign.objectives || {}).toLowerCase();

    if (conceptLower.includes('brand') || conceptLower.includes('awareness')) score += 0.2;
    if (conceptLower.includes('conversion') || conceptLower.includes('action')) score += 0.2;
    if (conceptLower.includes('engagement') || conceptLower.includes('interaction')) score += 0.1;

    return Math.min(score, 1);
  }

  private assessTechnicalCorrectness(output: any, format: string): number {
    let score = 0.5;

    // Check format-specific requirements
    if (format === 'image' && output.image_prompt) score += 0.3;
    if (format === 'video' && output.video_prompt) score += 0.3;
    if (output.technical_specs) score += 0.2;

    return Math.min(score, 1);
  }

  private generateDesignSuggestions(output: any, input: DesignAgentInput): string[] {
    const suggestions: string[] = [];

    // Suggest A/B test variations
    suggestions.push('Consider creating A/B test variations with different color schemes');

    // Format-specific suggestions
    if (input.format === 'video') {
      suggestions.push('Consider adding motion graphics to enhance engagement');
    }

    // Channel-specific suggestions
    if (input.dimensions.width === input.dimensions.height) {
      suggestions.push('Square format detected - optimize for Instagram and Facebook feeds');
    }

    return suggestions;
  }
}
