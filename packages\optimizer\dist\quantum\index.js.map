{"version": 3, "sources": ["../../src/quantum/index.ts", "../../src/quantum/willow-bridge.ts", "../../src/types/index.ts", "../../src/classical/solver.ts", "../../src/quantum/optimizer.ts"], "sourcesContent": ["/**\n * Quantum optimization module\n * \n * Provides quantum-enhanced budget optimization using Google Willow\n * quantum processor with advanced error correction and coherence.\n */\n\nimport { QuantumOptimizer } from './optimizer';\nimport { WillowBridge } from './willow-bridge';\n\nexport { QuantumOptimizer, WillowBridge };\n\nexport type {\n  WillowConfig,\n  QuantumCircuitConfig,\n} from '../types';\n\nexport {\n  QuantumOptimizationError,\n  WillowAPIError,\n} from '../types';\n\n// Quantum algorithm implementations\nexport const QUANTUM_ALGORITHMS = {\n  VQE: 'Variational Quantum Eigensolver',\n  QAOA: 'Quantum Approximate Optimization Algorithm',\n  QUANTUM_ANNEALING: 'Quantum Annealing',\n  GROVER: 'Grover Search Algorithm',\n  SHOR: 'Shor Factoring Algorithm',\n} as const;\n\n// Willow-specific constants\nexport const WILLOW_SPECS = {\n  MAX_QUBITS: 105,\n  COHERENCE_TIME: 100, // microseconds\n  ERROR_RATE: 0.001,\n  GATE_TIME: 0.1, // microseconds\n  READOUT_FIDELITY: 0.999,\n  TWO_QUBIT_GATE_FIDELITY: 0.995,\n} as const;\n\nconst defaultExport = {\n  QuantumOptimizer,\n  WillowBridge,\n  QUANTUM_ALGORITHMS,\n  WILLOW_SPECS,\n};\n\nexport default defaultExport;\n", "import axios, { AxiosInstance, AxiosResponse } from 'axios';\nimport { WillowConfig, QuantumCircuitConfig, WillowAPIError, OptimizationRequest } from '../types';\n\n/**\n * Google Willow Quantum Chip REST API Bridge\n * \n * This bridge interfaces with Google's Willow quantum computing platform\n * to perform quantum-enhanced budget optimization calculations.\n * \n * Willow Features:\n * - 105 qubits with breakthrough error correction\n * - Sub-millisecond coherence times\n * - Real-time error correction below threshold\n * - Quantum advantage for optimization problems\n */\nexport class WillowBridge {\n  private client: AxiosInstance;\n  private config: WillowConfig;\n  private circuitCache: Map<string, any> = new Map();\n\n  constructor(config: WillowConfig) {\n    this.config = config;\n    this.client = axios.create({\n      baseURL: config.endpoint,\n      timeout: config.timeout,\n      headers: {\n        'Authorization': `Bearer ${config.apiKey}`,\n        'Content-Type': 'application/json',\n        'X-Goog-User-Project': config.projectId,\n        'X-Quantum-Engine': 'willow-v1',\n      },\n    });\n\n    this.setupInterceptors();\n  }\n\n  private setupInterceptors(): void {\n    // Request interceptor for logging and validation\n    this.client.interceptors.request.use(\n      (config) => {\n        console.log(`[Willow] ${config.method?.toUpperCase()} ${config.url}`);\n        return config;\n      },\n      (error) => Promise.reject(error)\n    );\n\n    // Response interceptor for error handling\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        const willowError = new WillowAPIError(\n          error.response?.data?.message || error.message,\n          error.response?.status || 500,\n          error.response?.data\n        );\n        return Promise.reject(willowError);\n      }\n    );\n  }\n\n  /**\n   * Check Willow quantum processor availability and status\n   */\n  async getProcessorStatus(): Promise<{\n    available: boolean;\n    qubits: number;\n    coherenceTime: number;\n    errorRate: number;\n    queueLength: number;\n    estimatedWaitTime: number;\n  }> {\n    try {\n      const response = await this.client.get('/v1/processors/willow/status');\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to get processor status',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Create a quantum circuit for budget optimization\n   */\n  async createOptimizationCircuit(\n    request: OptimizationRequest,\n    circuitConfig: QuantumCircuitConfig\n  ): Promise<string> {\n    const circuitKey = this.generateCircuitKey(request, circuitConfig);\n    \n    if (this.circuitCache.has(circuitKey)) {\n      return this.circuitCache.get(circuitKey);\n    }\n\n    const circuit = this.buildQuantumCircuit(request, circuitConfig);\n    \n    try {\n      const response = await this.client.post('/v1/circuits', {\n        name: `budget-optimization-${request.id}`,\n        circuit: circuit,\n        config: circuitConfig,\n        metadata: {\n          channels: request.channels.length,\n          constraints: request.constraints.length,\n          totalBudget: request.totalBudget,\n          objective: request.objective.primary,\n        },\n      });\n\n      const circuitId = response.data.circuitId;\n      this.circuitCache.set(circuitKey, circuitId);\n      return circuitId;\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to create quantum circuit',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Execute quantum optimization on Willow processor\n   */\n  async executeOptimization(\n    circuitId: string,\n    parameters: Record<string, number>\n  ): Promise<{\n    result: number[];\n    confidence: number;\n    quantumAdvantage: number;\n    metrics: {\n      gateCount: number;\n      depth: number;\n      errorRate: number;\n      coherenceTime: number;\n      executionTime: number;\n    };\n  }> {\n    try {\n      const response = await this.client.post(`/v1/circuits/${circuitId}/execute`, {\n        parameters,\n        shots: 10000, // Number of quantum measurements\n        errorCorrection: this.config.circuitConfig.errorCorrection,\n        optimization: {\n          level: 'aggressive',\n          errorMitigation: true,\n          readoutCorrection: true,\n        },\n      });\n\n      return this.processQuantumResult(response.data);\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to execute quantum optimization',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Run variational quantum eigensolver (VQE) for optimization\n   */\n  async runVQE(\n    hamiltonian: number[][],\n    initialParameters: number[],\n    maxIterations: number = 100\n  ): Promise<{\n    eigenvalue: number;\n    eigenvector: number[];\n    parameters: number[];\n    iterations: number;\n    convergence: boolean;\n  }> {\n    try {\n      const response = await this.client.post('/v1/algorithms/vqe', {\n        hamiltonian,\n        initialParameters,\n        maxIterations,\n        convergenceThreshold: 1e-6,\n        optimizer: 'COBYLA',\n        ansatz: 'hardware_efficient',\n      });\n\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'VQE execution failed',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Run Quantum Approximate Optimization Algorithm (QAOA)\n   */\n  async runQAOA(\n    costFunction: number[][],\n    mixingHamiltonian: number[][],\n    layers: number = 3\n  ): Promise<{\n    optimalParameters: number[];\n    expectationValue: number;\n    probability: number[];\n    iterations: number;\n  }> {\n    try {\n      const response = await this.client.post('/v1/algorithms/qaoa', {\n        costFunction,\n        mixingHamiltonian,\n        layers,\n        optimizer: 'NELDER_MEAD',\n        shots: 8192,\n      });\n\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'QAOA execution failed',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Get quantum processor metrics and utilization\n   */\n  async getProcessorMetrics(): Promise<{\n    utilization: number;\n    queueLength: number;\n    averageJobTime: number;\n    errorRates: Record<string, number>;\n    calibrationStatus: string;\n    temperature: number;\n  }> {\n    try {\n      const response = await this.client.get('/v1/processors/willow/metrics');\n      return response.data;\n    } catch (error) {\n      throw new WillowAPIError(\n        'Failed to get processor metrics',\n        500,\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Build quantum circuit for budget optimization problem\n   */\n  private buildQuantumCircuit(\n    request: OptimizationRequest,\n    config: QuantumCircuitConfig\n  ): any {\n    const numChannels = request.channels.length;\n    const numQubits = Math.min(numChannels * 4, config.qubits); // 4 qubits per channel\n    \n    const circuit = {\n      qubits: numQubits,\n      gates: [] as any[],\n      measurements: [] as any[],\n    };\n\n    // Initialize superposition\n    for (let i = 0; i < numQubits; i++) {\n      circuit.gates.push({ type: 'H', qubit: i });\n    }\n\n    // Encode budget constraints as quantum gates\n    this.encodeConstraints(circuit, request.constraints, numChannels);\n    \n    // Encode optimization objective\n    this.encodeObjective(circuit, request.objective, numChannels);\n    \n    // Add entanglement for channel correlations\n    this.addEntanglement(circuit, numChannels);\n    \n    // Add measurements\n    for (let i = 0; i < numQubits; i++) {\n      circuit.measurements.push({ qubit: i, classical_bit: i });\n    }\n\n    return circuit;\n  }\n\n  private encodeConstraints(circuit: any, constraints: any[], numChannels: number): void {\n    constraints.forEach((constraint, idx) => {\n      if (constraint.type === 'total_budget') {\n        // Encode total budget constraint using controlled rotations\n        for (let i = 0; i < numChannels; i++) {\n          const angle = (constraint.value / 1000000) * Math.PI; // Normalize\n          circuit.gates.push({\n            type: 'RY',\n            qubit: i * 4,\n            parameter: angle,\n          });\n        }\n      }\n    });\n  }\n\n  private encodeObjective(circuit: any, objective: any, numChannels: number): void {\n    // Encode optimization objective as quantum phase rotations\n    const weight = objective.weights.primary;\n    \n    for (let i = 0; i < numChannels; i++) {\n      const phase = weight * Math.PI / 2;\n      circuit.gates.push({\n        type: 'RZ',\n        qubit: i * 4 + 1,\n        parameter: phase,\n      });\n    }\n  }\n\n  private addEntanglement(circuit: any, numChannels: number): void {\n    // Add CNOT gates for channel correlations\n    for (let i = 0; i < numChannels - 1; i++) {\n      circuit.gates.push({\n        type: 'CNOT',\n        control: i * 4,\n        target: (i + 1) * 4,\n      });\n    }\n  }\n\n  private processQuantumResult(rawResult: any): any {\n    const measurements = rawResult.measurements;\n    const counts = rawResult.counts;\n    \n    // Convert quantum measurements to budget allocations\n    const result = this.decodeMeasurements(measurements, counts);\n    \n    // Calculate quantum advantage metric\n    const quantumAdvantage = this.calculateQuantumAdvantage(rawResult);\n    \n    return {\n      result: result.allocations,\n      confidence: result.confidence,\n      quantumAdvantage,\n      metrics: {\n        gateCount: rawResult.circuitMetrics.gateCount,\n        depth: rawResult.circuitMetrics.depth,\n        errorRate: rawResult.errorMetrics.averageErrorRate,\n        coherenceTime: rawResult.coherenceMetrics.averageCoherenceTime,\n        executionTime: rawResult.timing.executionTime,\n      },\n    };\n  }\n\n  private decodeMeasurements(measurements: any, counts: any): any {\n    // Decode quantum measurements into budget allocations\n    const totalShots = Object.values(counts).reduce((a: number, b: any) => a + Number(b), 0);\n    const allocations: number[] = [];\n    let confidence = 0;\n\n    // Find most probable measurement outcome\n    let maxCount: number = 0;\n    let bestOutcome = '';\n\n    for (const [outcome, count] of Object.entries(counts)) {\n      const countNum = Number(count);\n      if (countNum > maxCount) {\n        maxCount = countNum;\n        bestOutcome = outcome;\n      }\n    }\n\n    confidence = maxCount / totalShots;\n    \n    // Convert binary outcome to budget allocations\n    const binaryString = bestOutcome;\n    const numChannels = binaryString.length / 4;\n    \n    for (let i = 0; i < numChannels; i++) {\n      const channelBits = binaryString.slice(i * 4, (i + 1) * 4);\n      const allocation = parseInt(channelBits, 2) / 15; // Normalize to [0,1]\n      allocations.push(allocation);\n    }\n\n    return { allocations, confidence };\n  }\n\n  private calculateQuantumAdvantage(result: any): number {\n    // Calculate quantum advantage based on coherence and error rates\n    const coherenceTime = result.coherenceMetrics.averageCoherenceTime;\n    const errorRate = result.errorMetrics.averageErrorRate;\n    const gateTime = result.timing.averageGateTime;\n    \n    // Quantum advantage when coherence time >> gate time and error rate is low\n    const coherenceAdvantage = Math.min(coherenceTime / (gateTime * 100), 1);\n    const errorAdvantage = Math.max(0, 1 - errorRate * 100);\n    \n    return (coherenceAdvantage + errorAdvantage) / 2;\n  }\n\n  private generateCircuitKey(request: OptimizationRequest, config: QuantumCircuitConfig): string {\n    const keyData = {\n      channels: request.channels.length,\n      constraints: request.constraints.length,\n      objective: request.objective.primary,\n      qubits: config.qubits,\n      depth: config.depth,\n    };\n    \n    return Buffer.from(JSON.stringify(keyData)).toString('base64');\n  }\n\n  /**\n   * Clean up resources and close connections\n   */\n  async cleanup(): Promise<void> {\n    this.circuitCache.clear();\n    // Additional cleanup if needed\n  }\n}\n", "import { z } from 'zod';\n\n// Budget Constraint Types\nexport const BudgetConstraintSchema = z.object({\n  id: z.string(),\n  type: z.enum(['total_budget', 'channel_budget', 'daily_budget', 'cpa_target', 'roas_target']),\n  value: z.number().positive(),\n  operator: z.enum(['<=', '>=', '=', '<', '>']),\n  priority: z.number().min(1).max(10).default(5),\n  flexible: z.boolean().default(false),\n  tolerance: z.number().min(0).max(1).default(0.1),\n});\n\nexport type BudgetConstraint = z.infer<typeof BudgetConstraintSchema>;\n\n// Channel Configuration\nexport const ChannelConfigSchema = z.object({\n  id: z.string(),\n  name: z.string(),\n  type: z.enum(['facebook', 'google', 'linkedin', 'twitter', 'tiktok', 'email', 'sms']),\n  minBudget: z.number().min(0),\n  maxBudget: z.number().positive(),\n  costModel: z.enum(['cpc', 'cpm', 'cpa', 'cpv', 'flat']),\n  baseRate: z.number().positive(),\n  scalingFactor: z.number().positive().default(1.0),\n  saturationPoint: z.number().positive().optional(),\n  conversionRate: z.number().min(0).max(1),\n  averageOrderValue: z.number().positive(),\n  seasonality: z.array(z.number()).length(12).default(Array(12).fill(1.0)),\n  competitiveIndex: z.number().min(0).max(10).default(5),\n});\n\nexport type ChannelConfig = z.infer<typeof ChannelConfigSchema>;\n\n// Optimization Objective\nexport const OptimizationObjectiveSchema = z.object({\n  primary: z.enum(['maximize_revenue', 'maximize_conversions', 'minimize_cpa', 'maximize_roas']),\n  secondary: z.enum(['maximize_reach', 'minimize_cost', 'maximize_engagement']).optional(),\n  weights: z.object({\n    primary: z.number().min(0).max(1).default(0.8),\n    secondary: z.number().min(0).max(1).default(0.2),\n  }),\n  timeHorizon: z.enum(['daily', 'weekly', 'monthly', 'quarterly']).default('monthly'),\n});\n\nexport type OptimizationObjective = z.infer<typeof OptimizationObjectiveSchema>;\n\n// Budget Allocation Result\nexport const BudgetAllocationSchema = z.object({\n  channelId: z.string(),\n  allocatedBudget: z.number().min(0),\n  expectedConversions: z.number().min(0),\n  expectedRevenue: z.number().min(0),\n  expectedCPA: z.number().positive(),\n  expectedROAS: z.number().positive(),\n  confidence: z.number().min(0).max(1),\n  riskScore: z.number().min(0).max(10),\n});\n\nexport type BudgetAllocation = z.infer<typeof BudgetAllocationSchema>;\n\n// Optimization Request\nexport const OptimizationRequestSchema = z.object({\n  id: z.string(),\n  totalBudget: z.number().positive(),\n  channels: z.array(ChannelConfigSchema),\n  constraints: z.array(BudgetConstraintSchema),\n  objective: OptimizationObjectiveSchema,\n  timeframe: z.object({\n    start: z.date(),\n    end: z.date(),\n  }),\n  historicalData: z.object({\n    channelPerformance: z.record(z.string(), z.array(z.object({\n      date: z.date(),\n      spend: z.number().min(0),\n      conversions: z.number().min(0),\n      revenue: z.number().min(0),\n    }))),\n    marketConditions: z.array(z.object({\n      date: z.date(),\n      competitiveIndex: z.number().min(0).max(10),\n      seasonalityFactor: z.number().positive(),\n      economicIndicator: z.number(),\n    })),\n  }).optional(),\n  preferences: z.object({\n    useQuantumOptimization: z.boolean().default(true),\n    quantumFallbackThreshold: z.number().min(0).max(1).default(0.95),\n    maxIterations: z.number().positive().default(1000),\n    convergenceThreshold: z.number().positive().default(0.001),\n    riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).default('moderate'),\n  }).default({}),\n});\n\nexport type OptimizationRequest = z.infer<typeof OptimizationRequestSchema>;\n\n// Optimization Result\nexport const OptimizationResultSchema = z.object({\n  id: z.string(),\n  requestId: z.string(),\n  status: z.enum(['success', 'partial', 'failed']),\n  method: z.enum(['quantum', 'classical', 'hybrid']),\n  allocations: z.array(BudgetAllocationSchema),\n  totalAllocated: z.number().min(0),\n  expectedTotalRevenue: z.number().min(0),\n  expectedTotalConversions: z.number().min(0),\n  overallROAS: z.number().positive(),\n  overallCPA: z.number().positive(),\n  confidence: z.number().min(0).max(1),\n  riskScore: z.number().min(0).max(10),\n  optimizationMetrics: z.object({\n    iterations: z.number().positive(),\n    convergenceTime: z.number().positive(),\n    objectiveValue: z.number(),\n    constraintViolations: z.array(z.object({\n      constraintId: z.string(),\n      violation: z.number(),\n      severity: z.enum(['low', 'medium', 'high']),\n    })),\n  }),\n  quantumMetrics: z.object({\n    quantumAdvantage: z.number().min(0).max(1).optional(),\n    coherenceTime: z.number().positive().optional(),\n    gateCount: z.number().positive().optional(),\n    errorRate: z.number().min(0).max(1).optional(),\n    willowChipUtilization: z.number().min(0).max(1).optional(),\n  }).optional(),\n  recommendations: z.array(z.object({\n    type: z.enum(['budget_increase', 'channel_rebalance', 'constraint_relaxation', 'timing_adjustment']),\n    description: z.string(),\n    impact: z.object({\n      revenueChange: z.number(),\n      costChange: z.number(),\n      riskChange: z.number(),\n    }),\n    priority: z.enum(['low', 'medium', 'high']),\n  })),\n  createdAt: z.date(),\n  computeTime: z.number().positive(),\n});\n\nexport type OptimizationResult = z.infer<typeof OptimizationResultSchema>;\n\n// Quantum Circuit Configuration\nexport const QuantumCircuitConfigSchema = z.object({\n  qubits: z.number().positive().max(105), // Willow chip limit\n  depth: z.number().positive(),\n  gateSet: z.array(z.enum(['H', 'X', 'Y', 'Z', 'CNOT', 'CZ', 'RX', 'RY', 'RZ', 'SWAP'])),\n  errorCorrection: z.boolean().default(true),\n  coherenceTime: z.number().positive().default(100), // microseconds\n  fidelity: z.number().min(0).max(1).default(0.999),\n});\n\nexport type QuantumCircuitConfig = z.infer<typeof QuantumCircuitConfigSchema>;\n\n// Willow API Configuration\nexport const WillowConfigSchema = z.object({\n  endpoint: z.string().url(),\n  apiKey: z.string(),\n  projectId: z.string(),\n  region: z.enum(['us-central1', 'europe-west1', 'asia-east1']).default('us-central1'),\n  timeout: z.number().positive().default(30000),\n  retries: z.number().min(0).max(5).default(3),\n  circuitConfig: QuantumCircuitConfigSchema,\n});\n\nexport type WillowConfig = z.infer<typeof WillowConfigSchema>;\n\n// Error Types\nexport class QuantumOptimizationError extends Error {\n  constructor(\n    message: string,\n    public code: string,\n    public details?: Record<string, any>\n  ) {\n    super(message);\n    this.name = 'QuantumOptimizationError';\n  }\n}\n\nexport class WillowAPIError extends Error {\n  constructor(\n    message: string,\n    public statusCode: number,\n    public response?: any\n  ) {\n    super(message);\n    this.name = 'WillowAPIError';\n  }\n}\n\nexport class ClassicalOptimizationError extends Error {\n  constructor(\n    message: string,\n    public algorithm: string,\n    public details?: Record<string, any>\n  ) {\n    super(message);\n    this.name = 'ClassicalOptimizationError';\n  }\n}\n\n// Utility Types\nexport type Matrix = number[][];\nexport type Vector = number[];\n\nexport interface OptimizationMetrics {\n  objectiveValue: number;\n  constraintViolations: number;\n  feasibility: boolean;\n  optimality: number;\n  convergenceRate: number;\n}\n\nexport interface PerformanceMetrics {\n  computeTime: number;\n  memoryUsage: number;\n  iterations: number;\n  accuracy: number;\n  stability: number;\n}\n\n// Export all schemas for validation\nexport const Schemas = {\n  BudgetConstraint: BudgetConstraintSchema,\n  ChannelConfig: ChannelConfigSchema,\n  OptimizationObjective: OptimizationObjectiveSchema,\n  BudgetAllocation: BudgetAllocationSchema,\n  OptimizationRequest: OptimizationRequestSchema,\n  OptimizationResult: OptimizationResultSchema,\n  QuantumCircuitConfig: QuantumCircuitConfigSchema,\n  WillowConfig: WillowConfigSchema,\n} as const;\n", "import { Matrix } from 'ml-matrix';\nimport { evaluate } from 'mathjs';\nimport { cloneDeep, sum, maxBy, minBy } from 'lodash';\nimport {\n  OptimizationRequest,\n  OptimizationResult,\n  BudgetAllocation,\n  ClassicalOptimizationError,\n  OptimizationMetrics,\n  PerformanceMetrics,\n} from '../types';\n\n/**\n * Classical Budget Optimization Solver\n * \n * Implements multiple classical optimization algorithms as fallback\n * when quantum optimization is unavailable or suboptimal.\n * \n * Algorithms:\n * - Sequential Quadratic Programming (SQP)\n * - Genetic Algorithm (GA)\n * - Particle Swarm Optimization (PSO)\n * - Simulated Annealing (SA)\n * - Interior Point Method\n */\nexport class ClassicalSolver {\n  private maxIterations: number;\n  private convergenceThreshold: number;\n  private populationSize: number;\n\n  constructor(options: {\n    maxIterations?: number;\n    convergenceThreshold?: number;\n    populationSize?: number;\n  } = {}) {\n    this.maxIterations = options.maxIterations || 1000;\n    this.convergenceThreshold = options.convergenceThreshold || 1e-6;\n    this.populationSize = options.populationSize || 100;\n  }\n\n  /**\n   * Main optimization entry point\n   */\n  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Choose best algorithm based on problem characteristics\n      const algorithm = this.selectAlgorithm(request);\n      console.log(`[Classical] Using ${algorithm} algorithm`);\n\n      let result: OptimizationResult;\n      \n      switch (algorithm) {\n        case 'sqp':\n          result = await this.sequentialQuadraticProgramming(request);\n          break;\n        case 'genetic':\n          result = await this.geneticAlgorithm(request);\n          break;\n        case 'pso':\n          result = await this.particleSwarmOptimization(request);\n          break;\n        case 'simulated_annealing':\n          result = await this.simulatedAnnealing(request);\n          break;\n        case 'interior_point':\n          result = await this.interiorPointMethod(request);\n          break;\n        default:\n          throw new ClassicalOptimizationError(\n            `Unknown algorithm: ${algorithm}`,\n            algorithm\n          );\n      }\n\n      result.computeTime = Date.now() - startTime;\n      result.method = 'classical';\n      \n      return result;\n    } catch (error) {\n      throw new ClassicalOptimizationError(\n        `Classical optimization failed: ${error.message}`,\n        'unknown',\n        { originalError: error }\n      );\n    }\n  }\n\n  /**\n   * Sequential Quadratic Programming (SQP)\n   * Best for smooth, differentiable problems with equality/inequality constraints\n   */\n  private async sequentialQuadraticProgramming(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    let x = this.getInitialSolution(request); // Initial budget allocation\n    let iteration = 0;\n    let converged = false;\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: 0,\n      constraintViolations: 0,\n      feasibility: false,\n      optimality: 0,\n      convergenceRate: 0,\n    };\n\n    while (iteration < this.maxIterations && !converged) {\n      // Calculate gradient and Hessian\n      const gradient = this.calculateGradient(x, request);\n      const hessian = this.calculateHessian(x, request);\n      \n      // Solve QP subproblem\n      const direction = this.solveQPSubproblem(gradient, hessian, x, request);\n      \n      // Line search\n      const stepSize = this.lineSearch(x, direction, request);\n      \n      // Update solution\n      const newX = x.map((xi, i) => xi + stepSize * direction[i]);\n      \n      // Check convergence\n      const change = this.calculateNorm(newX.map((xi, i) => xi - x[i]));\n      converged = change < this.convergenceThreshold;\n      \n      x = newX;\n      iteration++;\n      \n      // Update metrics\n      metrics.objectiveValue = this.evaluateObjective(x, request);\n      metrics.constraintViolations = this.evaluateConstraintViolations(x, request);\n    }\n\n    metrics.feasibility = metrics.constraintViolations < 1e-6;\n    metrics.optimality = converged ? 1.0 : 0.5;\n    metrics.convergenceRate = iteration / this.maxIterations;\n\n    return this.buildResult(request, x, metrics, iteration, 'sqp');\n  }\n\n  /**\n   * Genetic Algorithm\n   * Best for discrete, combinatorial problems with complex constraints\n   */\n  private async geneticAlgorithm(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    let population = this.initializePopulation(request, this.populationSize);\n    let generation = 0;\n    let bestSolution = population[0];\n    let bestFitness = this.evaluateFitness(bestSolution, request);\n\n    while (generation < this.maxIterations) {\n      // Evaluate fitness for all individuals\n      const fitness = population.map(individual => this.evaluateFitness(individual, request));\n      \n      // Find best individual\n      const maxFitnessIndex = fitness.indexOf(Math.max(...fitness));\n      if (fitness[maxFitnessIndex] > bestFitness) {\n        bestSolution = cloneDeep(population[maxFitnessIndex]);\n        bestFitness = fitness[maxFitnessIndex];\n      }\n\n      // Selection (tournament selection)\n      const parents = this.tournamentSelection(population, fitness, this.populationSize);\n      \n      // Crossover and mutation\n      const offspring = this.crossoverAndMutation(parents, request);\n      \n      // Replace population\n      population = offspring;\n      generation++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(bestSolution, request),\n      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),\n      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,\n      optimality: 0.8, // GA doesn't guarantee global optimum\n      convergenceRate: generation / this.maxIterations,\n    };\n\n    return this.buildResult(request, bestSolution, metrics, generation, 'genetic');\n  }\n\n  /**\n   * Particle Swarm Optimization\n   * Best for continuous optimization with multiple local optima\n   */\n  private async particleSwarmOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    const numParticles = this.populationSize;\n    \n    // Initialize particles\n    const particles = Array(numParticles).fill(null).map(() => ({\n      position: this.getInitialSolution(request),\n      velocity: Array(numChannels).fill(0).map(() => (Math.random() - 0.5) * 0.1),\n      bestPosition: [] as number[],\n      bestFitness: -Infinity,\n    }));\n\n    let globalBestPosition = particles[0].position;\n    let globalBestFitness = -Infinity;\n    let iteration = 0;\n\n    // PSO parameters\n    const w = 0.7; // Inertia weight\n    const c1 = 1.5; // Cognitive parameter\n    const c2 = 1.5; // Social parameter\n\n    while (iteration < this.maxIterations) {\n      for (const particle of particles) {\n        const fitness = this.evaluateFitness(particle.position, request);\n        \n        // Update personal best\n        if (fitness > particle.bestFitness) {\n          particle.bestPosition = cloneDeep(particle.position);\n          particle.bestFitness = fitness;\n        }\n        \n        // Update global best\n        if (fitness > globalBestFitness) {\n          globalBestPosition = cloneDeep(particle.position);\n          globalBestFitness = fitness;\n        }\n        \n        // Update velocity and position\n        for (let i = 0; i < numChannels; i++) {\n          const r1 = Math.random();\n          const r2 = Math.random();\n          \n          particle.velocity[i] = w * particle.velocity[i] +\n            c1 * r1 * (particle.bestPosition[i] - particle.position[i]) +\n            c2 * r2 * (globalBestPosition[i] - particle.position[i]);\n          \n          particle.position[i] += particle.velocity[i];\n          \n          // Ensure bounds\n          particle.position[i] = Math.max(0, Math.min(1, particle.position[i]));\n        }\n      }\n      \n      iteration++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(globalBestPosition, request),\n      constraintViolations: this.evaluateConstraintViolations(globalBestPosition, request),\n      feasibility: this.evaluateConstraintViolations(globalBestPosition, request) < 1e-6,\n      optimality: 0.85,\n      convergenceRate: iteration / this.maxIterations,\n    };\n\n    return this.buildResult(request, globalBestPosition, metrics, iteration, 'pso');\n  }\n\n  /**\n   * Simulated Annealing\n   * Best for discrete optimization with many local optima\n   */\n  private async simulatedAnnealing(request: OptimizationRequest): Promise<OptimizationResult> {\n    let currentSolution = this.getInitialSolution(request);\n    let currentEnergy = -this.evaluateFitness(currentSolution, request);\n    let bestSolution = cloneDeep(currentSolution);\n    let bestEnergy = currentEnergy;\n    \n    const initialTemp = 1000;\n    const finalTemp = 0.01;\n    const coolingRate = 0.95;\n    let temperature = initialTemp;\n    let iteration = 0;\n\n    while (temperature > finalTemp && iteration < this.maxIterations) {\n      // Generate neighbor solution\n      const neighbor = this.generateNeighbor(currentSolution, request);\n      const neighborEnergy = -this.evaluateFitness(neighbor, request);\n      \n      // Accept or reject neighbor\n      const deltaE = neighborEnergy - currentEnergy;\n      if (deltaE < 0 || Math.random() < Math.exp(-deltaE / temperature)) {\n        currentSolution = neighbor;\n        currentEnergy = neighborEnergy;\n        \n        if (currentEnergy < bestEnergy) {\n          bestSolution = cloneDeep(currentSolution);\n          bestEnergy = currentEnergy;\n        }\n      }\n      \n      temperature *= coolingRate;\n      iteration++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(bestSolution, request),\n      constraintViolations: this.evaluateConstraintViolations(bestSolution, request),\n      feasibility: this.evaluateConstraintViolations(bestSolution, request) < 1e-6,\n      optimality: 0.75,\n      convergenceRate: iteration / this.maxIterations,\n    };\n\n    return this.buildResult(request, bestSolution, metrics, iteration, 'simulated_annealing');\n  }\n\n  /**\n   * Interior Point Method\n   * Best for convex optimization with inequality constraints\n   */\n  private async interiorPointMethod(request: OptimizationRequest): Promise<OptimizationResult> {\n    const numChannels = request.channels.length;\n    let x = this.getInitialSolution(request);\n    let mu = 1.0; // Barrier parameter\n    let iteration = 0;\n    let converged = false;\n\n    while (iteration < this.maxIterations && !converged && mu > 1e-8) {\n      // Solve barrier subproblem\n      const result = this.solveBarrierSubproblem(x, mu, request);\n      x = result.solution;\n      converged = result.converged;\n      \n      // Update barrier parameter\n      mu *= 0.1;\n      iteration++;\n    }\n\n    const metrics: OptimizationMetrics = {\n      objectiveValue: this.evaluateObjective(x, request),\n      constraintViolations: this.evaluateConstraintViolations(x, request),\n      feasibility: this.evaluateConstraintViolations(x, request) < 1e-6,\n      optimality: converged ? 0.95 : 0.7,\n      convergenceRate: iteration / this.maxIterations,\n    };\n\n    return this.buildResult(request, x, metrics, iteration, 'interior_point');\n  }\n\n  // Helper methods...\n  private selectAlgorithm(request: OptimizationRequest): string {\n    const numChannels = request.channels.length;\n    const numConstraints = request.constraints.length;\n    const hasNonlinearConstraints = request.constraints.some(c => \n      c.type === 'roas_target' || c.type === 'cpa_target'\n    );\n\n    if (numChannels <= 5 && !hasNonlinearConstraints) {\n      return 'sqp'; // Small, smooth problems\n    } else if (numChannels > 20 || hasNonlinearConstraints) {\n      return 'genetic'; // Large or complex problems\n    } else if (request.objective.primary === 'maximize_roas') {\n      return 'pso'; // Continuous optimization\n    } else {\n      return 'interior_point'; // General case\n    }\n  }\n\n  private getInitialSolution(request: OptimizationRequest): number[] {\n    // Equal allocation as starting point\n    const numChannels = request.channels.length;\n    return Array(numChannels).fill(1 / numChannels);\n  }\n\n  private evaluateObjective(allocation: number[], request: OptimizationRequest): number {\n    let objective = 0;\n    const totalBudget = request.totalBudget;\n\n    allocation.forEach((weight, i) => {\n      const channel = request.channels[i];\n      const budget = weight * totalBudget;\n      \n      // Calculate expected revenue based on channel model\n      const conversions = this.calculateConversions(budget, channel);\n      const revenue = conversions * channel.averageOrderValue;\n      \n      switch (request.objective.primary) {\n        case 'maximize_revenue':\n          objective += revenue;\n          break;\n        case 'maximize_conversions':\n          objective += conversions;\n          break;\n        case 'minimize_cpa':\n          objective -= budget / Math.max(conversions, 1);\n          break;\n        case 'maximize_roas':\n          objective += revenue / Math.max(budget, 1);\n          break;\n      }\n    });\n\n    return objective;\n  }\n\n  private evaluateFitness(allocation: number[], request: OptimizationRequest): number {\n    const objective = this.evaluateObjective(allocation, request);\n    const violations = this.evaluateConstraintViolations(allocation, request);\n    \n    // Penalize constraint violations\n    return objective - 1000 * violations;\n  }\n\n  private evaluateConstraintViolations(allocation: number[], request: OptimizationRequest): number {\n    let violations = 0;\n    const totalBudget = request.totalBudget;\n\n    // Budget allocation must sum to 1\n    const allocationSum = sum(allocation);\n    violations += Math.abs(allocationSum - 1);\n\n    // Check individual constraints\n    request.constraints.forEach(constraint => {\n      switch (constraint.type) {\n        case 'total_budget':\n          const totalSpend = allocationSum * totalBudget;\n          if (constraint.operator === '<=' && totalSpend > constraint.value) {\n            violations += (totalSpend - constraint.value) / constraint.value;\n          }\n          break;\n        // Add other constraint types...\n      }\n    });\n\n    return violations;\n  }\n\n  private calculateConversions(budget: number, channel: any): number {\n    // Simplified conversion model with diminishing returns\n    const baseConversions = budget * channel.conversionRate / channel.baseRate;\n    const saturationFactor = channel.saturationPoint ? \n      Math.min(1, channel.saturationPoint / budget) : 1;\n    \n    return baseConversions * saturationFactor * channel.scalingFactor;\n  }\n\n  private buildResult(\n    request: OptimizationRequest,\n    allocation: number[],\n    metrics: OptimizationMetrics,\n    iterations: number,\n    algorithm: string\n  ): OptimizationResult {\n    const allocations: BudgetAllocation[] = allocation.map((weight, i) => {\n      const channel = request.channels[i];\n      const budget = weight * request.totalBudget;\n      const conversions = this.calculateConversions(budget, channel);\n      const revenue = conversions * channel.averageOrderValue;\n\n      return {\n        channelId: channel.id,\n        allocatedBudget: budget,\n        expectedConversions: conversions,\n        expectedRevenue: revenue,\n        expectedCPA: budget / Math.max(conversions, 1),\n        expectedROAS: revenue / Math.max(budget, 1),\n        confidence: 0.8, // Classical methods have good confidence\n        riskScore: 3, // Moderate risk\n      };\n    });\n\n    return {\n      id: `result_${Date.now()}`,\n      requestId: request.id,\n      status: metrics.feasibility ? 'success' : 'partial',\n      method: 'classical',\n      allocations,\n      totalAllocated: sum(allocations.map(a => a.allocatedBudget)),\n      expectedTotalRevenue: sum(allocations.map(a => a.expectedRevenue)),\n      expectedTotalConversions: sum(allocations.map(a => a.expectedConversions)),\n      overallROAS: sum(allocations.map(a => a.expectedRevenue)) / \n                   Math.max(sum(allocations.map(a => a.allocatedBudget)), 1),\n      overallCPA: sum(allocations.map(a => a.allocatedBudget)) / \n                  Math.max(sum(allocations.map(a => a.expectedConversions)), 1),\n      confidence: metrics.optimality,\n      riskScore: metrics.feasibility ? 2 : 6,\n      optimizationMetrics: {\n        iterations,\n        convergenceTime: 0, // Will be set by caller\n        objectiveValue: metrics.objectiveValue,\n        constraintViolations: [],\n      },\n      recommendations: [],\n      createdAt: new Date(),\n      computeTime: 0, // Will be set by caller\n    };\n  }\n\n  // Additional helper methods for specific algorithms...\n  private calculateGradient(x: number[], request: OptimizationRequest): number[] {\n    const h = 1e-8;\n    const gradient = new Array(x.length);\n    \n    for (let i = 0; i < x.length; i++) {\n      const xPlus = [...x];\n      const xMinus = [...x];\n      xPlus[i] += h;\n      xMinus[i] -= h;\n      \n      gradient[i] = (this.evaluateObjective(xPlus, request) - \n                    this.evaluateObjective(xMinus, request)) / (2 * h);\n    }\n    \n    return gradient;\n  }\n\n  private calculateHessian(x: number[], request: OptimizationRequest): number[][] {\n    const h = 1e-6;\n    const n = x.length;\n    const hessian = Array(n).fill(null).map(() => Array(n).fill(0));\n    \n    for (let i = 0; i < n; i++) {\n      for (let j = 0; j < n; j++) {\n        const xPP = [...x]; xPP[i] += h; xPP[j] += h;\n        const xPM = [...x]; xPM[i] += h; xPM[j] -= h;\n        const xMP = [...x]; xMP[i] -= h; xMP[j] += h;\n        const xMM = [...x]; xMM[i] -= h; xMM[j] -= h;\n        \n        hessian[i][j] = (\n          this.evaluateObjective(xPP, request) -\n          this.evaluateObjective(xPM, request) -\n          this.evaluateObjective(xMP, request) +\n          this.evaluateObjective(xMM, request)\n        ) / (4 * h * h);\n      }\n    }\n    \n    return hessian;\n  }\n\n  private solveQPSubproblem(gradient: number[], hessian: number[][], x: number[], request: OptimizationRequest): number[] {\n    // Simplified QP solver - in practice would use specialized library\n    const n = x.length;\n    const direction = new Array(n).fill(0);\n    \n    // Simple steepest descent direction\n    for (let i = 0; i < n; i++) {\n      direction[i] = -gradient[i];\n    }\n    \n    return direction;\n  }\n\n  private lineSearch(x: number[], direction: number[], request: OptimizationRequest): number {\n    let alpha = 1.0;\n    const c1 = 1e-4; // Armijo condition parameter\n    const rho = 0.5; // Backtracking parameter\n    \n    const f0 = this.evaluateObjective(x, request);\n    const grad0 = this.calculateGradient(x, request);\n    const directionalDerivative = sum(grad0.map((g, i) => g * direction[i]));\n    \n    while (alpha > 1e-8) {\n      const newX = x.map((xi, i) => xi + alpha * direction[i]);\n      const f1 = this.evaluateObjective(newX, request);\n      \n      if (f1 >= f0 + c1 * alpha * directionalDerivative) {\n        return alpha;\n      }\n      \n      alpha *= rho;\n    }\n    \n    return alpha;\n  }\n\n  private calculateNorm(vector: number[]): number {\n    return Math.sqrt(sum(vector.map(v => v * v)));\n  }\n\n  private initializePopulation(request: OptimizationRequest, size: number): number[][] {\n    const numChannels = request.channels.length;\n    const population: number[][] = [];\n    \n    for (let i = 0; i < size; i++) {\n      const individual = Array(numChannels).fill(0).map(() => Math.random());\n      const total = sum(individual);\n      // Normalize to sum to 1\n      population.push(individual.map(x => x / total));\n    }\n    \n    return population;\n  }\n\n  private tournamentSelection(population: number[][], fitness: number[], size: number): number[][] {\n    const selected: number[][] = [];\n    const tournamentSize = 3;\n    \n    for (let i = 0; i < size; i++) {\n      let best = Math.floor(Math.random() * population.length);\n      \n      for (let j = 1; j < tournamentSize; j++) {\n        const candidate = Math.floor(Math.random() * population.length);\n        if (fitness[candidate] > fitness[best]) {\n          best = candidate;\n        }\n      }\n      \n      selected.push(cloneDeep(population[best]));\n    }\n    \n    return selected;\n  }\n\n  private crossoverAndMutation(parents: number[][], request: OptimizationRequest): number[][] {\n    const offspring: number[][] = [];\n    const mutationRate = 0.1;\n    const crossoverRate = 0.8;\n    \n    for (let i = 0; i < parents.length; i += 2) {\n      let child1 = cloneDeep(parents[i]);\n      let child2 = cloneDeep(parents[i + 1] || parents[i]);\n      \n      // Crossover\n      if (Math.random() < crossoverRate) {\n        const crossoverPoint = Math.floor(Math.random() * child1.length);\n        for (let j = crossoverPoint; j < child1.length; j++) {\n          [child1[j], child2[j]] = [child2[j], child1[j]];\n        }\n      }\n      \n      // Mutation\n      if (Math.random() < mutationRate) {\n        const mutationPoint = Math.floor(Math.random() * child1.length);\n        child1[mutationPoint] += (Math.random() - 0.5) * 0.1;\n        child1[mutationPoint] = Math.max(0, child1[mutationPoint]);\n      }\n      \n      // Normalize\n      const total1 = sum(child1);\n      const total2 = sum(child2);\n      child1 = child1.map(x => x / total1);\n      child2 = child2.map(x => x / total2);\n      \n      offspring.push(child1, child2);\n    }\n    \n    return offspring.slice(0, parents.length);\n  }\n\n  private generateNeighbor(solution: number[], request: OptimizationRequest): number[] {\n    const neighbor = cloneDeep(solution);\n    const perturbationSize = 0.05;\n    \n    // Randomly perturb one or two elements\n    const numPerturbations = Math.random() < 0.5 ? 1 : 2;\n    \n    for (let i = 0; i < numPerturbations; i++) {\n      const index = Math.floor(Math.random() * neighbor.length);\n      neighbor[index] += (Math.random() - 0.5) * perturbationSize;\n      neighbor[index] = Math.max(0, neighbor[index]);\n    }\n    \n    // Normalize\n    const total = sum(neighbor);\n    return neighbor.map(x => x / total);\n  }\n\n  private solveBarrierSubproblem(x: number[], mu: number, request: OptimizationRequest): { solution: number[]; converged: boolean } {\n    // Simplified barrier method implementation\n    let currentX = cloneDeep(x);\n    let converged = false;\n    const maxInnerIterations = 50;\n    \n    for (let iter = 0; iter < maxInnerIterations; iter++) {\n      const gradient = this.calculateBarrierGradient(currentX, mu, request);\n      const stepSize = this.lineSearch(currentX, gradient.map(g => -g), request);\n      \n      const newX = currentX.map((xi, i) => xi - stepSize * gradient[i]);\n      \n      // Ensure feasibility\n      const total = sum(newX);\n      const normalizedX = newX.map(xi => Math.max(1e-8, xi / total));\n      \n      const change = this.calculateNorm(normalizedX.map((xi, i) => xi - currentX[i]));\n      if (change < this.convergenceThreshold) {\n        converged = true;\n        break;\n      }\n      \n      currentX = normalizedX;\n    }\n    \n    return { solution: currentX, converged };\n  }\n\n  private calculateBarrierGradient(x: number[], mu: number, request: OptimizationRequest): number[] {\n    const objectiveGradient = this.calculateGradient(x, request);\n    const barrierGradient = new Array(x.length);\n    \n    // Add barrier terms for bound constraints\n    for (let i = 0; i < x.length; i++) {\n      barrierGradient[i] = objectiveGradient[i] - mu / Math.max(x[i], 1e-8);\n    }\n    \n    return barrierGradient;\n  }\n}\n", "import { WillowBridge } from './willow-bridge';\nimport { ClassicalSolver } from '../classical/solver';\nimport {\n  OptimizationRequest,\n  OptimizationResult,\n  WillowConfig,\n  QuantumOptimizationError,\n  BudgetAllocation,\n} from '../types';\n\n/**\n * Quantum Budget Optimizer\n * \n * Main orchestrator that attempts quantum optimization first,\n * then falls back to classical methods if needed.\n * \n * Features:\n * - Automatic quantum/classical selection\n * - Hybrid optimization strategies\n * - Performance monitoring and comparison\n * - Adaptive algorithm selection\n */\nexport class QuantumOptimizer {\n  private willowBridge: WillowBridge;\n  private classicalSolver: ClassicalSolver;\n  private config: WillowConfig;\n  private performanceHistory: Map<string, number[]> = new Map();\n\n  constructor(config: WillowConfig) {\n    this.config = config;\n    this.willowBridge = new WillowBridge(config);\n    this.classicalSolver = new ClassicalSolver({\n      maxIterations: 1000,\n      convergenceThreshold: 1e-6,\n      populationSize: 100,\n    });\n  }\n\n  /**\n   * Main optimization entry point\n   */\n  async optimize(request: OptimizationRequest): Promise<OptimizationResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Determine optimization strategy\n      const strategy = await this.selectOptimizationStrategy(request);\n      console.log(`[Quantum] Using ${strategy} optimization strategy`);\n\n      let result: OptimizationResult;\n\n      switch (strategy) {\n        case 'quantum':\n          result = await this.quantumOptimization(request);\n          break;\n        case 'classical':\n          result = await this.classicalOptimization(request);\n          break;\n        case 'hybrid':\n          result = await this.hybridOptimization(request);\n          break;\n        default:\n          throw new QuantumOptimizationError(\n            `Unknown optimization strategy: ${strategy}`,\n            'INVALID_STRATEGY'\n          );\n      }\n\n      // Update performance history\n      this.updatePerformanceHistory(strategy, result.computeTime);\n\n      // Add quantum-specific recommendations\n      result.recommendations.push(...this.generateQuantumRecommendations(result));\n\n      return result;\n    } catch (error) {\n      console.error('[Quantum] Optimization failed, falling back to classical:', error);\n      \n      // Emergency fallback to classical\n      const fallbackResult = await this.classicalOptimization(request);\n      fallbackResult.status = 'partial';\n      fallbackResult.recommendations.unshift({\n        type: 'budget_increase',\n        description: 'Quantum optimization failed. Consider upgrading to ensure quantum advantage.',\n        impact: {\n          revenueChange: 0,\n          costChange: 0,\n          riskChange: 1,\n        },\n        priority: 'medium',\n      });\n\n      return fallbackResult;\n    }\n  }\n\n  /**\n   * Pure quantum optimization using Willow\n   */\n  private async quantumOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    console.log('[Quantum] Starting quantum optimization on Willow');\n\n    // Check processor availability\n    const status = await this.willowBridge.getProcessorStatus();\n    if (!status.available) {\n      throw new QuantumOptimizationError(\n        'Willow processor not available',\n        'PROCESSOR_UNAVAILABLE',\n        { status }\n      );\n    }\n\n    // Create quantum circuit\n    const circuitId = await this.willowBridge.createOptimizationCircuit(\n      request,\n      this.config.circuitConfig\n    );\n\n    // Prepare quantum parameters\n    const parameters = this.prepareQuantumParameters(request);\n\n    // Execute quantum optimization\n    const quantumResult = await this.willowBridge.executeOptimization(circuitId, parameters);\n\n    // Convert quantum result to budget allocations\n    const allocations = this.convertQuantumToBudgetAllocations(\n      quantumResult.result,\n      request\n    );\n\n    // Build optimization result\n    const result: OptimizationResult = {\n      id: `quantum_${Date.now()}`,\n      requestId: request.id,\n      status: 'success',\n      method: 'quantum',\n      allocations,\n      totalAllocated: allocations.reduce((sum, a) => sum + a.allocatedBudget, 0),\n      expectedTotalRevenue: allocations.reduce((sum, a) => sum + a.expectedRevenue, 0),\n      expectedTotalConversions: allocations.reduce((sum, a) => sum + a.expectedConversions, 0),\n      overallROAS: 0, // Will be calculated\n      overallCPA: 0, // Will be calculated\n      confidence: quantumResult.confidence,\n      riskScore: this.calculateQuantumRiskScore(quantumResult),\n      optimizationMetrics: {\n        iterations: 1, // Quantum is single-shot\n        convergenceTime: quantumResult.metrics.executionTime,\n        objectiveValue: this.calculateObjectiveValue(allocations, request),\n        constraintViolations: [],\n      },\n      quantumMetrics: {\n        quantumAdvantage: quantumResult.quantumAdvantage,\n        coherenceTime: quantumResult.metrics.coherenceTime,\n        gateCount: quantumResult.metrics.gateCount,\n        errorRate: quantumResult.metrics.errorRate,\n        willowChipUtilization: status.qubits / 105, // Willow has 105 qubits\n      },\n      recommendations: [],\n      createdAt: new Date(),\n      computeTime: quantumResult.metrics.executionTime,\n    };\n\n    // Calculate derived metrics\n    result.overallROAS = result.expectedTotalRevenue / Math.max(result.totalAllocated, 1);\n    result.overallCPA = result.totalAllocated / Math.max(result.expectedTotalConversions, 1);\n\n    return result;\n  }\n\n  /**\n   * Classical optimization fallback\n   */\n  private async classicalOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    console.log('[Quantum] Using classical optimization fallback');\n    return await this.classicalSolver.optimize(request);\n  }\n\n  /**\n   * Hybrid quantum-classical optimization\n   */\n  private async hybridOptimization(request: OptimizationRequest): Promise<OptimizationResult> {\n    console.log('[Quantum] Starting hybrid optimization');\n\n    try {\n      // Start with quantum optimization for global exploration\n      const quantumResult = await this.quantumOptimization(request);\n\n      // Use quantum result as starting point for classical refinement\n      const refinedRequest = this.createRefinementRequest(request, quantumResult);\n      const classicalResult = await this.classicalSolver.optimize(refinedRequest);\n\n      // Combine results\n      const hybridResult = this.combineResults(quantumResult, classicalResult);\n      hybridResult.method = 'hybrid';\n\n      return hybridResult;\n    } catch (error) {\n      console.warn('[Quantum] Hybrid optimization failed, using classical only:', error);\n      return await this.classicalOptimization(request);\n    }\n  }\n\n  /**\n   * Select the best optimization strategy based on problem characteristics\n   */\n  private async selectOptimizationStrategy(request: OptimizationRequest): Promise<'quantum' | 'classical' | 'hybrid'> {\n    const numChannels = request.channels.length;\n    const numConstraints = request.constraints.length;\n    const totalBudget = request.totalBudget;\n\n    // Check if quantum optimization is preferred\n    if (!request.preferences.useQuantumOptimization) {\n      return 'classical';\n    }\n\n    // Check Willow availability\n    try {\n      const status = await this.willowBridge.getProcessorStatus();\n      if (!status.available || status.queueLength > 10) {\n        return 'classical';\n      }\n\n      // Quantum advantage criteria\n      const quantumAdvantageScore = this.calculateQuantumAdvantageScore(request);\n      \n      if (quantumAdvantageScore > 0.8) {\n        return 'quantum';\n      } else if (quantumAdvantageScore > 0.5) {\n        return 'hybrid';\n      } else {\n        return 'classical';\n      }\n    } catch (error) {\n      console.warn('[Quantum] Cannot access Willow, using classical:', error);\n      return 'classical';\n    }\n  }\n\n  /**\n   * Calculate quantum advantage score for the problem\n   */\n  private calculateQuantumAdvantageScore(request: OptimizationRequest): number {\n    let score = 0;\n\n    // Problem size factor (quantum advantage increases with problem size)\n    const numChannels = request.channels.length;\n    if (numChannels >= 10) score += 0.3;\n    if (numChannels >= 20) score += 0.2;\n\n    // Constraint complexity (quantum handles complex constraints better)\n    const complexConstraints = request.constraints.filter(c => \n      c.type === 'roas_target' || c.type === 'cpa_target'\n    ).length;\n    score += Math.min(complexConstraints * 0.15, 0.3);\n\n    // Optimization objective (some objectives benefit more from quantum)\n    if (request.objective.primary === 'maximize_roas') score += 0.2;\n    if (request.objective.secondary) score += 0.1;\n\n    // Historical performance\n    const quantumHistory = this.performanceHistory.get('quantum') || [];\n    const classicalHistory = this.performanceHistory.get('classical') || [];\n    \n    if (quantumHistory.length > 0 && classicalHistory.length > 0) {\n      const avgQuantumTime = quantumHistory.reduce((a, b) => a + b) / quantumHistory.length;\n      const avgClassicalTime = classicalHistory.reduce((a, b) => a + b) / classicalHistory.length;\n      \n      if (avgQuantumTime < avgClassicalTime) score += 0.2;\n    }\n\n    return Math.min(score, 1.0);\n  }\n\n  /**\n   * Prepare parameters for quantum circuit\n   */\n  private prepareQuantumParameters(request: OptimizationRequest): Record<string, number> {\n    const parameters: Record<string, number> = {};\n\n    // Budget constraints\n    parameters.totalBudget = request.totalBudget / 1000000; // Normalize\n    \n    // Channel parameters\n    request.channels.forEach((channel, i) => {\n      parameters[`channel_${i}_min`] = channel.minBudget / request.totalBudget;\n      parameters[`channel_${i}_max`] = channel.maxBudget / request.totalBudget;\n      parameters[`channel_${i}_conversion_rate`] = channel.conversionRate;\n      parameters[`channel_${i}_aov`] = channel.averageOrderValue / 1000; // Normalize\n    });\n\n    // Objective weights\n    parameters.primary_weight = request.objective.weights.primary;\n    parameters.secondary_weight = request.objective.weights.secondary || 0;\n\n    return parameters;\n  }\n\n  /**\n   * Convert quantum measurement results to budget allocations\n   */\n  private convertQuantumToBudgetAllocations(\n    quantumResult: number[],\n    request: OptimizationRequest\n  ): BudgetAllocation[] {\n    const allocations: BudgetAllocation[] = [];\n    const totalBudget = request.totalBudget;\n\n    // Normalize quantum results to valid budget allocations\n    const sum = quantumResult.reduce((a, b) => a + b, 0);\n    const normalizedWeights = quantumResult.map(w => w / sum);\n\n    request.channels.forEach((channel, i) => {\n      const weight = normalizedWeights[i] || 0;\n      const budget = weight * totalBudget;\n      \n      // Ensure budget is within channel constraints\n      const constrainedBudget = Math.max(\n        channel.minBudget,\n        Math.min(channel.maxBudget, budget)\n      );\n\n      // Calculate expected performance\n      const conversions = this.calculateExpectedConversions(constrainedBudget, channel);\n      const revenue = conversions * channel.averageOrderValue;\n\n      allocations.push({\n        channelId: channel.id,\n        allocatedBudget: constrainedBudget,\n        expectedConversions: conversions,\n        expectedRevenue: revenue,\n        expectedCPA: constrainedBudget / Math.max(conversions, 1),\n        expectedROAS: revenue / Math.max(constrainedBudget, 1),\n        confidence: 0.9, // Quantum typically has high confidence\n        riskScore: 2, // Quantum optimization is generally low risk\n      });\n    });\n\n    return allocations;\n  }\n\n  /**\n   * Calculate expected conversions for a channel given budget\n   */\n  private calculateExpectedConversions(budget: number, channel: any): number {\n    // Simplified model with diminishing returns\n    const baseConversions = budget * channel.conversionRate / channel.baseRate;\n    \n    // Apply saturation curve\n    let saturationFactor = 1;\n    if (channel.saturationPoint && budget > channel.saturationPoint) {\n      saturationFactor = Math.sqrt(channel.saturationPoint / budget);\n    }\n    \n    return baseConversions * saturationFactor * channel.scalingFactor;\n  }\n\n  /**\n   * Calculate quantum-specific risk score\n   */\n  private calculateQuantumRiskScore(quantumResult: any): number {\n    let riskScore = 2; // Base low risk for quantum\n\n    // Increase risk if error rate is high\n    if (quantumResult.metrics.errorRate > 0.01) {\n      riskScore += 2;\n    }\n\n    // Increase risk if coherence time is low\n    if (quantumResult.metrics.coherenceTime < 50) {\n      riskScore += 1;\n    }\n\n    // Decrease risk if quantum advantage is high\n    if (quantumResult.quantumAdvantage > 0.8) {\n      riskScore -= 1;\n    }\n\n    return Math.max(1, Math.min(10, riskScore));\n  }\n\n  /**\n   * Calculate objective value for allocations\n   */\n  private calculateObjectiveValue(allocations: BudgetAllocation[], request: OptimizationRequest): number {\n    let value = 0;\n\n    switch (request.objective.primary) {\n      case 'maximize_revenue':\n        value = allocations.reduce((sum, a) => sum + a.expectedRevenue, 0);\n        break;\n      case 'maximize_conversions':\n        value = allocations.reduce((sum, a) => sum + a.expectedConversions, 0);\n        break;\n      case 'minimize_cpa':\n        const totalCPA = allocations.reduce((sum, a) => sum + a.expectedCPA, 0) / allocations.length;\n        value = -totalCPA; // Negative because we want to minimize\n        break;\n      case 'maximize_roas':\n        const totalROAS = allocations.reduce((sum, a) => sum + a.expectedROAS, 0) / allocations.length;\n        value = totalROAS;\n        break;\n    }\n\n    return value;\n  }\n\n  /**\n   * Generate quantum-specific recommendations\n   */\n  private generateQuantumRecommendations(result: OptimizationResult): any[] {\n    const recommendations: any[] = [];\n\n    // Quantum advantage recommendation\n    if (result.quantumMetrics?.quantumAdvantage && result.quantumMetrics.quantumAdvantage > 0.8) {\n      recommendations.push({\n        type: 'budget_increase',\n        description: 'High quantum advantage detected. Consider increasing budget to maximize quantum benefits.',\n        impact: {\n          revenueChange: result.expectedTotalRevenue * 0.15,\n          costChange: result.totalAllocated * 0.1,\n          riskChange: -0.5,\n        },\n        priority: 'high',\n      });\n    }\n\n    // Error rate recommendation\n    if (result.quantumMetrics?.errorRate && result.quantumMetrics.errorRate > 0.01) {\n      recommendations.push({\n        type: 'timing_adjustment',\n        description: 'Higher than optimal quantum error rate. Consider running optimization during off-peak hours.',\n        impact: {\n          revenueChange: result.expectedTotalRevenue * 0.05,\n          costChange: 0,\n          riskChange: -1,\n        },\n        priority: 'medium',\n      });\n    }\n\n    return recommendations;\n  }\n\n  /**\n   * Create refinement request for hybrid optimization\n   */\n  private createRefinementRequest(original: OptimizationRequest, quantumResult: OptimizationResult): OptimizationRequest {\n    // Use quantum result to create tighter constraints for classical refinement\n    const refinedRequest = { ...original };\n    \n    // Add budget allocation constraints based on quantum result\n    quantumResult.allocations.forEach((allocation, i) => {\n      const tolerance = 0.2; // 20% tolerance around quantum solution\n      const minBudget = allocation.allocatedBudget * (1 - tolerance);\n      const maxBudget = allocation.allocatedBudget * (1 + tolerance);\n      \n      refinedRequest.constraints.push({\n        id: `quantum_constraint_${i}`,\n        type: 'channel_budget',\n        value: maxBudget,\n        operator: '<=',\n        priority: 7,\n        flexible: true,\n        tolerance: 0.1,\n      });\n    });\n\n    return refinedRequest;\n  }\n\n  /**\n   * Combine quantum and classical results\n   */\n  private combineResults(quantumResult: OptimizationResult, classicalResult: OptimizationResult): OptimizationResult {\n    // Use quantum result as base and incorporate classical improvements\n    const combined = { ...quantumResult };\n    \n    // Take the better objective value\n    if (classicalResult.optimizationMetrics.objectiveValue > quantumResult.optimizationMetrics.objectiveValue) {\n      combined.allocations = classicalResult.allocations;\n      combined.totalAllocated = classicalResult.totalAllocated;\n      combined.expectedTotalRevenue = classicalResult.expectedTotalRevenue;\n      combined.expectedTotalConversions = classicalResult.expectedTotalConversions;\n      combined.overallROAS = classicalResult.overallROAS;\n      combined.overallCPA = classicalResult.overallCPA;\n    }\n\n    // Combine confidence scores\n    combined.confidence = (quantumResult.confidence + classicalResult.confidence) / 2;\n    \n    // Take minimum risk score\n    combined.riskScore = Math.min(quantumResult.riskScore, classicalResult.riskScore);\n\n    return combined;\n  }\n\n  /**\n   * Update performance history for algorithm selection\n   */\n  private updatePerformanceHistory(strategy: string, computeTime: number): void {\n    if (!this.performanceHistory.has(strategy)) {\n      this.performanceHistory.set(strategy, []);\n    }\n    \n    const history = this.performanceHistory.get(strategy)!;\n    history.push(computeTime);\n    \n    // Keep only last 10 results\n    if (history.length > 10) {\n      history.shift();\n    }\n  }\n\n  /**\n   * Clean up resources\n   */\n  async cleanup(): Promise<void> {\n    await this.willowBridge.cleanup();\n    this.performanceHistory.clear();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,mBAAoD;;;ACApD,iBAAkB;AAGX,IAAM,yBAAyB,aAAE,OAAO;AAAA,EAC7C,IAAI,aAAE,OAAO;AAAA,EACb,MAAM,aAAE,KAAK,CAAC,gBAAgB,kBAAkB,gBAAgB,cAAc,aAAa,CAAC;AAAA,EAC5F,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,UAAU,aAAE,KAAK,CAAC,MAAM,MAAM,KAAK,KAAK,GAAG,CAAC;AAAA,EAC5C,UAAU,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC;AAAA,EAC7C,UAAU,aAAE,QAAQ,EAAE,QAAQ,KAAK;AAAA,EACnC,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;AACjD,CAAC;AAKM,IAAM,sBAAsB,aAAE,OAAO;AAAA,EAC1C,IAAI,aAAE,OAAO;AAAA,EACb,MAAM,aAAE,OAAO;AAAA,EACf,MAAM,aAAE,KAAK,CAAC,YAAY,UAAU,YAAY,WAAW,UAAU,SAAS,KAAK,CAAC;AAAA,EACpF,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAC3B,WAAW,aAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,WAAW,aAAE,KAAK,CAAC,OAAO,OAAO,OAAO,OAAO,MAAM,CAAC;AAAA,EACtD,UAAU,aAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAG;AAAA,EAChD,iBAAiB,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,EAChD,gBAAgB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,EACvC,mBAAmB,aAAE,OAAO,EAAE,SAAS;AAAA,EACvC,aAAa,aAAE,MAAM,aAAE,OAAO,CAAC,EAAE,OAAO,EAAE,EAAE,QAAQ,MAAM,EAAE,EAAE,KAAK,CAAG,CAAC;AAAA,EACvE,kBAAkB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC;AACvD,CAAC;AAKM,IAAM,8BAA8B,aAAE,OAAO;AAAA,EAClD,SAAS,aAAE,KAAK,CAAC,oBAAoB,wBAAwB,gBAAgB,eAAe,CAAC;AAAA,EAC7F,WAAW,aAAE,KAAK,CAAC,kBAAkB,iBAAiB,qBAAqB,CAAC,EAAE,SAAS;AAAA,EACvF,SAAS,aAAE,OAAO;AAAA,IAChB,SAAS,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;AAAA,IAC7C,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG;AAAA,EACjD,CAAC;AAAA,EACD,aAAa,aAAE,KAAK,CAAC,SAAS,UAAU,WAAW,WAAW,CAAC,EAAE,QAAQ,SAAS;AACpF,CAAC;AAKM,IAAM,yBAAyB,aAAE,OAAO;AAAA,EAC7C,WAAW,aAAE,OAAO;AAAA,EACpB,iBAAiB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACjC,qBAAqB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACrC,iBAAiB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACjC,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,EACjC,cAAc,aAAE,OAAO,EAAE,SAAS;AAAA,EAClC,YAAY,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,EACnC,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AACrC,CAAC;AAKM,IAAM,4BAA4B,aAAE,OAAO;AAAA,EAChD,IAAI,aAAE,OAAO;AAAA,EACb,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,EACjC,UAAU,aAAE,MAAM,mBAAmB;AAAA,EACrC,aAAa,aAAE,MAAM,sBAAsB;AAAA,EAC3C,WAAW;AAAA,EACX,WAAW,aAAE,OAAO;AAAA,IAClB,OAAO,aAAE,KAAK;AAAA,IACd,KAAK,aAAE,KAAK;AAAA,EACd,CAAC;AAAA,EACD,gBAAgB,aAAE,OAAO;AAAA,IACvB,oBAAoB,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,MAAM,aAAE,OAAO;AAAA,MACxD,MAAM,aAAE,KAAK;AAAA,MACb,OAAO,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MACvB,aAAa,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,MAC7B,SAAS,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,IAC3B,CAAC,CAAC,CAAC;AAAA,IACH,kBAAkB,aAAE,MAAM,aAAE,OAAO;AAAA,MACjC,MAAM,aAAE,KAAK;AAAA,MACb,kBAAkB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAAA,MAC1C,mBAAmB,aAAE,OAAO,EAAE,SAAS;AAAA,MACvC,mBAAmB,aAAE,OAAO;AAAA,IAC9B,CAAC,CAAC;AAAA,EACJ,CAAC,EAAE,SAAS;AAAA,EACZ,aAAa,aAAE,OAAO;AAAA,IACpB,wBAAwB,aAAE,QAAQ,EAAE,QAAQ,IAAI;AAAA,IAChD,0BAA0B,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,IAAI;AAAA,IAC/D,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,GAAI;AAAA,IACjD,sBAAsB,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,IAAK;AAAA,IACzD,eAAe,aAAE,KAAK,CAAC,gBAAgB,YAAY,YAAY,CAAC,EAAE,QAAQ,UAAU;AAAA,EACtF,CAAC,EAAE,QAAQ,CAAC,CAAC;AACf,CAAC;AAKM,IAAM,2BAA2B,aAAE,OAAO;AAAA,EAC/C,IAAI,aAAE,OAAO;AAAA,EACb,WAAW,aAAE,OAAO;AAAA,EACpB,QAAQ,aAAE,KAAK,CAAC,WAAW,WAAW,QAAQ,CAAC;AAAA,EAC/C,QAAQ,aAAE,KAAK,CAAC,WAAW,aAAa,QAAQ,CAAC;AAAA,EACjD,aAAa,aAAE,MAAM,sBAAsB;AAAA,EAC3C,gBAAgB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAChC,sBAAsB,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACtC,0BAA0B,aAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAC1C,aAAa,aAAE,OAAO,EAAE,SAAS;AAAA,EACjC,YAAY,aAAE,OAAO,EAAE,SAAS;AAAA,EAChC,YAAY,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,EACnC,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;AAAA,EACnC,qBAAqB,aAAE,OAAO;AAAA,IAC5B,YAAY,aAAE,OAAO,EAAE,SAAS;AAAA,IAChC,iBAAiB,aAAE,OAAO,EAAE,SAAS;AAAA,IACrC,gBAAgB,aAAE,OAAO;AAAA,IACzB,sBAAsB,aAAE,MAAM,aAAE,OAAO;AAAA,MACrC,cAAc,aAAE,OAAO;AAAA,MACvB,WAAW,aAAE,OAAO;AAAA,MACpB,UAAU,aAAE,KAAK,CAAC,OAAO,UAAU,MAAM,CAAC;AAAA,IAC5C,CAAC,CAAC;AAAA,EACJ,CAAC;AAAA,EACD,gBAAgB,aAAE,OAAO;AAAA,IACvB,kBAAkB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,IACpD,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,IAC9C,WAAW,aAAE,OAAO,EAAE,SAAS,EAAE,SAAS;AAAA,IAC1C,WAAW,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,IAC7C,uBAAuB,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAAA,EAC3D,CAAC,EAAE,SAAS;AAAA,EACZ,iBAAiB,aAAE,MAAM,aAAE,OAAO;AAAA,IAChC,MAAM,aAAE,KAAK,CAAC,mBAAmB,qBAAqB,yBAAyB,mBAAmB,CAAC;AAAA,IACnG,aAAa,aAAE,OAAO;AAAA,IACtB,QAAQ,aAAE,OAAO;AAAA,MACf,eAAe,aAAE,OAAO;AAAA,MACxB,YAAY,aAAE,OAAO;AAAA,MACrB,YAAY,aAAE,OAAO;AAAA,IACvB,CAAC;AAAA,IACD,UAAU,aAAE,KAAK,CAAC,OAAO,UAAU,MAAM,CAAC;AAAA,EAC5C,CAAC,CAAC;AAAA,EACF,WAAW,aAAE,KAAK;AAAA,EAClB,aAAa,aAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAKM,IAAM,6BAA6B,aAAE,OAAO;AAAA,EACjD,QAAQ,aAAE,OAAO,EAAE,SAAS,EAAE,IAAI,GAAG;AAAA;AAAA,EACrC,OAAO,aAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,SAAS,aAAE,MAAM,aAAE,KAAK,CAAC,KAAK,KAAK,KAAK,KAAK,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,CAAC,CAAC;AAAA,EACrF,iBAAiB,aAAE,QAAQ,EAAE,QAAQ,IAAI;AAAA,EACzC,eAAe,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,GAAG;AAAA;AAAA,EAChD,UAAU,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,KAAK;AAClD,CAAC;AAKM,IAAM,qBAAqB,aAAE,OAAO;AAAA,EACzC,UAAU,aAAE,OAAO,EAAE,IAAI;AAAA,EACzB,QAAQ,aAAE,OAAO;AAAA,EACjB,WAAW,aAAE,OAAO;AAAA,EACpB,QAAQ,aAAE,KAAK,CAAC,eAAe,gBAAgB,YAAY,CAAC,EAAE,QAAQ,aAAa;AAAA,EACnF,SAAS,aAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,GAAK;AAAA,EAC5C,SAAS,aAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,QAAQ,CAAC;AAAA,EAC3C,eAAe;AACjB,CAAC;AAKM,IAAM,2BAAN,cAAuC,MAAM;AAAA,EAClD,YACE,SACO,MACA,SACP;AACA,UAAM,OAAO;AAHN;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AACF;AAEO,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACxC,YACE,SACO,YACA,UACP;AACA,UAAM,OAAO;AAHN;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AACF;AAEO,IAAM,6BAAN,cAAyC,MAAM;AAAA,EACpD,YACE,SACO,WACA,SACP;AACA,UAAM,OAAO;AAHN;AACA;AAGP,SAAK,OAAO;AAAA,EACd;AACF;;;AD1LO,IAAM,eAAN,MAAmB;AAAA,EAChB;AAAA,EACA;AAAA,EACA,eAAiC,oBAAI,IAAI;AAAA,EAEjD,YAAY,QAAsB;AAChC,SAAK,SAAS;AACd,SAAK,SAAS,aAAAA,QAAM,OAAO;AAAA,MACzB,SAAS,OAAO;AAAA,MAChB,SAAS,OAAO;AAAA,MAChB,SAAS;AAAA,QACP,iBAAiB,UAAU,OAAO,MAAM;AAAA,QACxC,gBAAgB;AAAA,QAChB,uBAAuB,OAAO;AAAA,QAC9B,oBAAoB;AAAA,MACtB;AAAA,IACF,CAAC;AAED,SAAK,kBAAkB;AAAA,EACzB;AAAA,EAEQ,oBAA0B;AAEhC,SAAK,OAAO,aAAa,QAAQ;AAAA,MAC/B,CAAC,WAAW;AACV,gBAAQ,IAAI,YAAY,OAAO,QAAQ,YAAY,CAAC,IAAI,OAAO,GAAG,EAAE;AACpE,eAAO;AAAA,MACT;AAAA,MACA,CAAC,UAAU,QAAQ,OAAO,KAAK;AAAA,IACjC;AAGA,SAAK,OAAO,aAAa,SAAS;AAAA,MAChC,CAAC,aAAa;AAAA,MACd,CAAC,UAAU;AACT,cAAM,cAAc,IAAI;AAAA,UACtB,MAAM,UAAU,MAAM,WAAW,MAAM;AAAA,UACvC,MAAM,UAAU,UAAU;AAAA,UAC1B,MAAM,UAAU;AAAA,QAClB;AACA,eAAO,QAAQ,OAAO,WAAW;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAOH;AACD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,OAAO,IAAI,8BAA8B;AACrE,aAAO,SAAS;AAAA,IAClB,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,0BACJ,SACA,eACiB;AACjB,UAAM,aAAa,KAAK,mBAAmB,SAAS,aAAa;AAEjE,QAAI,KAAK,aAAa,IAAI,UAAU,GAAG;AACrC,aAAO,KAAK,aAAa,IAAI,UAAU;AAAA,IACzC;AAEA,UAAM,UAAU,KAAK,oBAAoB,SAAS,aAAa;AAE/D,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,OAAO,KAAK,gBAAgB;AAAA,QACtD,MAAM,uBAAuB,QAAQ,EAAE;AAAA,QACvC;AAAA,QACA,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,UAAU,QAAQ,SAAS;AAAA,UAC3B,aAAa,QAAQ,YAAY;AAAA,UACjC,aAAa,QAAQ;AAAA,UACrB,WAAW,QAAQ,UAAU;AAAA,QAC/B;AAAA,MACF,CAAC;AAED,YAAM,YAAY,SAAS,KAAK;AAChC,WAAK,aAAa,IAAI,YAAY,SAAS;AAC3C,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBACJ,WACA,YAYC;AACD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,OAAO,KAAK,gBAAgB,SAAS,YAAY;AAAA,QAC3E;AAAA,QACA,OAAO;AAAA;AAAA,QACP,iBAAiB,KAAK,OAAO,cAAc;AAAA,QAC3C,cAAc;AAAA,UACZ,OAAO;AAAA,UACP,iBAAiB;AAAA,UACjB,mBAAmB;AAAA,QACrB;AAAA,MACF,CAAC;AAED,aAAO,KAAK,qBAAqB,SAAS,IAAI;AAAA,IAChD,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OACJ,aACA,mBACA,gBAAwB,KAOvB;AACD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,OAAO,KAAK,sBAAsB;AAAA,QAC5D;AAAA,QACA;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,QACtB,WAAW;AAAA,QACX,QAAQ;AAAA,MACV,CAAC;AAED,aAAO,SAAS;AAAA,IAClB,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QACJ,cACA,mBACA,SAAiB,GAMhB;AACD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,OAAO,KAAK,uBAAuB;AAAA,QAC7D;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX,OAAO;AAAA,MACT,CAAC;AAED,aAAO,SAAS;AAAA,IAClB,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAOH;AACD,QAAI;AACF,YAAM,WAAW,MAAM,KAAK,OAAO,IAAI,+BAA+B;AACtE,aAAO,SAAS;AAAA,IAClB,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,oBACN,SACA,QACK;AACL,UAAM,cAAc,QAAQ,SAAS;AACrC,UAAM,YAAY,KAAK,IAAI,cAAc,GAAG,OAAO,MAAM;AAEzD,UAAM,UAAU;AAAA,MACd,QAAQ;AAAA,MACR,OAAO,CAAC;AAAA,MACR,cAAc,CAAC;AAAA,IACjB;AAGA,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAQ,MAAM,KAAK,EAAE,MAAM,KAAK,OAAO,EAAE,CAAC;AAAA,IAC5C;AAGA,SAAK,kBAAkB,SAAS,QAAQ,aAAa,WAAW;AAGhE,SAAK,gBAAgB,SAAS,QAAQ,WAAW,WAAW;AAG5D,SAAK,gBAAgB,SAAS,WAAW;AAGzC,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAQ,aAAa,KAAK,EAAE,OAAO,GAAG,eAAe,EAAE,CAAC;AAAA,IAC1D;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,kBAAkB,SAAc,aAAoB,aAA2B;AACrF,gBAAY,QAAQ,CAAC,YAAY,QAAQ;AACvC,UAAI,WAAW,SAAS,gBAAgB;AAEtC,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,gBAAM,QAAS,WAAW,QAAQ,MAAW,KAAK;AAClD,kBAAQ,MAAM,KAAK;AAAA,YACjB,MAAM;AAAA,YACN,OAAO,IAAI;AAAA,YACX,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEQ,gBAAgB,SAAc,WAAgB,aAA2B;AAE/E,UAAM,SAAS,UAAU,QAAQ;AAEjC,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,QAAQ,SAAS,KAAK,KAAK;AACjC,cAAQ,MAAM,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,OAAO,IAAI,IAAI;AAAA,QACf,WAAW;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEQ,gBAAgB,SAAc,aAA2B;AAE/D,aAAS,IAAI,GAAG,IAAI,cAAc,GAAG,KAAK;AACxC,cAAQ,MAAM,KAAK;AAAA,QACjB,MAAM;AAAA,QACN,SAAS,IAAI;AAAA,QACb,SAAS,IAAI,KAAK;AAAA,MACpB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEQ,qBAAqB,WAAqB;AAChD,UAAM,eAAe,UAAU;AAC/B,UAAM,SAAS,UAAU;AAGzB,UAAM,SAAS,KAAK,mBAAmB,cAAc,MAAM;AAG3D,UAAM,mBAAmB,KAAK,0BAA0B,SAAS;AAEjE,WAAO;AAAA,MACL,QAAQ,OAAO;AAAA,MACf,YAAY,OAAO;AAAA,MACnB;AAAA,MACA,SAAS;AAAA,QACP,WAAW,UAAU,eAAe;AAAA,QACpC,OAAO,UAAU,eAAe;AAAA,QAChC,WAAW,UAAU,aAAa;AAAA,QAClC,eAAe,UAAU,iBAAiB;AAAA,QAC1C,eAAe,UAAU,OAAO;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,mBAAmB,cAAmB,QAAkB;AAE9D,UAAM,aAAa,OAAO,OAAO,MAAM,EAAE,OAAO,CAAC,GAAW,MAAW,IAAI,OAAO,CAAC,GAAG,CAAC;AACvF,UAAM,cAAwB,CAAC;AAC/B,QAAI,aAAa;AAGjB,QAAI,WAAmB;AACvB,QAAI,cAAc;AAElB,eAAW,CAAC,SAAS,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACrD,YAAM,WAAW,OAAO,KAAK;AAC7B,UAAI,WAAW,UAAU;AACvB,mBAAW;AACX,sBAAc;AAAA,MAChB;AAAA,IACF;AAEA,iBAAa,WAAW;AAGxB,UAAM,eAAe;AACrB,UAAM,cAAc,aAAa,SAAS;AAE1C,aAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,YAAM,cAAc,aAAa,MAAM,IAAI,IAAI,IAAI,KAAK,CAAC;AACzD,YAAM,aAAa,SAAS,aAAa,CAAC,IAAI;AAC9C,kBAAY,KAAK,UAAU;AAAA,IAC7B;AAEA,WAAO,EAAE,aAAa,WAAW;AAAA,EACnC;AAAA,EAEQ,0BAA0B,QAAqB;AAErD,UAAM,gBAAgB,OAAO,iBAAiB;AAC9C,UAAM,YAAY,OAAO,aAAa;AACtC,UAAM,WAAW,OAAO,OAAO;AAG/B,UAAM,qBAAqB,KAAK,IAAI,iBAAiB,WAAW,MAAM,CAAC;AACvE,UAAM,iBAAiB,KAAK,IAAI,GAAG,IAAI,YAAY,GAAG;AAEtD,YAAQ,qBAAqB,kBAAkB;AAAA,EACjD;AAAA,EAEQ,mBAAmB,SAA8B,QAAsC;AAC7F,UAAM,UAAU;AAAA,MACd,UAAU,QAAQ,SAAS;AAAA,MAC3B,aAAa,QAAQ,YAAY;AAAA,MACjC,WAAW,QAAQ,UAAU;AAAA,MAC7B,QAAQ,OAAO;AAAA,MACf,OAAO,OAAO;AAAA,IAChB;AAEA,WAAO,OAAO,KAAK,KAAK,UAAU,OAAO,CAAC,EAAE,SAAS,QAAQ;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAyB;AAC7B,SAAK,aAAa,MAAM;AAAA,EAE1B;AACF;;;AElaA,oBAA6C;AAuBtC,IAAM,kBAAN,MAAsB;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EAER,YAAY,UAIR,CAAC,GAAG;AACN,SAAK,gBAAgB,QAAQ,iBAAiB;AAC9C,SAAK,uBAAuB,QAAQ,wBAAwB;AAC5D,SAAK,iBAAiB,QAAQ,kBAAkB;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,SAA2D;AACxE,UAAM,YAAY,KAAK,IAAI;AAE3B,QAAI;AAEF,YAAM,YAAY,KAAK,gBAAgB,OAAO;AAC9C,cAAQ,IAAI,qBAAqB,SAAS,YAAY;AAEtD,UAAI;AAEJ,cAAQ,WAAW;AAAA,QACjB,KAAK;AACH,mBAAS,MAAM,KAAK,+BAA+B,OAAO;AAC1D;AAAA,QACF,KAAK;AACH,mBAAS,MAAM,KAAK,iBAAiB,OAAO;AAC5C;AAAA,QACF,KAAK;AACH,mBAAS,MAAM,KAAK,0BAA0B,OAAO;AACrD;AAAA,QACF,KAAK;AACH,mBAAS,MAAM,KAAK,mBAAmB,OAAO;AAC9C;AAAA,QACF,KAAK;AACH,mBAAS,MAAM,KAAK,oBAAoB,OAAO;AAC/C;AAAA,QACF;AACE,gBAAM,IAAI;AAAA,YACR,sBAAsB,SAAS;AAAA,YAC/B;AAAA,UACF;AAAA,MACJ;AAEA,aAAO,cAAc,KAAK,IAAI,IAAI;AAClC,aAAO,SAAS;AAEhB,aAAO;AAAA,IACT,SAAS,OAAO;AACd,YAAM,IAAI;AAAA,QACR,kCAAkC,MAAM,OAAO;AAAA,QAC/C;AAAA,QACA,EAAE,eAAe,MAAM;AAAA,MACzB;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,+BAA+B,SAA2D;AACtG,UAAM,cAAc,QAAQ,SAAS;AACrC,QAAI,IAAI,KAAK,mBAAmB,OAAO;AACvC,QAAI,YAAY;AAChB,QAAI,YAAY;AAEhB,UAAM,UAA+B;AAAA,MACnC,gBAAgB;AAAA,MAChB,sBAAsB;AAAA,MACtB,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,iBAAiB;AAAA,IACnB;AAEA,WAAO,YAAY,KAAK,iBAAiB,CAAC,WAAW;AAEnD,YAAM,WAAW,KAAK,kBAAkB,GAAG,OAAO;AAClD,YAAM,UAAU,KAAK,iBAAiB,GAAG,OAAO;AAGhD,YAAM,YAAY,KAAK,kBAAkB,UAAU,SAAS,GAAG,OAAO;AAGtE,YAAM,WAAW,KAAK,WAAW,GAAG,WAAW,OAAO;AAGtD,YAAM,OAAO,EAAE,IAAI,CAAC,IAAI,MAAM,KAAK,WAAW,UAAU,CAAC,CAAC;AAG1D,YAAM,SAAS,KAAK,cAAc,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC;AAChE,kBAAY,SAAS,KAAK;AAE1B,UAAI;AACJ;AAGA,cAAQ,iBAAiB,KAAK,kBAAkB,GAAG,OAAO;AAC1D,cAAQ,uBAAuB,KAAK,6BAA6B,GAAG,OAAO;AAAA,IAC7E;AAEA,YAAQ,cAAc,QAAQ,uBAAuB;AACrD,YAAQ,aAAa,YAAY,IAAM;AACvC,YAAQ,kBAAkB,YAAY,KAAK;AAE3C,WAAO,KAAK,YAAY,SAAS,GAAG,SAAS,WAAW,KAAK;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,iBAAiB,SAA2D;AACxF,UAAM,cAAc,QAAQ,SAAS;AACrC,QAAI,aAAa,KAAK,qBAAqB,SAAS,KAAK,cAAc;AACvE,QAAI,aAAa;AACjB,QAAI,eAAe,WAAW,CAAC;AAC/B,QAAI,cAAc,KAAK,gBAAgB,cAAc,OAAO;AAE5D,WAAO,aAAa,KAAK,eAAe;AAEtC,YAAM,UAAU,WAAW,IAAI,gBAAc,KAAK,gBAAgB,YAAY,OAAO,CAAC;AAGtF,YAAM,kBAAkB,QAAQ,QAAQ,KAAK,IAAI,GAAG,OAAO,CAAC;AAC5D,UAAI,QAAQ,eAAe,IAAI,aAAa;AAC1C,2BAAe,yBAAU,WAAW,eAAe,CAAC;AACpD,sBAAc,QAAQ,eAAe;AAAA,MACvC;AAGA,YAAM,UAAU,KAAK,oBAAoB,YAAY,SAAS,KAAK,cAAc;AAGjF,YAAM,YAAY,KAAK,qBAAqB,SAAS,OAAO;AAG5D,mBAAa;AACb;AAAA,IACF;AAEA,UAAM,UAA+B;AAAA,MACnC,gBAAgB,KAAK,kBAAkB,cAAc,OAAO;AAAA,MAC5D,sBAAsB,KAAK,6BAA6B,cAAc,OAAO;AAAA,MAC7E,aAAa,KAAK,6BAA6B,cAAc,OAAO,IAAI;AAAA,MACxE,YAAY;AAAA;AAAA,MACZ,iBAAiB,aAAa,KAAK;AAAA,IACrC;AAEA,WAAO,KAAK,YAAY,SAAS,cAAc,SAAS,YAAY,SAAS;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,0BAA0B,SAA2D;AACjG,UAAM,cAAc,QAAQ,SAAS;AACrC,UAAM,eAAe,KAAK;AAG1B,UAAM,YAAY,MAAM,YAAY,EAAE,KAAK,IAAI,EAAE,IAAI,OAAO;AAAA,MAC1D,UAAU,KAAK,mBAAmB,OAAO;AAAA,MACzC,UAAU,MAAM,WAAW,EAAE,KAAK,CAAC,EAAE,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,GAAG;AAAA,MAC1E,cAAc,CAAC;AAAA,MACf,aAAa;AAAA,IACf,EAAE;AAEF,QAAI,qBAAqB,UAAU,CAAC,EAAE;AACtC,QAAI,oBAAoB;AACxB,QAAI,YAAY;AAGhB,UAAM,IAAI;AACV,UAAM,KAAK;AACX,UAAM,KAAK;AAEX,WAAO,YAAY,KAAK,eAAe;AACrC,iBAAW,YAAY,WAAW;AAChC,cAAM,UAAU,KAAK,gBAAgB,SAAS,UAAU,OAAO;AAG/D,YAAI,UAAU,SAAS,aAAa;AAClC,mBAAS,mBAAe,yBAAU,SAAS,QAAQ;AACnD,mBAAS,cAAc;AAAA,QACzB;AAGA,YAAI,UAAU,mBAAmB;AAC/B,mCAAqB,yBAAU,SAAS,QAAQ;AAChD,8BAAoB;AAAA,QACtB;AAGA,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,gBAAM,KAAK,KAAK,OAAO;AACvB,gBAAM,KAAK,KAAK,OAAO;AAEvB,mBAAS,SAAS,CAAC,IAAI,IAAI,SAAS,SAAS,CAAC,IAC5C,KAAK,MAAM,SAAS,aAAa,CAAC,IAAI,SAAS,SAAS,CAAC,KACzD,KAAK,MAAM,mBAAmB,CAAC,IAAI,SAAS,SAAS,CAAC;AAExD,mBAAS,SAAS,CAAC,KAAK,SAAS,SAAS,CAAC;AAG3C,mBAAS,SAAS,CAAC,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,SAAS,SAAS,CAAC,CAAC,CAAC;AAAA,QACtE;AAAA,MACF;AAEA;AAAA,IACF;AAEA,UAAM,UAA+B;AAAA,MACnC,gBAAgB,KAAK,kBAAkB,oBAAoB,OAAO;AAAA,MAClE,sBAAsB,KAAK,6BAA6B,oBAAoB,OAAO;AAAA,MACnF,aAAa,KAAK,6BAA6B,oBAAoB,OAAO,IAAI;AAAA,MAC9E,YAAY;AAAA,MACZ,iBAAiB,YAAY,KAAK;AAAA,IACpC;AAEA,WAAO,KAAK,YAAY,SAAS,oBAAoB,SAAS,WAAW,KAAK;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,mBAAmB,SAA2D;AAC1F,QAAI,kBAAkB,KAAK,mBAAmB,OAAO;AACrD,QAAI,gBAAgB,CAAC,KAAK,gBAAgB,iBAAiB,OAAO;AAClE,QAAI,mBAAe,yBAAU,eAAe;AAC5C,QAAI,aAAa;AAEjB,UAAM,cAAc;AACpB,UAAM,YAAY;AAClB,UAAM,cAAc;AACpB,QAAI,cAAc;AAClB,QAAI,YAAY;AAEhB,WAAO,cAAc,aAAa,YAAY,KAAK,eAAe;AAEhE,YAAM,WAAW,KAAK,iBAAiB,iBAAiB,OAAO;AAC/D,YAAM,iBAAiB,CAAC,KAAK,gBAAgB,UAAU,OAAO;AAG9D,YAAM,SAAS,iBAAiB;AAChC,UAAI,SAAS,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,CAAC,SAAS,WAAW,GAAG;AACjE,0BAAkB;AAClB,wBAAgB;AAEhB,YAAI,gBAAgB,YAAY;AAC9B,6BAAe,yBAAU,eAAe;AACxC,uBAAa;AAAA,QACf;AAAA,MACF;AAEA,qBAAe;AACf;AAAA,IACF;AAEA,UAAM,UAA+B;AAAA,MACnC,gBAAgB,KAAK,kBAAkB,cAAc,OAAO;AAAA,MAC5D,sBAAsB,KAAK,6BAA6B,cAAc,OAAO;AAAA,MAC7E,aAAa,KAAK,6BAA6B,cAAc,OAAO,IAAI;AAAA,MACxE,YAAY;AAAA,MACZ,iBAAiB,YAAY,KAAK;AAAA,IACpC;AAEA,WAAO,KAAK,YAAY,SAAS,cAAc,SAAS,WAAW,qBAAqB;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc,oBAAoB,SAA2D;AAC3F,UAAM,cAAc,QAAQ,SAAS;AACrC,QAAI,IAAI,KAAK,mBAAmB,OAAO;AACvC,QAAI,KAAK;AACT,QAAI,YAAY;AAChB,QAAI,YAAY;AAEhB,WAAO,YAAY,KAAK,iBAAiB,CAAC,aAAa,KAAK,MAAM;AAEhE,YAAM,SAAS,KAAK,uBAAuB,GAAG,IAAI,OAAO;AACzD,UAAI,OAAO;AACX,kBAAY,OAAO;AAGnB,YAAM;AACN;AAAA,IACF;AAEA,UAAM,UAA+B;AAAA,MACnC,gBAAgB,KAAK,kBAAkB,GAAG,OAAO;AAAA,MACjD,sBAAsB,KAAK,6BAA6B,GAAG,OAAO;AAAA,MAClE,aAAa,KAAK,6BAA6B,GAAG,OAAO,IAAI;AAAA,MAC7D,YAAY,YAAY,OAAO;AAAA,MAC/B,iBAAiB,YAAY,KAAK;AAAA,IACpC;AAEA,WAAO,KAAK,YAAY,SAAS,GAAG,SAAS,WAAW,gBAAgB;AAAA,EAC1E;AAAA;AAAA,EAGQ,gBAAgB,SAAsC;AAC5D,UAAM,cAAc,QAAQ,SAAS;AACrC,UAAM,iBAAiB,QAAQ,YAAY;AAC3C,UAAM,0BAA0B,QAAQ,YAAY;AAAA,MAAK,OACvD,EAAE,SAAS,iBAAiB,EAAE,SAAS;AAAA,IACzC;AAEA,QAAI,eAAe,KAAK,CAAC,yBAAyB;AAChD,aAAO;AAAA,IACT,WAAW,cAAc,MAAM,yBAAyB;AACtD,aAAO;AAAA,IACT,WAAW,QAAQ,UAAU,YAAY,iBAAiB;AACxD,aAAO;AAAA,IACT,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEQ,mBAAmB,SAAwC;AAEjE,UAAM,cAAc,QAAQ,SAAS;AACrC,WAAO,MAAM,WAAW,EAAE,KAAK,IAAI,WAAW;AAAA,EAChD;AAAA,EAEQ,kBAAkB,YAAsB,SAAsC;AACpF,QAAI,YAAY;AAChB,UAAM,cAAc,QAAQ;AAE5B,eAAW,QAAQ,CAAC,QAAQ,MAAM;AAChC,YAAM,UAAU,QAAQ,SAAS,CAAC;AAClC,YAAM,SAAS,SAAS;AAGxB,YAAM,cAAc,KAAK,qBAAqB,QAAQ,OAAO;AAC7D,YAAM,UAAU,cAAc,QAAQ;AAEtC,cAAQ,QAAQ,UAAU,SAAS;AAAA,QACjC,KAAK;AACH,uBAAa;AACb;AAAA,QACF,KAAK;AACH,uBAAa;AACb;AAAA,QACF,KAAK;AACH,uBAAa,SAAS,KAAK,IAAI,aAAa,CAAC;AAC7C;AAAA,QACF,KAAK;AACH,uBAAa,UAAU,KAAK,IAAI,QAAQ,CAAC;AACzC;AAAA,MACJ;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEQ,gBAAgB,YAAsB,SAAsC;AAClF,UAAM,YAAY,KAAK,kBAAkB,YAAY,OAAO;AAC5D,UAAM,aAAa,KAAK,6BAA6B,YAAY,OAAO;AAGxE,WAAO,YAAY,MAAO;AAAA,EAC5B;AAAA,EAEQ,6BAA6B,YAAsB,SAAsC;AAC/F,QAAI,aAAa;AACjB,UAAM,cAAc,QAAQ;AAG5B,UAAM,oBAAgB,mBAAI,UAAU;AACpC,kBAAc,KAAK,IAAI,gBAAgB,CAAC;AAGxC,YAAQ,YAAY,QAAQ,gBAAc;AACxC,cAAQ,WAAW,MAAM;AAAA,QACvB,KAAK;AACH,gBAAM,aAAa,gBAAgB;AACnC,cAAI,WAAW,aAAa,QAAQ,aAAa,WAAW,OAAO;AACjE,2BAAe,aAAa,WAAW,SAAS,WAAW;AAAA,UAC7D;AACA;AAAA,MAEJ;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEQ,qBAAqB,QAAgB,SAAsB;AAEjE,UAAM,kBAAkB,SAAS,QAAQ,iBAAiB,QAAQ;AAClE,UAAM,mBAAmB,QAAQ,kBAC/B,KAAK,IAAI,GAAG,QAAQ,kBAAkB,MAAM,IAAI;AAElD,WAAO,kBAAkB,mBAAmB,QAAQ;AAAA,EACtD;AAAA,EAEQ,YACN,SACA,YACA,SACA,YACA,WACoB;AACpB,UAAM,cAAkC,WAAW,IAAI,CAAC,QAAQ,MAAM;AACpE,YAAM,UAAU,QAAQ,SAAS,CAAC;AAClC,YAAM,SAAS,SAAS,QAAQ;AAChC,YAAM,cAAc,KAAK,qBAAqB,QAAQ,OAAO;AAC7D,YAAM,UAAU,cAAc,QAAQ;AAEtC,aAAO;AAAA,QACL,WAAW,QAAQ;AAAA,QACnB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,aAAa,SAAS,KAAK,IAAI,aAAa,CAAC;AAAA,QAC7C,cAAc,UAAU,KAAK,IAAI,QAAQ,CAAC;AAAA,QAC1C,YAAY;AAAA;AAAA,QACZ,WAAW;AAAA;AAAA,MACb;AAAA,IACF,CAAC;AAED,WAAO;AAAA,MACL,IAAI,UAAU,KAAK,IAAI,CAAC;AAAA,MACxB,WAAW,QAAQ;AAAA,MACnB,QAAQ,QAAQ,cAAc,YAAY;AAAA,MAC1C,QAAQ;AAAA,MACR;AAAA,MACA,oBAAgB,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC;AAAA,MAC3D,0BAAsB,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC;AAAA,MACjE,8BAA0B,mBAAI,YAAY,IAAI,OAAK,EAAE,mBAAmB,CAAC;AAAA,MACzE,iBAAa,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC,IAC3C,KAAK,QAAI,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC,GAAG,CAAC;AAAA,MACrE,gBAAY,mBAAI,YAAY,IAAI,OAAK,EAAE,eAAe,CAAC,IAC3C,KAAK,QAAI,mBAAI,YAAY,IAAI,OAAK,EAAE,mBAAmB,CAAC,GAAG,CAAC;AAAA,MACxE,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ,cAAc,IAAI;AAAA,MACrC,qBAAqB;AAAA,QACnB;AAAA,QACA,iBAAiB;AAAA;AAAA,QACjB,gBAAgB,QAAQ;AAAA,QACxB,sBAAsB,CAAC;AAAA,MACzB;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,WAAW,oBAAI,KAAK;AAAA,MACpB,aAAa;AAAA;AAAA,IACf;AAAA,EACF;AAAA;AAAA,EAGQ,kBAAkB,GAAa,SAAwC;AAC7E,UAAM,IAAI;AACV,UAAM,WAAW,IAAI,MAAM,EAAE,MAAM;AAEnC,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,YAAM,QAAQ,CAAC,GAAG,CAAC;AACnB,YAAM,SAAS,CAAC,GAAG,CAAC;AACpB,YAAM,CAAC,KAAK;AACZ,aAAO,CAAC,KAAK;AAEb,eAAS,CAAC,KAAK,KAAK,kBAAkB,OAAO,OAAO,IACtC,KAAK,kBAAkB,QAAQ,OAAO,MAAM,IAAI;AAAA,IAChE;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,iBAAiB,GAAa,SAA0C;AAC9E,UAAM,IAAI;AACV,UAAM,IAAI,EAAE;AACZ,UAAM,UAAU,MAAM,CAAC,EAAE,KAAK,IAAI,EAAE,IAAI,MAAM,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AAE9D,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,MAAM,CAAC,GAAG,CAAC;AAAG,YAAI,CAAC,KAAK;AAAG,YAAI,CAAC,KAAK;AAC3C,cAAM,MAAM,CAAC,GAAG,CAAC;AAAG,YAAI,CAAC,KAAK;AAAG,YAAI,CAAC,KAAK;AAC3C,cAAM,MAAM,CAAC,GAAG,CAAC;AAAG,YAAI,CAAC,KAAK;AAAG,YAAI,CAAC,KAAK;AAC3C,cAAM,MAAM,CAAC,GAAG,CAAC;AAAG,YAAI,CAAC,KAAK;AAAG,YAAI,CAAC,KAAK;AAE3C,gBAAQ,CAAC,EAAE,CAAC,KACV,KAAK,kBAAkB,KAAK,OAAO,IACnC,KAAK,kBAAkB,KAAK,OAAO,IACnC,KAAK,kBAAkB,KAAK,OAAO,IACnC,KAAK,kBAAkB,KAAK,OAAO,MAChC,IAAI,IAAI;AAAA,MACf;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,kBAAkB,UAAoB,SAAqB,GAAa,SAAwC;AAEtH,UAAM,IAAI,EAAE;AACZ,UAAM,YAAY,IAAI,MAAM,CAAC,EAAE,KAAK,CAAC;AAGrC,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,gBAAU,CAAC,IAAI,CAAC,SAAS,CAAC;AAAA,IAC5B;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,WAAW,GAAa,WAAqB,SAAsC;AACzF,QAAI,QAAQ;AACZ,UAAM,KAAK;AACX,UAAM,MAAM;AAEZ,UAAM,KAAK,KAAK,kBAAkB,GAAG,OAAO;AAC5C,UAAM,QAAQ,KAAK,kBAAkB,GAAG,OAAO;AAC/C,UAAM,4BAAwB,mBAAI,MAAM,IAAI,CAAC,GAAG,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC;AAEvE,WAAO,QAAQ,MAAM;AACnB,YAAM,OAAO,EAAE,IAAI,CAAC,IAAI,MAAM,KAAK,QAAQ,UAAU,CAAC,CAAC;AACvD,YAAM,KAAK,KAAK,kBAAkB,MAAM,OAAO;AAE/C,UAAI,MAAM,KAAK,KAAK,QAAQ,uBAAuB;AACjD,eAAO;AAAA,MACT;AAEA,eAAS;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,cAAc,QAA0B;AAC9C,WAAO,KAAK,SAAK,mBAAI,OAAO,IAAI,OAAK,IAAI,CAAC,CAAC,CAAC;AAAA,EAC9C;AAAA,EAEQ,qBAAqB,SAA8B,MAA0B;AACnF,UAAM,cAAc,QAAQ,SAAS;AACrC,UAAM,aAAyB,CAAC;AAEhC,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,YAAM,aAAa,MAAM,WAAW,EAAE,KAAK,CAAC,EAAE,IAAI,MAAM,KAAK,OAAO,CAAC;AACrE,YAAM,YAAQ,mBAAI,UAAU;AAE5B,iBAAW,KAAK,WAAW,IAAI,OAAK,IAAI,KAAK,CAAC;AAAA,IAChD;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,oBAAoB,YAAwB,SAAmB,MAA0B;AAC/F,UAAM,WAAuB,CAAC;AAC9B,UAAM,iBAAiB;AAEvB,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAC7B,UAAI,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM;AAEvD,eAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACvC,cAAM,YAAY,KAAK,MAAM,KAAK,OAAO,IAAI,WAAW,MAAM;AAC9D,YAAI,QAAQ,SAAS,IAAI,QAAQ,IAAI,GAAG;AACtC,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,eAAS,SAAK,yBAAU,WAAW,IAAI,CAAC,CAAC;AAAA,IAC3C;AAEA,WAAO;AAAA,EACT;AAAA,EAEQ,qBAAqB,SAAqB,SAA0C;AAC1F,UAAM,YAAwB,CAAC;AAC/B,UAAM,eAAe;AACrB,UAAM,gBAAgB;AAEtB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1C,UAAI,aAAS,yBAAU,QAAQ,CAAC,CAAC;AACjC,UAAI,aAAS,yBAAU,QAAQ,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;AAGnD,UAAI,KAAK,OAAO,IAAI,eAAe;AACjC,cAAM,iBAAiB,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM;AAC/D,iBAAS,IAAI,gBAAgB,IAAI,OAAO,QAAQ,KAAK;AACnD,WAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,QAChD;AAAA,MACF;AAGA,UAAI,KAAK,OAAO,IAAI,cAAc;AAChC,cAAM,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO,MAAM;AAC9D,eAAO,aAAa,MAAM,KAAK,OAAO,IAAI,OAAO;AACjD,eAAO,aAAa,IAAI,KAAK,IAAI,GAAG,OAAO,aAAa,CAAC;AAAA,MAC3D;AAGA,YAAM,aAAS,mBAAI,MAAM;AACzB,YAAM,aAAS,mBAAI,MAAM;AACzB,eAAS,OAAO,IAAI,OAAK,IAAI,MAAM;AACnC,eAAS,OAAO,IAAI,OAAK,IAAI,MAAM;AAEnC,gBAAU,KAAK,QAAQ,MAAM;AAAA,IAC/B;AAEA,WAAO,UAAU,MAAM,GAAG,QAAQ,MAAM;AAAA,EAC1C;AAAA,EAEQ,iBAAiB,UAAoB,SAAwC;AACnF,UAAM,eAAW,yBAAU,QAAQ;AACnC,UAAM,mBAAmB;AAGzB,UAAM,mBAAmB,KAAK,OAAO,IAAI,MAAM,IAAI;AAEnD,aAAS,IAAI,GAAG,IAAI,kBAAkB,KAAK;AACzC,YAAM,QAAQ,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM;AACxD,eAAS,KAAK,MAAM,KAAK,OAAO,IAAI,OAAO;AAC3C,eAAS,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,KAAK,CAAC;AAAA,IAC/C;AAGA,UAAM,YAAQ,mBAAI,QAAQ;AAC1B,WAAO,SAAS,IAAI,OAAK,IAAI,KAAK;AAAA,EACpC;AAAA,EAEQ,uBAAuB,GAAa,IAAY,SAA0E;AAEhI,QAAI,eAAW,yBAAU,CAAC;AAC1B,QAAI,YAAY;AAChB,UAAM,qBAAqB;AAE3B,aAAS,OAAO,GAAG,OAAO,oBAAoB,QAAQ;AACpD,YAAM,WAAW,KAAK,yBAAyB,UAAU,IAAI,OAAO;AACpE,YAAM,WAAW,KAAK,WAAW,UAAU,SAAS,IAAI,OAAK,CAAC,CAAC,GAAG,OAAO;AAEzE,YAAM,OAAO,SAAS,IAAI,CAAC,IAAI,MAAM,KAAK,WAAW,SAAS,CAAC,CAAC;AAGhE,YAAM,YAAQ,mBAAI,IAAI;AACtB,YAAM,cAAc,KAAK,IAAI,QAAM,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AAE7D,YAAM,SAAS,KAAK,cAAc,YAAY,IAAI,CAAC,IAAI,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC;AAC9E,UAAI,SAAS,KAAK,sBAAsB;AACtC,oBAAY;AACZ;AAAA,MACF;AAEA,iBAAW;AAAA,IACb;AAEA,WAAO,EAAE,UAAU,UAAU,UAAU;AAAA,EACzC;AAAA,EAEQ,yBAAyB,GAAa,IAAY,SAAwC;AAChG,UAAM,oBAAoB,KAAK,kBAAkB,GAAG,OAAO;AAC3D,UAAM,kBAAkB,IAAI,MAAM,EAAE,MAAM;AAG1C,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,sBAAgB,CAAC,IAAI,kBAAkB,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI;AAAA,IACtE;AAEA,WAAO;AAAA,EACT;AACF;;;AChqBO,IAAM,mBAAN,MAAuB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA,qBAA4C,oBAAI,IAAI;AAAA,EAE5D,YAAY,QAAsB;AAChC,SAAK,SAAS;AACd,SAAK,eAAe,IAAI,aAAa,MAAM;AAC3C,SAAK,kBAAkB,IAAI,gBAAgB;AAAA,MACzC,eAAe;AAAA,MACf,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,SAA2D;AACxE,UAAM,YAAY,KAAK,IAAI;AAE3B,QAAI;AAEF,YAAM,WAAW,MAAM,KAAK,2BAA2B,OAAO;AAC9D,cAAQ,IAAI,mBAAmB,QAAQ,wBAAwB;AAE/D,UAAI;AAEJ,cAAQ,UAAU;AAAA,QAChB,KAAK;AACH,mBAAS,MAAM,KAAK,oBAAoB,OAAO;AAC/C;AAAA,QACF,KAAK;AACH,mBAAS,MAAM,KAAK,sBAAsB,OAAO;AACjD;AAAA,QACF,KAAK;AACH,mBAAS,MAAM,KAAK,mBAAmB,OAAO;AAC9C;AAAA,QACF;AACE,gBAAM,IAAI;AAAA,YACR,kCAAkC,QAAQ;AAAA,YAC1C;AAAA,UACF;AAAA,MACJ;AAGA,WAAK,yBAAyB,UAAU,OAAO,WAAW;AAG1D,aAAO,gBAAgB,KAAK,GAAG,KAAK,+BAA+B,MAAM,CAAC;AAE1E,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,6DAA6D,KAAK;AAGhF,YAAM,iBAAiB,MAAM,KAAK,sBAAsB,OAAO;AAC/D,qBAAe,SAAS;AACxB,qBAAe,gBAAgB,QAAQ;AAAA,QACrC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACN,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAED,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,oBAAoB,SAA2D;AAC3F,YAAQ,IAAI,mDAAmD;AAG/D,UAAM,SAAS,MAAM,KAAK,aAAa,mBAAmB;AAC1D,QAAI,CAAC,OAAO,WAAW;AACrB,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,QACA,EAAE,OAAO;AAAA,MACX;AAAA,IACF;AAGA,UAAM,YAAY,MAAM,KAAK,aAAa;AAAA,MACxC;AAAA,MACA,KAAK,OAAO;AAAA,IACd;AAGA,UAAM,aAAa,KAAK,yBAAyB,OAAO;AAGxD,UAAM,gBAAgB,MAAM,KAAK,aAAa,oBAAoB,WAAW,UAAU;AAGvF,UAAM,cAAc,KAAK;AAAA,MACvB,cAAc;AAAA,MACd;AAAA,IACF;AAGA,UAAM,SAA6B;AAAA,MACjC,IAAI,WAAW,KAAK,IAAI,CAAC;AAAA,MACzB,WAAW,QAAQ;AAAA,MACnB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR;AAAA,MACA,gBAAgB,YAAY,OAAO,CAACC,MAAK,MAAMA,OAAM,EAAE,iBAAiB,CAAC;AAAA,MACzE,sBAAsB,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,iBAAiB,CAAC;AAAA,MAC/E,0BAA0B,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,qBAAqB,CAAC;AAAA,MACvF,aAAa;AAAA;AAAA,MACb,YAAY;AAAA;AAAA,MACZ,YAAY,cAAc;AAAA,MAC1B,WAAW,KAAK,0BAA0B,aAAa;AAAA,MACvD,qBAAqB;AAAA,QACnB,YAAY;AAAA;AAAA,QACZ,iBAAiB,cAAc,QAAQ;AAAA,QACvC,gBAAgB,KAAK,wBAAwB,aAAa,OAAO;AAAA,QACjE,sBAAsB,CAAC;AAAA,MACzB;AAAA,MACA,gBAAgB;AAAA,QACd,kBAAkB,cAAc;AAAA,QAChC,eAAe,cAAc,QAAQ;AAAA,QACrC,WAAW,cAAc,QAAQ;AAAA,QACjC,WAAW,cAAc,QAAQ;AAAA,QACjC,uBAAuB,OAAO,SAAS;AAAA;AAAA,MACzC;AAAA,MACA,iBAAiB,CAAC;AAAA,MAClB,WAAW,oBAAI,KAAK;AAAA,MACpB,aAAa,cAAc,QAAQ;AAAA,IACrC;AAGA,WAAO,cAAc,OAAO,uBAAuB,KAAK,IAAI,OAAO,gBAAgB,CAAC;AACpF,WAAO,aAAa,OAAO,iBAAiB,KAAK,IAAI,OAAO,0BAA0B,CAAC;AAEvF,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,sBAAsB,SAA2D;AAC7F,YAAQ,IAAI,iDAAiD;AAC7D,WAAO,MAAM,KAAK,gBAAgB,SAAS,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,mBAAmB,SAA2D;AAC1F,YAAQ,IAAI,wCAAwC;AAEpD,QAAI;AAEF,YAAM,gBAAgB,MAAM,KAAK,oBAAoB,OAAO;AAG5D,YAAM,iBAAiB,KAAK,wBAAwB,SAAS,aAAa;AAC1E,YAAM,kBAAkB,MAAM,KAAK,gBAAgB,SAAS,cAAc;AAG1E,YAAM,eAAe,KAAK,eAAe,eAAe,eAAe;AACvE,mBAAa,SAAS;AAEtB,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,KAAK,+DAA+D,KAAK;AACjF,aAAO,MAAM,KAAK,sBAAsB,OAAO;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,2BAA2B,SAA2E;AAClH,UAAM,cAAc,QAAQ,SAAS;AACrC,UAAM,iBAAiB,QAAQ,YAAY;AAC3C,UAAM,cAAc,QAAQ;AAG5B,QAAI,CAAC,QAAQ,YAAY,wBAAwB;AAC/C,aAAO;AAAA,IACT;AAGA,QAAI;AACF,YAAM,SAAS,MAAM,KAAK,aAAa,mBAAmB;AAC1D,UAAI,CAAC,OAAO,aAAa,OAAO,cAAc,IAAI;AAChD,eAAO;AAAA,MACT;AAGA,YAAM,wBAAwB,KAAK,+BAA+B,OAAO;AAEzE,UAAI,wBAAwB,KAAK;AAC/B,eAAO;AAAA,MACT,WAAW,wBAAwB,KAAK;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,KAAK,oDAAoD,KAAK;AACtE,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,+BAA+B,SAAsC;AAC3E,QAAI,QAAQ;AAGZ,UAAM,cAAc,QAAQ,SAAS;AACrC,QAAI,eAAe,GAAI,UAAS;AAChC,QAAI,eAAe,GAAI,UAAS;AAGhC,UAAM,qBAAqB,QAAQ,YAAY;AAAA,MAAO,OACpD,EAAE,SAAS,iBAAiB,EAAE,SAAS;AAAA,IACzC,EAAE;AACF,aAAS,KAAK,IAAI,qBAAqB,MAAM,GAAG;AAGhD,QAAI,QAAQ,UAAU,YAAY,gBAAiB,UAAS;AAC5D,QAAI,QAAQ,UAAU,UAAW,UAAS;AAG1C,UAAM,iBAAiB,KAAK,mBAAmB,IAAI,SAAS,KAAK,CAAC;AAClE,UAAM,mBAAmB,KAAK,mBAAmB,IAAI,WAAW,KAAK,CAAC;AAEtE,QAAI,eAAe,SAAS,KAAK,iBAAiB,SAAS,GAAG;AAC5D,YAAM,iBAAiB,eAAe,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,eAAe;AAC/E,YAAM,mBAAmB,iBAAiB,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC,IAAI,iBAAiB;AAErF,UAAI,iBAAiB,iBAAkB,UAAS;AAAA,IAClD;AAEA,WAAO,KAAK,IAAI,OAAO,CAAG;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKQ,yBAAyB,SAAsD;AACrF,UAAM,aAAqC,CAAC;AAG5C,eAAW,cAAc,QAAQ,cAAc;AAG/C,YAAQ,SAAS,QAAQ,CAAC,SAAS,MAAM;AACvC,iBAAW,WAAW,CAAC,MAAM,IAAI,QAAQ,YAAY,QAAQ;AAC7D,iBAAW,WAAW,CAAC,MAAM,IAAI,QAAQ,YAAY,QAAQ;AAC7D,iBAAW,WAAW,CAAC,kBAAkB,IAAI,QAAQ;AACrD,iBAAW,WAAW,CAAC,MAAM,IAAI,QAAQ,oBAAoB;AAAA,IAC/D,CAAC;AAGD,eAAW,iBAAiB,QAAQ,UAAU,QAAQ;AACtD,eAAW,mBAAmB,QAAQ,UAAU,QAAQ,aAAa;AAErE,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,kCACN,eACA,SACoB;AACpB,UAAM,cAAkC,CAAC;AACzC,UAAM,cAAc,QAAQ;AAG5B,UAAMA,OAAM,cAAc,OAAO,CAAC,GAAG,MAAM,IAAI,GAAG,CAAC;AACnD,UAAM,oBAAoB,cAAc,IAAI,OAAK,IAAIA,IAAG;AAExD,YAAQ,SAAS,QAAQ,CAAC,SAAS,MAAM;AACvC,YAAM,SAAS,kBAAkB,CAAC,KAAK;AACvC,YAAM,SAAS,SAAS;AAGxB,YAAM,oBAAoB,KAAK;AAAA,QAC7B,QAAQ;AAAA,QACR,KAAK,IAAI,QAAQ,WAAW,MAAM;AAAA,MACpC;AAGA,YAAM,cAAc,KAAK,6BAA6B,mBAAmB,OAAO;AAChF,YAAM,UAAU,cAAc,QAAQ;AAEtC,kBAAY,KAAK;AAAA,QACf,WAAW,QAAQ;AAAA,QACnB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,aAAa,oBAAoB,KAAK,IAAI,aAAa,CAAC;AAAA,QACxD,cAAc,UAAU,KAAK,IAAI,mBAAmB,CAAC;AAAA,QACrD,YAAY;AAAA;AAAA,QACZ,WAAW;AAAA;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,6BAA6B,QAAgB,SAAsB;AAEzE,UAAM,kBAAkB,SAAS,QAAQ,iBAAiB,QAAQ;AAGlE,QAAI,mBAAmB;AACvB,QAAI,QAAQ,mBAAmB,SAAS,QAAQ,iBAAiB;AAC/D,yBAAmB,KAAK,KAAK,QAAQ,kBAAkB,MAAM;AAAA,IAC/D;AAEA,WAAO,kBAAkB,mBAAmB,QAAQ;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKQ,0BAA0B,eAA4B;AAC5D,QAAI,YAAY;AAGhB,QAAI,cAAc,QAAQ,YAAY,MAAM;AAC1C,mBAAa;AAAA,IACf;AAGA,QAAI,cAAc,QAAQ,gBAAgB,IAAI;AAC5C,mBAAa;AAAA,IACf;AAGA,QAAI,cAAc,mBAAmB,KAAK;AACxC,mBAAa;AAAA,IACf;AAEA,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,IAAI,SAAS,CAAC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKQ,wBAAwB,aAAiC,SAAsC;AACrG,QAAI,QAAQ;AAEZ,YAAQ,QAAQ,UAAU,SAAS;AAAA,MACjC,KAAK;AACH,gBAAQ,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,iBAAiB,CAAC;AACjE;AAAA,MACF,KAAK;AACH,gBAAQ,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,qBAAqB,CAAC;AACrE;AAAA,MACF,KAAK;AACH,cAAM,WAAW,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,aAAa,CAAC,IAAI,YAAY;AACtF,gBAAQ,CAAC;AACT;AAAA,MACF,KAAK;AACH,cAAM,YAAY,YAAY,OAAO,CAACA,MAAK,MAAMA,OAAM,EAAE,cAAc,CAAC,IAAI,YAAY;AACxF,gBAAQ;AACR;AAAA,IACJ;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,+BAA+B,QAAmC;AACxE,UAAM,kBAAyB,CAAC;AAGhC,QAAI,OAAO,gBAAgB,oBAAoB,OAAO,eAAe,mBAAmB,KAAK;AAC3F,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACN,eAAe,OAAO,uBAAuB;AAAA,UAC7C,YAAY,OAAO,iBAAiB;AAAA,UACpC,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAGA,QAAI,OAAO,gBAAgB,aAAa,OAAO,eAAe,YAAY,MAAM;AAC9E,sBAAgB,KAAK;AAAA,QACnB,MAAM;AAAA,QACN,aAAa;AAAA,QACb,QAAQ;AAAA,UACN,eAAe,OAAO,uBAAuB;AAAA,UAC7C,YAAY;AAAA,UACZ,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,wBAAwB,UAA+B,eAAwD;AAErH,UAAM,iBAAiB,EAAE,GAAG,SAAS;AAGrC,kBAAc,YAAY,QAAQ,CAAC,YAAY,MAAM;AACnD,YAAM,YAAY;AAClB,YAAM,YAAY,WAAW,mBAAmB,IAAI;AACpD,YAAM,YAAY,WAAW,mBAAmB,IAAI;AAEpD,qBAAe,YAAY,KAAK;AAAA,QAC9B,IAAI,sBAAsB,CAAC;AAAA,QAC3B,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAED,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,eAAe,eAAmC,iBAAyD;AAEjH,UAAM,WAAW,EAAE,GAAG,cAAc;AAGpC,QAAI,gBAAgB,oBAAoB,iBAAiB,cAAc,oBAAoB,gBAAgB;AACzG,eAAS,cAAc,gBAAgB;AACvC,eAAS,iBAAiB,gBAAgB;AAC1C,eAAS,uBAAuB,gBAAgB;AAChD,eAAS,2BAA2B,gBAAgB;AACpD,eAAS,cAAc,gBAAgB;AACvC,eAAS,aAAa,gBAAgB;AAAA,IACxC;AAGA,aAAS,cAAc,cAAc,aAAa,gBAAgB,cAAc;AAGhF,aAAS,YAAY,KAAK,IAAI,cAAc,WAAW,gBAAgB,SAAS;AAEhF,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKQ,yBAAyB,UAAkB,aAA2B;AAC5E,QAAI,CAAC,KAAK,mBAAmB,IAAI,QAAQ,GAAG;AAC1C,WAAK,mBAAmB,IAAI,UAAU,CAAC,CAAC;AAAA,IAC1C;AAEA,UAAM,UAAU,KAAK,mBAAmB,IAAI,QAAQ;AACpD,YAAQ,KAAK,WAAW;AAGxB,QAAI,QAAQ,SAAS,IAAI;AACvB,cAAQ,MAAM;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAyB;AAC7B,UAAM,KAAK,aAAa,QAAQ;AAChC,SAAK,mBAAmB,MAAM;AAAA,EAChC;AACF;;;AJjfO,IAAM,qBAAqB;AAAA,EAChC,KAAK;AAAA,EACL,MAAM;AAAA,EACN,mBAAmB;AAAA,EACnB,QAAQ;AAAA,EACR,MAAM;AACR;AAGO,IAAM,eAAe;AAAA,EAC1B,YAAY;AAAA,EACZ,gBAAgB;AAAA;AAAA,EAChB,YAAY;AAAA,EACZ,WAAW;AAAA;AAAA,EACX,kBAAkB;AAAA,EAClB,yBAAyB;AAC3B;AAEA,IAAM,gBAAgB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAO,kBAAQ;", "names": ["axios", "sum"]}