# 🚀 Metamorphic Flux Deployment Guide

This guide covers deploying the Metamorphic Flux MVP to production environments.

## 📋 Prerequisites

- Node.js 20+ and pnpm
- Supabase account and project
- Google AI API key
- Domain name (for production)
- Hosting platform account (Vercel, Netlify, Railway, etc.)

## 🔧 Environment Setup

### 1. Production Environment Variables

Copy `.env.production.template` to `.env.production` and fill in your values:

```bash
cp .env.production.template .env.production
```

Required variables:
- `NEXT_PUBLIC_SUPABASE_URL` - Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Your Supabase service role key
- `GOOGLE_AI_API_KEY` - Your Google AI API key
- `NEXT_PUBLIC_APP_URL` - Your production domain
- `DATABASE_URL` - Your production database URL

### 2. Supabase Setup

1. Create a new Supabase project
2. Run the database migrations:
   ```bash
   cd supabase
   npx supabase db push
   ```
3. Set up Row Level Security (RLS) policies
4. Configure authentication providers

## 🏗️ Build and Deploy

### Option 1: Automated Deployment Script

```bash
# Run the complete deployment process
./scripts/deploy.sh

# Or run individual steps
./scripts/deploy.sh deps    # Install dependencies
./scripts/deploy.sh check   # Type checking
./scripts/deploy.sh test    # Run tests
./scripts/deploy.sh build   # Build applications
./scripts/deploy.sh health  # Health checks
```

### Option 2: Manual Deployment

1. **Install Dependencies**
   ```bash
   pnpm install
   ```

2. **Type Check**
   ```bash
   cd apps/web && pnpm type-check
   cd ../agents && pnpm type-check
   ```

3. **Build Applications**
   ```bash
   cd apps/web && pnpm build
   cd ../agents && pnpm build
   ```

4. **Run Integration Tests**
   ```bash
   node scripts/integration-test.js
   ```

## 🌐 Platform-Specific Deployment

### Vercel (Recommended for Web App)

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy Web App**
   ```bash
   cd apps/web
   vercel --prod
   ```

3. **Configure Environment Variables**
   - Go to Vercel dashboard
   - Add all production environment variables
   - Redeploy

### Railway (Recommended for Agents Service)

1. **Install Railway CLI**
   ```bash
   npm i -g @railway/cli
   ```

2. **Deploy Agents Service**
   ```bash
   cd apps/agents
   railway login
   railway up
   ```

### Docker Deployment

1. **Build Production Images**
   ```bash
   # Build web app
   docker build --target web-runner -t metamorphic-flux-web .
   
   # Build agents service
   docker build --target agents-runner -t metamorphic-flux-agents .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

## 🔍 Health Monitoring

### Health Check Endpoints

- **Web App**: `https://your-domain.com/api/health`
- **Agents Service**: `https://your-agents-domain.com/health`

### Monitoring Setup

1. **Set up uptime monitoring** (UptimeRobot, Pingdom, etc.)
2. **Configure error tracking** (Sentry, LogRocket, etc.)
3. **Set up performance monitoring** (Vercel Analytics, etc.)

## 🔒 Security Checklist

- [ ] Environment variables are secure
- [ ] Database RLS policies are enabled
- [ ] API rate limiting is configured
- [ ] HTTPS is enabled
- [ ] CORS is properly configured
- [ ] Authentication is working
- [ ] Service role keys are protected

## 📊 Performance Optimization

### Web App Optimization

1. **Enable Vercel Analytics**
   ```bash
   pnpm add @vercel/analytics
   ```

2. **Configure Image Optimization**
   - Use Next.js Image component
   - Set up proper image domains

3. **Enable Caching**
   - Configure Redis for session storage
   - Set up proper cache headers

### Agents Service Optimization

1. **Enable Clustering**
   ```javascript
   // In production, use PM2 or cluster module
   const cluster = require('cluster');
   const numCPUs = require('os').cpus().length;
   ```

2. **Configure Rate Limiting**
   ```javascript
   app.use(rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100 // limit each IP to 100 requests per windowMs
   }));
   ```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript errors: `pnpm type-check`
   - Verify environment variables
   - Check dependency versions

2. **Database Connection Issues**
   - Verify DATABASE_URL format
   - Check Supabase project status
   - Ensure RLS policies allow access

3. **API Errors**
   - Check Google AI API quota
   - Verify API key format
   - Check network connectivity

### Debug Commands

```bash
# Check system health
curl https://your-domain.com/api/health

# View application logs
vercel logs your-deployment-url

# Test database connection
psql $DATABASE_URL -c "SELECT version();"

# Test agents service
curl https://your-agents-domain.com/health
```

## 📈 Scaling Considerations

### Horizontal Scaling

1. **Load Balancing**
   - Use Vercel's edge network
   - Configure Railway autoscaling

2. **Database Scaling**
   - Enable Supabase read replicas
   - Configure connection pooling

3. **Caching Strategy**
   - Implement Redis caching
   - Use CDN for static assets

### Monitoring and Alerts

1. **Set up alerts for**:
   - High error rates
   - Slow response times
   - Database connection issues
   - API quota limits

2. **Monitor metrics**:
   - Request volume
   - Response times
   - Error rates
   - Resource usage

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm install -g pnpm
      - run: pnpm install
      - run: ./scripts/deploy.sh
      - run: vercel --prod --token ${{ secrets.VERCEL_TOKEN }}
```

## 📞 Support

For deployment issues:
1. Check the health endpoints
2. Review application logs
3. Verify environment configuration
4. Run integration tests locally

## 🎯 Next Steps

After successful deployment:
1. Set up monitoring and alerts
2. Configure backup strategies
3. Plan scaling architecture
4. Set up staging environment
5. Implement CI/CD pipeline
