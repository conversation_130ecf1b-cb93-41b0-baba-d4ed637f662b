{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "eslint.config.*"]}, "test": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "jest.config.*", "vitest.config.*"]}, "test:e2e": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "playwright.config.*", "tests/**"]}, "type-check": {"dependsOn": ["^type-check"], "inputs": ["$TURBO_DEFAULT$", "tsconfig.json"]}, "clean": {"cache": false}}, "globalDependencies": ["**/.env*"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY", "GOOGLE_AI_API_KEY", "TEMPORAL_NAMESPACE", "TEMPORAL_ADDRESS"]}